"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f";
exports.ids = ["vendor-chunks/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs":
/*!***************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs ***!
  \***************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   CheckboxItem: () => (/* binding */ CheckboxItem2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuArrow: () => (/* binding */ DropdownMenuArrow),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuItemIndicator: () => (/* binding */ DropdownMenuItemIndicator),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger),\n/* harmony export */   Group: () => (/* binding */ Group2),\n/* harmony export */   Item: () => (/* binding */ Item2),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator2),\n/* harmony export */   Label: () => (/* binding */ Label2),\n/* harmony export */   Portal: () => (/* binding */ Portal2),\n/* harmony export */   RadioGroup: () => (/* binding */ RadioGroup2),\n/* harmony export */   RadioItem: () => (/* binding */ RadioItem2),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Separator: () => (/* binding */ Separator2),\n/* harmony export */   Sub: () => (/* binding */ Sub2),\n/* harmony export */   SubContent: () => (/* binding */ SubContent2),\n/* harmony export */   SubTrigger: () => (/* binding */ SubTrigger2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createDropdownMenuScope: () => (/* binding */ createDropdownMenuScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-menu */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-menu@2.1.15_fdc5c6ad9588e9f7f12c779229676662/node_modules/@radix-ui/react-menu/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,CheckboxItem,Content,DropdownMenu,DropdownMenuArrow,DropdownMenuCheckboxItem,DropdownMenuContent,DropdownMenuGroup,DropdownMenuItem,DropdownMenuItemIndicator,DropdownMenuLabel,DropdownMenuPortal,DropdownMenuRadioGroup,DropdownMenuRadioItem,DropdownMenuSeparator,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuTrigger,Group,Item,ItemIndicator,Label,Portal,RadioGroup,RadioItem,Root,Separator,Sub,SubContent,SubTrigger,Trigger,createDropdownMenuScope auto */ // src/dropdown-menu.tsx\n\n\n\n\n\n\n\n\n\n\nvar DROPDOWN_MENU_NAME = \"DropdownMenu\";\nvar [createDropdownMenuContext, createDropdownMenuScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(DROPDOWN_MENU_NAME, [\n    _radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope\n]);\nvar useMenuScope = (0,_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.createMenuScope)();\nvar [DropdownMenuProvider, useDropdownMenuContext] = createDropdownMenuContext(DROPDOWN_MENU_NAME);\nvar DropdownMenu = (props)=>{\n    const { __scopeDropdownMenu, children, dir, open: openProp, defaultOpen, onOpenChange, modal = true } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: DROPDOWN_MENU_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(DropdownMenuProvider, {\n        scope: __scopeDropdownMenu,\n        triggerId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        triggerRef,\n        contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n        open,\n        onOpenChange: setOpen,\n        onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n            setOpen\n        ]),\n        modal,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Root, {\n            ...menuScope,\n            open,\n            onOpenChange: setOpen,\n            dir,\n            modal,\n            children\n        })\n    });\n};\nDropdownMenu.displayName = DROPDOWN_MENU_NAME;\nvar TRIGGER_NAME = \"DropdownMenuTrigger\";\nvar DropdownMenuTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, disabled = false, ...triggerProps } = props;\n    const context = useDropdownMenuContext(TRIGGER_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...menuScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_6__.Primitive.button, {\n            type: \"button\",\n            id: context.triggerId,\n            \"aria-haspopup\": \"menu\",\n            \"aria-expanded\": context.open,\n            \"aria-controls\": context.open ? context.contentId : void 0,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            \"data-disabled\": disabled ? \"\" : void 0,\n            disabled,\n            ...triggerProps,\n            ref: (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_7__.composeRefs)(forwardedRef, context.triggerRef),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, (event)=>{\n                if (!disabled && event.button === 0 && event.ctrlKey === false) {\n                    context.onOpenToggle();\n                    if (!context.open) event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onKeyDown, (event)=>{\n                if (disabled) return;\n                if ([\n                    \"Enter\",\n                    \" \"\n                ].includes(event.key)) context.onOpenToggle();\n                if (event.key === \"ArrowDown\") context.onOpenChange(true);\n                if ([\n                    \"Enter\",\n                    \" \",\n                    \"ArrowDown\"\n                ].includes(event.key)) event.preventDefault();\n            })\n        })\n    });\n});\nDropdownMenuTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"DropdownMenuPortal\";\nvar DropdownMenuPortal = (props)=>{\n    const { __scopeDropdownMenu, ...portalProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        ...menuScope,\n        ...portalProps\n    });\n};\nDropdownMenuPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"DropdownMenuContent\";\nvar DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...contentProps } = props;\n    const context = useDropdownMenuContext(CONTENT_NAME, __scopeDropdownMenu);\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        id: context.contentId,\n        \"aria-labelledby\": context.triggerId,\n        ...menuScope,\n        ...contentProps,\n        ref: forwardedRef,\n        onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n            if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n            hasInteractedOutsideRef.current = false;\n            event.preventDefault();\n        }),\n        onInteractOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onInteractOutside, (event)=>{\n            const originalEvent = event.detail.originalEvent;\n            const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n            const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n            if (!context.modal || isRightClick) hasInteractedOutsideRef.current = true;\n        }),\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuContent.displayName = CONTENT_NAME;\nvar GROUP_NAME = \"DropdownMenuGroup\";\nvar DropdownMenuGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...groupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Group, {\n        ...menuScope,\n        ...groupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"DropdownMenuLabel\";\nvar DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...labelProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ...menuScope,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"DropdownMenuItem\";\nvar DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ...menuScope,\n        ...itemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItem.displayName = ITEM_NAME;\nvar CHECKBOX_ITEM_NAME = \"DropdownMenuCheckboxItem\";\nvar DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...checkboxItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ...menuScope,\n        ...checkboxItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuCheckboxItem.displayName = CHECKBOX_ITEM_NAME;\nvar RADIO_GROUP_NAME = \"DropdownMenuRadioGroup\";\nvar DropdownMenuRadioGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioGroupProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup, {\n        ...menuScope,\n        ...radioGroupProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioGroup.displayName = RADIO_GROUP_NAME;\nvar RADIO_ITEM_NAME = \"DropdownMenuRadioItem\";\nvar DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...radioItemProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ...menuScope,\n        ...radioItemProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuRadioItem.displayName = RADIO_ITEM_NAME;\nvar INDICATOR_NAME = \"DropdownMenuItemIndicator\";\nvar DropdownMenuItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...itemIndicatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n        ...menuScope,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuItemIndicator.displayName = INDICATOR_NAME;\nvar SEPARATOR_NAME = \"DropdownMenuSeparator\";\nvar DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...separatorProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ...menuScope,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"DropdownMenuArrow\";\nvar DropdownMenuArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...arrowProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...menuScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuArrow.displayName = ARROW_NAME;\nvar DropdownMenuSub = (props)=>{\n    const { __scopeDropdownMenu, children, open: openProp, onOpenChange, defaultOpen } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: \"DropdownMenuSub\"\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.Sub, {\n        ...menuScope,\n        open,\n        onOpenChange: setOpen,\n        children\n    });\n};\nvar SUB_TRIGGER_NAME = \"DropdownMenuSubTrigger\";\nvar DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subTriggerProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ...menuScope,\n        ...subTriggerProps,\n        ref: forwardedRef\n    });\n});\nDropdownMenuSubTrigger.displayName = SUB_TRIGGER_NAME;\nvar SUB_CONTENT_NAME = \"DropdownMenuSubContent\";\nvar DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeDropdownMenu, ...subContentProps } = props;\n    const menuScope = useMenuScope(__scopeDropdownMenu);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ...menuScope,\n        ...subContentProps,\n        ref: forwardedRef,\n        style: {\n            ...props.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-dropdown-menu-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-dropdown-menu-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-dropdown-menu-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-dropdown-menu-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-dropdown-menu-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nDropdownMenuSubContent.displayName = SUB_CONTENT_NAME;\nvar Root2 = DropdownMenu;\nvar Trigger = DropdownMenuTrigger;\nvar Portal2 = DropdownMenuPortal;\nvar Content2 = DropdownMenuContent;\nvar Group2 = DropdownMenuGroup;\nvar Label2 = DropdownMenuLabel;\nvar Item2 = DropdownMenuItem;\nvar CheckboxItem2 = DropdownMenuCheckboxItem;\nvar RadioGroup2 = DropdownMenuRadioGroup;\nvar RadioItem2 = DropdownMenuRadioItem;\nvar ItemIndicator2 = DropdownMenuItemIndicator;\nvar Separator2 = DropdownMenuSeparator;\nvar Arrow2 = DropdownMenuArrow;\nvar Sub2 = DropdownMenuSub;\nvar SubTrigger2 = DropdownMenuSubTrigger;\nvar SubContent2 = DropdownMenuSubContent;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-dropdown-me_8ec93851da28661231dcc925c4c21d7f/node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\n");

/***/ })

};
;