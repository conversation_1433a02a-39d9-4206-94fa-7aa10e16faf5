'use client'

import React, { useCallback, useMemo, useState, useEffect, useRef } from 'react'
import {
    CrewMembers_LogBookEntrySection,
    VESSEL_BRIEF_LIST,
} from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import { createColumns, DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { useBreakpoints } from '@/components/hooks/useBreakpoints'
import { useVesselIconData } from '@/app/lib/vessel-icon-helper'
import { VesselLocationDisplay } from '@/components/ui/vessel-location-display'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { ListHeader } from '@/components/ui'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { SealogsCogIcon } from '@/app/lib/icons'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType =
    | 'dateRange'
    | 'members'
    | 'vessels'
    | 'crewDuty'
    | 'reportMode'
interface IFilter {
    type: FilterType
    data: any
}

interface IReportItem {
    crewID: number
    crewName: string
    vesselID: number
    vesselName: string
    loginTime: Date
    logoutTime: Date
    totalLoggedMinutes: number
    loggedDuration: {
        hours: number
        minutes: number
    }
    dutyPerformedID: number
    primaryDuty: string
    workDetails?: string
}

// Helper function for generating crew member initials
const getCrewInitials = (crewName: string): string => {
    if (!crewName) return '??'
    const names = crewName.trim().split(' ')
    if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase()
    }
    const first = names[0]?.charAt(0)?.toUpperCase() || ''
    const last = names[names.length - 1]?.charAt(0)?.toUpperCase() || ''
    return `${first}${last}` || '??'
}

// Function to create columns for the crew seatime report
const createCrewSeatimeColumns = (
    bp: any,
    getVesselWithIcon: any,
    vessels: any[] = [],
) =>
    createColumns<IReportItem>([
        {
            accessorKey: 'title',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Crew member name" />
            ),
            cell: ({ row }: { row: any }) => {
                const item = row.original

                const crewContent = (
                    <div className="leading-tight truncate font-medium hover:text-curious-blue-400">
                        {item.crewName}
                    </div>
                )

                const actualVessel = vessels.find(
                    (vessel: any) => vessel.title === item.vesselName,
                )
                const vesselWithIcon = getVesselWithIcon(
                    actualVessel?.id || 0,
                    actualVessel,
                )

                return (
                    <div className="flex flex-col py-2.5 gap-2">
                        {/* Show card layout on xs devices */}
                        <div className="desktop:hidden inline-flex overflow-auto items-center gap-1.5">
                            <VesselLocationDisplay
                                vessel={vesselWithIcon}
                                displayText={false}
                                vesselId={actualVessel.id}
                            />
                            <div className="grid">
                                {crewContent}
                                <div className="flex flex-col text-sm text-muted-foreground">
                                    <span>{item.primaryDuty}</span>
                                </div>
                            </div>
                        </div>
                        {/* Show normal table layout on larger devices */}
                        <div className="hidden desktop:block">
                            <div className="flex items-center gap-2">
                                <Avatar size="sm">
                                    <AvatarFallback className="text-sm">
                                        {getCrewInitials(item.crewName)}
                                    </AvatarFallback>
                                </Avatar>
                                {crewContent}
                            </div>
                        </div>
                    </div>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.crewName || ''
                const valueB = rowB?.original?.crewName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'vesselName',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Vessel" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'desktop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original

                // Find the actual vessel by name from the vessels list
                const actualVessel = vessels.find(
                    (vessel: any) => vessel.title === item.vesselName,
                )

                if (actualVessel) {
                    // Use the actual vessel data with proper ID
                    const vesselWithIcon = getVesselWithIcon(
                        actualVessel.id,
                        actualVessel,
                    )
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            vesselId={actualVessel.id}
                            displayText={item.vesselName}
                        />
                    )
                } else {
                    // Fallback for vessels not found in the list
                    const vesselForIcon = {
                        id: 0,
                        title: item.vesselName,
                    }
                    const vesselWithIcon = getVesselWithIcon(0, vesselForIcon)
                    return (
                        <VesselLocationDisplay
                            vessel={vesselWithIcon}
                            displayText={item.vesselName}
                        />
                    )
                }
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.vesselName || ''
                const valueB = rowB?.original?.vesselName || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'primaryDuty',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Duty" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'laptop',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <span className="hover:text-curious-blue-400">
                        {item.primaryDuty}
                    </span>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.primaryDuty || ''
                const valueB = rowB?.original?.primaryDuty || ''
                return valueA.localeCompare(valueB)
            },
        },
        {
            accessorKey: 'loginTime',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Signed in" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'tablet-sm',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <span>{dayjs(item.loginTime).format('DD/M/YY HH:mm')}</span>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.loginTime || ''
                const valueB = rowB?.original?.loginTime || ''
                return dayjs(valueA).unix() - dayjs(valueB).unix()
            },
        },
        {
            accessorKey: 'logoutTime',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Signed out" />
            ),
            cellAlignment: 'left' as const,
            breakpoint: 'tablet-sm',
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <span>
                        {dayjs(item.logoutTime).format('DD/M/YY HH:mm')}
                    </span>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.logoutTime || ''
                const valueB = rowB?.original?.logoutTime || ''
                return dayjs(valueA).unix() - dayjs(valueB).unix()
            },
        },
        {
            accessorKey: 'loggedDuration',
            header: ({ column }: { column: any }) => (
                <DataTableSortHeader column={column} title="Time spent" />
            ),
            cellAlignment: 'right' as const,
            cellClassName: 'px-2.5',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <span>
                        {item.loggedDuration.hours != 0
                            ? `${item.loggedDuration.hours}h, `
                            : ''}
                        {item.loggedDuration.minutes}m
                    </span>
                )
            },
            sortingFn: (rowA: any, rowB: any) => {
                const valueA = rowA?.original?.totalLoggedMinutes || 0
                const valueB = rowB?.original?.totalLoggedMinutes || 0
                return valueA - valueB
            },
        },
    ])

// Actions component for crew seatime report
interface CrewSeatimeReportActionsProps {
    onDownloadCsv?: () => void
    onDownloadPdf?: () => void
}

const CrewSeatimeReportActions = ({
    onDownloadCsv,
    onDownloadPdf,
}: CrewSeatimeReportActionsProps) => {
    const { isMobile } = useSidebar()
    const router = useRouter()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <div className="text-input flex flex-col items-center justify-center py-[9px]">
                    <DropdownMenuItem
                        variant="backButton"
                        onClick={() => router.push('/reporting')}>
                        Back
                    </DropdownMenuItem>
                    {onDownloadPdf && (
                        <DropdownMenuItem
                            className="px-[26px]"
                            onClick={onDownloadPdf}>
                            Download PDF
                        </DropdownMenuItem>
                    )}
                    {onDownloadCsv && (
                        <DropdownMenuItem
                            className="px-[26px]"
                            onClick={onDownloadCsv}>
                            Download CSV
                        </DropdownMenuItem>
                    )}
                </div>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

export default function NewCrewSeatimeReport() {
    const router = useRouter()
    const bp = useBreakpoints()
    const { getVesselWithIcon } = useVesselIconData()
    const [selectedCrews, setSelectedCrews] = useState<IDropdownItem[]>([])
    const [selectedDuties, setSelectedDuties] = useState<IDropdownItem[]>([])
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [reportMode, setReportMode] = useState<'detailed' | 'summary'>(
        'detailed',
    )
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: null,
        endDate: null,
    })
    const [vessels, setVessels] = useState<any[]>([])

    // Optimize: Add debouncing to prevent excessive API calls
    const debounceTimeoutRef = useRef<NodeJS.Timeout | null>(null)

    // Load vessels for vessel lookup by name
    const [queryVessels] = useLazyQuery(VESSEL_BRIEF_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (queryVesselResponse: any) => {
            if (queryVesselResponse.readVessels.nodes) {
                const activeVessels =
                    queryVesselResponse.readVessels.nodes.filter(
                        (vessel: any) => !vessel.archived,
                    )
                setVessels(activeVessels)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })

    // Load vessels on component mount
    useEffect(() => {
        queryVessels({
            variables: {
                limit: 200,
                offset: 0,
            },
        })
    }, [queryVessels])

    // Create columns with access to bp, vessel icon data, and vessels list
    const columns = createCrewSeatimeColumns(bp, getVesselWithIcon, vessels)

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'dateRange':
                setDateRange(data)
                break

            case 'members':
                setSelectedCrews(data)
                break

            case 'vessels':
                // Handle both single vessel and multi-vessel selection
                if (Array.isArray(data)) {
                    setSelectedVessels(data)
                } else if (data) {
                    // Single vessel selection - convert to array for consistency
                    setSelectedVessels([data])
                } else {
                    // Clear selection
                    setSelectedVessels([])
                }
                break

            case 'crewDuty':
                setSelectedDuties(data)
                break

            case 'reportMode':
                setReportMode(data)
                break

            default:
                break
        }
    }

    const [getReportData, { called, loading, data }] = useLazyQuery(
        CrewMembers_LogBookEntrySection,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: () => {
                // Query completed successfully
            },
            onError: (error: any) => {
                console.error('❌ queryLogBookEntrySections error', error)
            },
        },
    )

    const generateReportImmediate = useCallback(() => {
        const filter: any = {}

        // Optimize: Only include filters that have values to reduce query complexity
        if (selectedCrews && selectedCrews.length > 0) {
            filter['crewMemberID'] = {
                in: selectedCrews.map((crew) => crew.value),
            }
        }

        if (selectedDuties && selectedDuties.length > 0) {
            filter['dutyPerformedID'] = {
                in: selectedDuties.map((duty) => duty.value),
            }
        }

        // Optimize: Build logBookEntry filter only when needed
        const logBookFilter: any = {}

        if (selectedVessels && selectedVessels.length > 0) {
            logBookFilter.vehicleID = {
                in: selectedVessels.map((vessel) => vessel.value),
            }
        }

        if (
            dateRange &&
            dateRange.startDate !== null &&
            dateRange.endDate !== null
        ) {
            logBookFilter.startDate = {
                gte: dateRange.startDate,
                lte: dateRange.endDate,
            }
        }

        // Only add logBookEntry filter if we have vessel or date filters
        if (Object.keys(logBookFilter).length > 0) {
            filter['logBookEntry'] = logBookFilter
        }

        // Optimize: Add server-side filter for punchOut to reduce data transfer
        filter['punchOut'] = {
            ne: null,
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }, [
        selectedCrews,
        selectedDuties,
        selectedVessels,
        dateRange,
        reportMode,
        getReportData,
    ])

    // Optimize: Debounced version for filter changes
    const generateReport = useCallback(() => {
        if (debounceTimeoutRef.current) {
            clearTimeout(debounceTimeoutRef.current)
        }

        debounceTimeoutRef.current = setTimeout(() => {
            generateReportImmediate()
        }, 300) // 300ms debounce
    }, [generateReportImmediate])

    const reportData = useMemo<IReportItem[]>(() => {
        const rawData = data?.readCrewMembers_LogBookEntrySections?.nodes ?? []

        // Optimize: Since we now filter punchOut on server-side, this client-side filter should be redundant
        // But keeping it as a safety net for now
        const filteredData = rawData.filter(
            (item: any) => item.punchOut !== null,
        )

        // Optimize: Use more efficient data transformation
        const reportItems: IReportItem[] = filteredData.map((item: any) => {
            // Pre-calculate time values to avoid repeated dayjs calls
            const punchInTime = dayjs(item.punchIn)
            const punchOutTime = dayjs(item.punchOut)
            const loggedDurationMinutes = punchOutTime.diff(
                punchInTime,
                'minutes',
            )

            // Optimize: Use bitwise operations for better performance
            const hours =
                loggedDurationMinutes >= 60
                    ? (loggedDurationMinutes / 60) | 0
                    : 0
            const minutes = loggedDurationMinutes % 60

            // Optimize: Create object directly without intermediate variables
            return {
                crewID: +item.crewMember.id,
                crewName: `${item.crewMember.firstName} ${item.crewMember.surname}`,
                totalLoggedMinutes: loggedDurationMinutes,
                loggedDuration: { hours, minutes },
                loginTime: new Date(item.punchIn),
                logoutTime: new Date(item.punchOut),
                dutyPerformedID: +item.dutyPerformedID,
                primaryDuty: item.dutyPerformed.title,
                vesselID: +item.logBookEntry.vehicleID,
                vesselName: item.logBookEntry.vehicle.title,
                workDetails: item.workDetails,
            } as IReportItem
        })

        if (reportMode === 'detailed') {
            return reportItems
        }

        // Optimize: Create summary report using Map for better performance
        const summaryMap = new Map<string, IReportItem>()

        // Group items by crew, duty, and vessel combination
        reportItems.forEach((item) => {
            const key = `${item.crewID}|${item.dutyPerformedID}|${item.vesselID}`

            if (summaryMap.has(key)) {
                // Add to existing summary
                const existing = summaryMap.get(key)!
                const newTotalMinutes =
                    existing.totalLoggedMinutes + item.totalLoggedMinutes
                const hours =
                    newTotalMinutes >= 60 ? (newTotalMinutes / 60) | 0 : 0
                const minutes = newTotalMinutes % 60

                summaryMap.set(key, {
                    ...existing,
                    totalLoggedMinutes: newTotalMinutes,
                    loggedDuration: { hours, minutes },
                })
            } else {
                // Create new summary entry
                summaryMap.set(key, {
                    ...item,
                    loginTime:
                        (dateRange && dateRange.startDate) || item.loginTime,
                    logoutTime:
                        (dateRange && dateRange.endDate) || item.logoutTime,
                })
            }
        })

        const summarizedReportItems = Array.from(summaryMap.values())
        return summarizedReportItems
    }, [called, data, loading, reportMode])

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries = []

        csvEntries.push([
            'crew',
            'vessel',
            'duty',
            'signed in',
            'signed out',
            'time spent',
        ])
        reportData.forEach((item) => {
            csvEntries.push([
                item.crewName,
                item.vesselName,
                item.primaryDuty,
                item.loginTime.toISOString(),
                item.logoutTime.toISOString(),
                `${item.loggedDuration.hours > 0 ? `${item.loggedDuration.hours}h ` : ''}${item.loggedDuration.minutes}m`,
            ])
        })

        exportCsv(csvEntries)
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const headers: any = [
            ['Crew', 'Vessel', 'Duty', 'Signed in', 'Signed out', 'Time spent'],
        ]

        const data: any = reportData.map(function (item) {
            return [
                item.crewName + '',
                item.vesselName + '',
                item.primaryDuty + '',
                dayjs(item.loginTime).format('DD/MM/YY HH:mm') + '',
                dayjs(item.logoutTime).format('DD/MM/YY HH:mm') + '',
                `${item.loggedDuration.hours > 0 ? `${item.loggedDuration.hours}h ` : ''}${item.loggedDuration.minutes}m`,
            ]
        })

        exportPdfTable({
            headers,
            body: data,
        })
    }

    return (
        <>
            <ListHeader
                title="Crew Seatime Report"
                actions={
                    <CrewSeatimeReportActions
                        onDownloadCsv={downloadCsv}
                        onDownloadPdf={downloadPdf}
                    />
                }
            />
            <div className="mt-16">
                <DataTable
                    columns={columns}
                    data={reportData}
                    isLoading={called && loading}
                    onChange={handleFilterOnChange}
                    onFilterClick={generateReport}
                    noDataText="No crew seatime data found, try clicking generate report to view results"
                    showToolbar={true}
                />
            </div>
        </>
    )
}
