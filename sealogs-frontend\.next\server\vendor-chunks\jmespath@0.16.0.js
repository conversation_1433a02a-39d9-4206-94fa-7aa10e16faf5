/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jmespath@0.16.0";
exports.ids = ["vendor-chunks/jmespath@0.16.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/jmespath@0.16.0/node_modules/jmespath/jmespath.js":
/*!******************************************************************************!*\
  !*** ./node_modules/.pnpm/jmespath@0.16.0/node_modules/jmespath/jmespath.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("(function(exports) {\n  \"use strict\";\n\n  function isArray(obj) {\n    if (obj !== null) {\n      return Object.prototype.toString.call(obj) === \"[object Array]\";\n    } else {\n      return false;\n    }\n  }\n\n  function isObject(obj) {\n    if (obj !== null) {\n      return Object.prototype.toString.call(obj) === \"[object Object]\";\n    } else {\n      return false;\n    }\n  }\n\n  function strictDeepEqual(first, second) {\n    // Check the scalar case first.\n    if (first === second) {\n      return true;\n    }\n\n    // Check if they are the same type.\n    var firstType = Object.prototype.toString.call(first);\n    if (firstType !== Object.prototype.toString.call(second)) {\n      return false;\n    }\n    // We know that first and second have the same type so we can just check the\n    // first type from now on.\n    if (isArray(first) === true) {\n      // Short circuit if they're not the same length;\n      if (first.length !== second.length) {\n        return false;\n      }\n      for (var i = 0; i < first.length; i++) {\n        if (strictDeepEqual(first[i], second[i]) === false) {\n          return false;\n        }\n      }\n      return true;\n    }\n    if (isObject(first) === true) {\n      // An object is equal if it has the same key/value pairs.\n      var keysSeen = {};\n      for (var key in first) {\n        if (hasOwnProperty.call(first, key)) {\n          if (strictDeepEqual(first[key], second[key]) === false) {\n            return false;\n          }\n          keysSeen[key] = true;\n        }\n      }\n      // Now check that there aren't any keys in second that weren't\n      // in first.\n      for (var key2 in second) {\n        if (hasOwnProperty.call(second, key2)) {\n          if (keysSeen[key2] !== true) {\n            return false;\n          }\n        }\n      }\n      return true;\n    }\n    return false;\n  }\n\n  function isFalse(obj) {\n    // From the spec:\n    // A false value corresponds to the following values:\n    // Empty list\n    // Empty object\n    // Empty string\n    // False boolean\n    // null value\n\n    // First check the scalar values.\n    if (obj === \"\" || obj === false || obj === null) {\n        return true;\n    } else if (isArray(obj) && obj.length === 0) {\n        // Check for an empty array.\n        return true;\n    } else if (isObject(obj)) {\n        // Check for an empty object.\n        for (var key in obj) {\n            // If there are any keys, then\n            // the object is not empty so the object\n            // is not false.\n            if (obj.hasOwnProperty(key)) {\n              return false;\n            }\n        }\n        return true;\n    } else {\n        return false;\n    }\n  }\n\n  function objValues(obj) {\n    var keys = Object.keys(obj);\n    var values = [];\n    for (var i = 0; i < keys.length; i++) {\n      values.push(obj[keys[i]]);\n    }\n    return values;\n  }\n\n  function merge(a, b) {\n      var merged = {};\n      for (var key in a) {\n          merged[key] = a[key];\n      }\n      for (var key2 in b) {\n          merged[key2] = b[key2];\n      }\n      return merged;\n  }\n\n  var trimLeft;\n  if (typeof String.prototype.trimLeft === \"function\") {\n    trimLeft = function(str) {\n      return str.trimLeft();\n    };\n  } else {\n    trimLeft = function(str) {\n      return str.match(/^\\s*(.*)/)[1];\n    };\n  }\n\n  // Type constants used to define functions.\n  var TYPE_NUMBER = 0;\n  var TYPE_ANY = 1;\n  var TYPE_STRING = 2;\n  var TYPE_ARRAY = 3;\n  var TYPE_OBJECT = 4;\n  var TYPE_BOOLEAN = 5;\n  var TYPE_EXPREF = 6;\n  var TYPE_NULL = 7;\n  var TYPE_ARRAY_NUMBER = 8;\n  var TYPE_ARRAY_STRING = 9;\n  var TYPE_NAME_TABLE = {\n    0: 'number',\n    1: 'any',\n    2: 'string',\n    3: 'array',\n    4: 'object',\n    5: 'boolean',\n    6: 'expression',\n    7: 'null',\n    8: 'Array<number>',\n    9: 'Array<string>'\n  };\n\n  var TOK_EOF = \"EOF\";\n  var TOK_UNQUOTEDIDENTIFIER = \"UnquotedIdentifier\";\n  var TOK_QUOTEDIDENTIFIER = \"QuotedIdentifier\";\n  var TOK_RBRACKET = \"Rbracket\";\n  var TOK_RPAREN = \"Rparen\";\n  var TOK_COMMA = \"Comma\";\n  var TOK_COLON = \"Colon\";\n  var TOK_RBRACE = \"Rbrace\";\n  var TOK_NUMBER = \"Number\";\n  var TOK_CURRENT = \"Current\";\n  var TOK_EXPREF = \"Expref\";\n  var TOK_PIPE = \"Pipe\";\n  var TOK_OR = \"Or\";\n  var TOK_AND = \"And\";\n  var TOK_EQ = \"EQ\";\n  var TOK_GT = \"GT\";\n  var TOK_LT = \"LT\";\n  var TOK_GTE = \"GTE\";\n  var TOK_LTE = \"LTE\";\n  var TOK_NE = \"NE\";\n  var TOK_FLATTEN = \"Flatten\";\n  var TOK_STAR = \"Star\";\n  var TOK_FILTER = \"Filter\";\n  var TOK_DOT = \"Dot\";\n  var TOK_NOT = \"Not\";\n  var TOK_LBRACE = \"Lbrace\";\n  var TOK_LBRACKET = \"Lbracket\";\n  var TOK_LPAREN= \"Lparen\";\n  var TOK_LITERAL= \"Literal\";\n\n  // The \"&\", \"[\", \"<\", \">\" tokens\n  // are not in basicToken because\n  // there are two token variants\n  // (\"&&\", \"[?\", \"<=\", \">=\").  This is specially handled\n  // below.\n\n  var basicTokens = {\n    \".\": TOK_DOT,\n    \"*\": TOK_STAR,\n    \",\": TOK_COMMA,\n    \":\": TOK_COLON,\n    \"{\": TOK_LBRACE,\n    \"}\": TOK_RBRACE,\n    \"]\": TOK_RBRACKET,\n    \"(\": TOK_LPAREN,\n    \")\": TOK_RPAREN,\n    \"@\": TOK_CURRENT\n  };\n\n  var operatorStartToken = {\n      \"<\": true,\n      \">\": true,\n      \"=\": true,\n      \"!\": true\n  };\n\n  var skipChars = {\n      \" \": true,\n      \"\\t\": true,\n      \"\\n\": true\n  };\n\n\n  function isAlpha(ch) {\n      return (ch >= \"a\" && ch <= \"z\") ||\n             (ch >= \"A\" && ch <= \"Z\") ||\n             ch === \"_\";\n  }\n\n  function isNum(ch) {\n      return (ch >= \"0\" && ch <= \"9\") ||\n             ch === \"-\";\n  }\n  function isAlphaNum(ch) {\n      return (ch >= \"a\" && ch <= \"z\") ||\n             (ch >= \"A\" && ch <= \"Z\") ||\n             (ch >= \"0\" && ch <= \"9\") ||\n             ch === \"_\";\n  }\n\n  function Lexer() {\n  }\n  Lexer.prototype = {\n      tokenize: function(stream) {\n          var tokens = [];\n          this._current = 0;\n          var start;\n          var identifier;\n          var token;\n          while (this._current < stream.length) {\n              if (isAlpha(stream[this._current])) {\n                  start = this._current;\n                  identifier = this._consumeUnquotedIdentifier(stream);\n                  tokens.push({type: TOK_UNQUOTEDIDENTIFIER,\n                               value: identifier,\n                               start: start});\n              } else if (basicTokens[stream[this._current]] !== undefined) {\n                  tokens.push({type: basicTokens[stream[this._current]],\n                              value: stream[this._current],\n                              start: this._current});\n                  this._current++;\n              } else if (isNum(stream[this._current])) {\n                  token = this._consumeNumber(stream);\n                  tokens.push(token);\n              } else if (stream[this._current] === \"[\") {\n                  // No need to increment this._current.  This happens\n                  // in _consumeLBracket\n                  token = this._consumeLBracket(stream);\n                  tokens.push(token);\n              } else if (stream[this._current] === \"\\\"\") {\n                  start = this._current;\n                  identifier = this._consumeQuotedIdentifier(stream);\n                  tokens.push({type: TOK_QUOTEDIDENTIFIER,\n                               value: identifier,\n                               start: start});\n              } else if (stream[this._current] === \"'\") {\n                  start = this._current;\n                  identifier = this._consumeRawStringLiteral(stream);\n                  tokens.push({type: TOK_LITERAL,\n                               value: identifier,\n                               start: start});\n              } else if (stream[this._current] === \"`\") {\n                  start = this._current;\n                  var literal = this._consumeLiteral(stream);\n                  tokens.push({type: TOK_LITERAL,\n                               value: literal,\n                               start: start});\n              } else if (operatorStartToken[stream[this._current]] !== undefined) {\n                  tokens.push(this._consumeOperator(stream));\n              } else if (skipChars[stream[this._current]] !== undefined) {\n                  // Ignore whitespace.\n                  this._current++;\n              } else if (stream[this._current] === \"&\") {\n                  start = this._current;\n                  this._current++;\n                  if (stream[this._current] === \"&\") {\n                      this._current++;\n                      tokens.push({type: TOK_AND, value: \"&&\", start: start});\n                  } else {\n                      tokens.push({type: TOK_EXPREF, value: \"&\", start: start});\n                  }\n              } else if (stream[this._current] === \"|\") {\n                  start = this._current;\n                  this._current++;\n                  if (stream[this._current] === \"|\") {\n                      this._current++;\n                      tokens.push({type: TOK_OR, value: \"||\", start: start});\n                  } else {\n                      tokens.push({type: TOK_PIPE, value: \"|\", start: start});\n                  }\n              } else {\n                  var error = new Error(\"Unknown character:\" + stream[this._current]);\n                  error.name = \"LexerError\";\n                  throw error;\n              }\n          }\n          return tokens;\n      },\n\n      _consumeUnquotedIdentifier: function(stream) {\n          var start = this._current;\n          this._current++;\n          while (this._current < stream.length && isAlphaNum(stream[this._current])) {\n              this._current++;\n          }\n          return stream.slice(start, this._current);\n      },\n\n      _consumeQuotedIdentifier: function(stream) {\n          var start = this._current;\n          this._current++;\n          var maxLength = stream.length;\n          while (stream[this._current] !== \"\\\"\" && this._current < maxLength) {\n              // You can escape a double quote and you can escape an escape.\n              var current = this._current;\n              if (stream[current] === \"\\\\\" && (stream[current + 1] === \"\\\\\" ||\n                                               stream[current + 1] === \"\\\"\")) {\n                  current += 2;\n              } else {\n                  current++;\n              }\n              this._current = current;\n          }\n          this._current++;\n          return JSON.parse(stream.slice(start, this._current));\n      },\n\n      _consumeRawStringLiteral: function(stream) {\n          var start = this._current;\n          this._current++;\n          var maxLength = stream.length;\n          while (stream[this._current] !== \"'\" && this._current < maxLength) {\n              // You can escape a single quote and you can escape an escape.\n              var current = this._current;\n              if (stream[current] === \"\\\\\" && (stream[current + 1] === \"\\\\\" ||\n                                               stream[current + 1] === \"'\")) {\n                  current += 2;\n              } else {\n                  current++;\n              }\n              this._current = current;\n          }\n          this._current++;\n          var literal = stream.slice(start + 1, this._current - 1);\n          return literal.replace(\"\\\\'\", \"'\");\n      },\n\n      _consumeNumber: function(stream) {\n          var start = this._current;\n          this._current++;\n          var maxLength = stream.length;\n          while (isNum(stream[this._current]) && this._current < maxLength) {\n              this._current++;\n          }\n          var value = parseInt(stream.slice(start, this._current));\n          return {type: TOK_NUMBER, value: value, start: start};\n      },\n\n      _consumeLBracket: function(stream) {\n          var start = this._current;\n          this._current++;\n          if (stream[this._current] === \"?\") {\n              this._current++;\n              return {type: TOK_FILTER, value: \"[?\", start: start};\n          } else if (stream[this._current] === \"]\") {\n              this._current++;\n              return {type: TOK_FLATTEN, value: \"[]\", start: start};\n          } else {\n              return {type: TOK_LBRACKET, value: \"[\", start: start};\n          }\n      },\n\n      _consumeOperator: function(stream) {\n          var start = this._current;\n          var startingChar = stream[start];\n          this._current++;\n          if (startingChar === \"!\") {\n              if (stream[this._current] === \"=\") {\n                  this._current++;\n                  return {type: TOK_NE, value: \"!=\", start: start};\n              } else {\n                return {type: TOK_NOT, value: \"!\", start: start};\n              }\n          } else if (startingChar === \"<\") {\n              if (stream[this._current] === \"=\") {\n                  this._current++;\n                  return {type: TOK_LTE, value: \"<=\", start: start};\n              } else {\n                  return {type: TOK_LT, value: \"<\", start: start};\n              }\n          } else if (startingChar === \">\") {\n              if (stream[this._current] === \"=\") {\n                  this._current++;\n                  return {type: TOK_GTE, value: \">=\", start: start};\n              } else {\n                  return {type: TOK_GT, value: \">\", start: start};\n              }\n          } else if (startingChar === \"=\") {\n              if (stream[this._current] === \"=\") {\n                  this._current++;\n                  return {type: TOK_EQ, value: \"==\", start: start};\n              }\n          }\n      },\n\n      _consumeLiteral: function(stream) {\n          this._current++;\n          var start = this._current;\n          var maxLength = stream.length;\n          var literal;\n          while(stream[this._current] !== \"`\" && this._current < maxLength) {\n              // You can escape a literal char or you can escape the escape.\n              var current = this._current;\n              if (stream[current] === \"\\\\\" && (stream[current + 1] === \"\\\\\" ||\n                                               stream[current + 1] === \"`\")) {\n                  current += 2;\n              } else {\n                  current++;\n              }\n              this._current = current;\n          }\n          var literalString = trimLeft(stream.slice(start, this._current));\n          literalString = literalString.replace(\"\\\\`\", \"`\");\n          if (this._looksLikeJSON(literalString)) {\n              literal = JSON.parse(literalString);\n          } else {\n              // Try to JSON parse it as \"<literal>\"\n              literal = JSON.parse(\"\\\"\" + literalString + \"\\\"\");\n          }\n          // +1 gets us to the ending \"`\", +1 to move on to the next char.\n          this._current++;\n          return literal;\n      },\n\n      _looksLikeJSON: function(literalString) {\n          var startingChars = \"[{\\\"\";\n          var jsonLiterals = [\"true\", \"false\", \"null\"];\n          var numberLooking = \"-0123456789\";\n\n          if (literalString === \"\") {\n              return false;\n          } else if (startingChars.indexOf(literalString[0]) >= 0) {\n              return true;\n          } else if (jsonLiterals.indexOf(literalString) >= 0) {\n              return true;\n          } else if (numberLooking.indexOf(literalString[0]) >= 0) {\n              try {\n                  JSON.parse(literalString);\n                  return true;\n              } catch (ex) {\n                  return false;\n              }\n          } else {\n              return false;\n          }\n      }\n  };\n\n      var bindingPower = {};\n      bindingPower[TOK_EOF] = 0;\n      bindingPower[TOK_UNQUOTEDIDENTIFIER] = 0;\n      bindingPower[TOK_QUOTEDIDENTIFIER] = 0;\n      bindingPower[TOK_RBRACKET] = 0;\n      bindingPower[TOK_RPAREN] = 0;\n      bindingPower[TOK_COMMA] = 0;\n      bindingPower[TOK_RBRACE] = 0;\n      bindingPower[TOK_NUMBER] = 0;\n      bindingPower[TOK_CURRENT] = 0;\n      bindingPower[TOK_EXPREF] = 0;\n      bindingPower[TOK_PIPE] = 1;\n      bindingPower[TOK_OR] = 2;\n      bindingPower[TOK_AND] = 3;\n      bindingPower[TOK_EQ] = 5;\n      bindingPower[TOK_GT] = 5;\n      bindingPower[TOK_LT] = 5;\n      bindingPower[TOK_GTE] = 5;\n      bindingPower[TOK_LTE] = 5;\n      bindingPower[TOK_NE] = 5;\n      bindingPower[TOK_FLATTEN] = 9;\n      bindingPower[TOK_STAR] = 20;\n      bindingPower[TOK_FILTER] = 21;\n      bindingPower[TOK_DOT] = 40;\n      bindingPower[TOK_NOT] = 45;\n      bindingPower[TOK_LBRACE] = 50;\n      bindingPower[TOK_LBRACKET] = 55;\n      bindingPower[TOK_LPAREN] = 60;\n\n  function Parser() {\n  }\n\n  Parser.prototype = {\n      parse: function(expression) {\n          this._loadTokens(expression);\n          this.index = 0;\n          var ast = this.expression(0);\n          if (this._lookahead(0) !== TOK_EOF) {\n              var t = this._lookaheadToken(0);\n              var error = new Error(\n                  \"Unexpected token type: \" + t.type + \", value: \" + t.value);\n              error.name = \"ParserError\";\n              throw error;\n          }\n          return ast;\n      },\n\n      _loadTokens: function(expression) {\n          var lexer = new Lexer();\n          var tokens = lexer.tokenize(expression);\n          tokens.push({type: TOK_EOF, value: \"\", start: expression.length});\n          this.tokens = tokens;\n      },\n\n      expression: function(rbp) {\n          var leftToken = this._lookaheadToken(0);\n          this._advance();\n          var left = this.nud(leftToken);\n          var currentToken = this._lookahead(0);\n          while (rbp < bindingPower[currentToken]) {\n              this._advance();\n              left = this.led(currentToken, left);\n              currentToken = this._lookahead(0);\n          }\n          return left;\n      },\n\n      _lookahead: function(number) {\n          return this.tokens[this.index + number].type;\n      },\n\n      _lookaheadToken: function(number) {\n          return this.tokens[this.index + number];\n      },\n\n      _advance: function() {\n          this.index++;\n      },\n\n      nud: function(token) {\n        var left;\n        var right;\n        var expression;\n        switch (token.type) {\n          case TOK_LITERAL:\n            return {type: \"Literal\", value: token.value};\n          case TOK_UNQUOTEDIDENTIFIER:\n            return {type: \"Field\", name: token.value};\n          case TOK_QUOTEDIDENTIFIER:\n            var node = {type: \"Field\", name: token.value};\n            if (this._lookahead(0) === TOK_LPAREN) {\n                throw new Error(\"Quoted identifier not allowed for function names.\");\n            }\n            return node;\n          case TOK_NOT:\n            right = this.expression(bindingPower.Not);\n            return {type: \"NotExpression\", children: [right]};\n          case TOK_STAR:\n            left = {type: \"Identity\"};\n            right = null;\n            if (this._lookahead(0) === TOK_RBRACKET) {\n                // This can happen in a multiselect,\n                // [a, b, *]\n                right = {type: \"Identity\"};\n            } else {\n                right = this._parseProjectionRHS(bindingPower.Star);\n            }\n            return {type: \"ValueProjection\", children: [left, right]};\n          case TOK_FILTER:\n            return this.led(token.type, {type: \"Identity\"});\n          case TOK_LBRACE:\n            return this._parseMultiselectHash();\n          case TOK_FLATTEN:\n            left = {type: TOK_FLATTEN, children: [{type: \"Identity\"}]};\n            right = this._parseProjectionRHS(bindingPower.Flatten);\n            return {type: \"Projection\", children: [left, right]};\n          case TOK_LBRACKET:\n            if (this._lookahead(0) === TOK_NUMBER || this._lookahead(0) === TOK_COLON) {\n                right = this._parseIndexExpression();\n                return this._projectIfSlice({type: \"Identity\"}, right);\n            } else if (this._lookahead(0) === TOK_STAR &&\n                       this._lookahead(1) === TOK_RBRACKET) {\n                this._advance();\n                this._advance();\n                right = this._parseProjectionRHS(bindingPower.Star);\n                return {type: \"Projection\",\n                        children: [{type: \"Identity\"}, right]};\n            }\n            return this._parseMultiselectList();\n          case TOK_CURRENT:\n            return {type: TOK_CURRENT};\n          case TOK_EXPREF:\n            expression = this.expression(bindingPower.Expref);\n            return {type: \"ExpressionReference\", children: [expression]};\n          case TOK_LPAREN:\n            var args = [];\n            while (this._lookahead(0) !== TOK_RPAREN) {\n              if (this._lookahead(0) === TOK_CURRENT) {\n                expression = {type: TOK_CURRENT};\n                this._advance();\n              } else {\n                expression = this.expression(0);\n              }\n              args.push(expression);\n            }\n            this._match(TOK_RPAREN);\n            return args[0];\n          default:\n            this._errorToken(token);\n        }\n      },\n\n      led: function(tokenName, left) {\n        var right;\n        switch(tokenName) {\n          case TOK_DOT:\n            var rbp = bindingPower.Dot;\n            if (this._lookahead(0) !== TOK_STAR) {\n                right = this._parseDotRHS(rbp);\n                return {type: \"Subexpression\", children: [left, right]};\n            }\n            // Creating a projection.\n            this._advance();\n            right = this._parseProjectionRHS(rbp);\n            return {type: \"ValueProjection\", children: [left, right]};\n          case TOK_PIPE:\n            right = this.expression(bindingPower.Pipe);\n            return {type: TOK_PIPE, children: [left, right]};\n          case TOK_OR:\n            right = this.expression(bindingPower.Or);\n            return {type: \"OrExpression\", children: [left, right]};\n          case TOK_AND:\n            right = this.expression(bindingPower.And);\n            return {type: \"AndExpression\", children: [left, right]};\n          case TOK_LPAREN:\n            var name = left.name;\n            var args = [];\n            var expression, node;\n            while (this._lookahead(0) !== TOK_RPAREN) {\n              if (this._lookahead(0) === TOK_CURRENT) {\n                expression = {type: TOK_CURRENT};\n                this._advance();\n              } else {\n                expression = this.expression(0);\n              }\n              if (this._lookahead(0) === TOK_COMMA) {\n                this._match(TOK_COMMA);\n              }\n              args.push(expression);\n            }\n            this._match(TOK_RPAREN);\n            node = {type: \"Function\", name: name, children: args};\n            return node;\n          case TOK_FILTER:\n            var condition = this.expression(0);\n            this._match(TOK_RBRACKET);\n            if (this._lookahead(0) === TOK_FLATTEN) {\n              right = {type: \"Identity\"};\n            } else {\n              right = this._parseProjectionRHS(bindingPower.Filter);\n            }\n            return {type: \"FilterProjection\", children: [left, right, condition]};\n          case TOK_FLATTEN:\n            var leftNode = {type: TOK_FLATTEN, children: [left]};\n            var rightNode = this._parseProjectionRHS(bindingPower.Flatten);\n            return {type: \"Projection\", children: [leftNode, rightNode]};\n          case TOK_EQ:\n          case TOK_NE:\n          case TOK_GT:\n          case TOK_GTE:\n          case TOK_LT:\n          case TOK_LTE:\n            return this._parseComparator(left, tokenName);\n          case TOK_LBRACKET:\n            var token = this._lookaheadToken(0);\n            if (token.type === TOK_NUMBER || token.type === TOK_COLON) {\n                right = this._parseIndexExpression();\n                return this._projectIfSlice(left, right);\n            }\n            this._match(TOK_STAR);\n            this._match(TOK_RBRACKET);\n            right = this._parseProjectionRHS(bindingPower.Star);\n            return {type: \"Projection\", children: [left, right]};\n          default:\n            this._errorToken(this._lookaheadToken(0));\n        }\n      },\n\n      _match: function(tokenType) {\n          if (this._lookahead(0) === tokenType) {\n              this._advance();\n          } else {\n              var t = this._lookaheadToken(0);\n              var error = new Error(\"Expected \" + tokenType + \", got: \" + t.type);\n              error.name = \"ParserError\";\n              throw error;\n          }\n      },\n\n      _errorToken: function(token) {\n          var error = new Error(\"Invalid token (\" +\n                                token.type + \"): \\\"\" +\n                                token.value + \"\\\"\");\n          error.name = \"ParserError\";\n          throw error;\n      },\n\n\n      _parseIndexExpression: function() {\n          if (this._lookahead(0) === TOK_COLON || this._lookahead(1) === TOK_COLON) {\n              return this._parseSliceExpression();\n          } else {\n              var node = {\n                  type: \"Index\",\n                  value: this._lookaheadToken(0).value};\n              this._advance();\n              this._match(TOK_RBRACKET);\n              return node;\n          }\n      },\n\n      _projectIfSlice: function(left, right) {\n          var indexExpr = {type: \"IndexExpression\", children: [left, right]};\n          if (right.type === \"Slice\") {\n              return {\n                  type: \"Projection\",\n                  children: [indexExpr, this._parseProjectionRHS(bindingPower.Star)]\n              };\n          } else {\n              return indexExpr;\n          }\n      },\n\n      _parseSliceExpression: function() {\n          // [start:end:step] where each part is optional, as well as the last\n          // colon.\n          var parts = [null, null, null];\n          var index = 0;\n          var currentToken = this._lookahead(0);\n          while (currentToken !== TOK_RBRACKET && index < 3) {\n              if (currentToken === TOK_COLON) {\n                  index++;\n                  this._advance();\n              } else if (currentToken === TOK_NUMBER) {\n                  parts[index] = this._lookaheadToken(0).value;\n                  this._advance();\n              } else {\n                  var t = this._lookahead(0);\n                  var error = new Error(\"Syntax error, unexpected token: \" +\n                                        t.value + \"(\" + t.type + \")\");\n                  error.name = \"Parsererror\";\n                  throw error;\n              }\n              currentToken = this._lookahead(0);\n          }\n          this._match(TOK_RBRACKET);\n          return {\n              type: \"Slice\",\n              children: parts\n          };\n      },\n\n      _parseComparator: function(left, comparator) {\n        var right = this.expression(bindingPower[comparator]);\n        return {type: \"Comparator\", name: comparator, children: [left, right]};\n      },\n\n      _parseDotRHS: function(rbp) {\n          var lookahead = this._lookahead(0);\n          var exprTokens = [TOK_UNQUOTEDIDENTIFIER, TOK_QUOTEDIDENTIFIER, TOK_STAR];\n          if (exprTokens.indexOf(lookahead) >= 0) {\n              return this.expression(rbp);\n          } else if (lookahead === TOK_LBRACKET) {\n              this._match(TOK_LBRACKET);\n              return this._parseMultiselectList();\n          } else if (lookahead === TOK_LBRACE) {\n              this._match(TOK_LBRACE);\n              return this._parseMultiselectHash();\n          }\n      },\n\n      _parseProjectionRHS: function(rbp) {\n          var right;\n          if (bindingPower[this._lookahead(0)] < 10) {\n              right = {type: \"Identity\"};\n          } else if (this._lookahead(0) === TOK_LBRACKET) {\n              right = this.expression(rbp);\n          } else if (this._lookahead(0) === TOK_FILTER) {\n              right = this.expression(rbp);\n          } else if (this._lookahead(0) === TOK_DOT) {\n              this._match(TOK_DOT);\n              right = this._parseDotRHS(rbp);\n          } else {\n              var t = this._lookaheadToken(0);\n              var error = new Error(\"Sytanx error, unexpected token: \" +\n                                    t.value + \"(\" + t.type + \")\");\n              error.name = \"ParserError\";\n              throw error;\n          }\n          return right;\n      },\n\n      _parseMultiselectList: function() {\n          var expressions = [];\n          while (this._lookahead(0) !== TOK_RBRACKET) {\n              var expression = this.expression(0);\n              expressions.push(expression);\n              if (this._lookahead(0) === TOK_COMMA) {\n                  this._match(TOK_COMMA);\n                  if (this._lookahead(0) === TOK_RBRACKET) {\n                    throw new Error(\"Unexpected token Rbracket\");\n                  }\n              }\n          }\n          this._match(TOK_RBRACKET);\n          return {type: \"MultiSelectList\", children: expressions};\n      },\n\n      _parseMultiselectHash: function() {\n        var pairs = [];\n        var identifierTypes = [TOK_UNQUOTEDIDENTIFIER, TOK_QUOTEDIDENTIFIER];\n        var keyToken, keyName, value, node;\n        for (;;) {\n          keyToken = this._lookaheadToken(0);\n          if (identifierTypes.indexOf(keyToken.type) < 0) {\n            throw new Error(\"Expecting an identifier token, got: \" +\n                            keyToken.type);\n          }\n          keyName = keyToken.value;\n          this._advance();\n          this._match(TOK_COLON);\n          value = this.expression(0);\n          node = {type: \"KeyValuePair\", name: keyName, value: value};\n          pairs.push(node);\n          if (this._lookahead(0) === TOK_COMMA) {\n            this._match(TOK_COMMA);\n          } else if (this._lookahead(0) === TOK_RBRACE) {\n            this._match(TOK_RBRACE);\n            break;\n          }\n        }\n        return {type: \"MultiSelectHash\", children: pairs};\n      }\n  };\n\n\n  function TreeInterpreter(runtime) {\n    this.runtime = runtime;\n  }\n\n  TreeInterpreter.prototype = {\n      search: function(node, value) {\n          return this.visit(node, value);\n      },\n\n      visit: function(node, value) {\n          var matched, current, result, first, second, field, left, right, collected, i;\n          switch (node.type) {\n            case \"Field\":\n              if (value !== null && isObject(value)) {\n                  field = value[node.name];\n                  if (field === undefined) {\n                      return null;\n                  } else {\n                      return field;\n                  }\n              }\n              return null;\n            case \"Subexpression\":\n              result = this.visit(node.children[0], value);\n              for (i = 1; i < node.children.length; i++) {\n                  result = this.visit(node.children[1], result);\n                  if (result === null) {\n                      return null;\n                  }\n              }\n              return result;\n            case \"IndexExpression\":\n              left = this.visit(node.children[0], value);\n              right = this.visit(node.children[1], left);\n              return right;\n            case \"Index\":\n              if (!isArray(value)) {\n                return null;\n              }\n              var index = node.value;\n              if (index < 0) {\n                index = value.length + index;\n              }\n              result = value[index];\n              if (result === undefined) {\n                result = null;\n              }\n              return result;\n            case \"Slice\":\n              if (!isArray(value)) {\n                return null;\n              }\n              var sliceParams = node.children.slice(0);\n              var computed = this.computeSliceParams(value.length, sliceParams);\n              var start = computed[0];\n              var stop = computed[1];\n              var step = computed[2];\n              result = [];\n              if (step > 0) {\n                  for (i = start; i < stop; i += step) {\n                      result.push(value[i]);\n                  }\n              } else {\n                  for (i = start; i > stop; i += step) {\n                      result.push(value[i]);\n                  }\n              }\n              return result;\n            case \"Projection\":\n              // Evaluate left child.\n              var base = this.visit(node.children[0], value);\n              if (!isArray(base)) {\n                return null;\n              }\n              collected = [];\n              for (i = 0; i < base.length; i++) {\n                current = this.visit(node.children[1], base[i]);\n                if (current !== null) {\n                  collected.push(current);\n                }\n              }\n              return collected;\n            case \"ValueProjection\":\n              // Evaluate left child.\n              base = this.visit(node.children[0], value);\n              if (!isObject(base)) {\n                return null;\n              }\n              collected = [];\n              var values = objValues(base);\n              for (i = 0; i < values.length; i++) {\n                current = this.visit(node.children[1], values[i]);\n                if (current !== null) {\n                  collected.push(current);\n                }\n              }\n              return collected;\n            case \"FilterProjection\":\n              base = this.visit(node.children[0], value);\n              if (!isArray(base)) {\n                return null;\n              }\n              var filtered = [];\n              var finalResults = [];\n              for (i = 0; i < base.length; i++) {\n                matched = this.visit(node.children[2], base[i]);\n                if (!isFalse(matched)) {\n                  filtered.push(base[i]);\n                }\n              }\n              for (var j = 0; j < filtered.length; j++) {\n                current = this.visit(node.children[1], filtered[j]);\n                if (current !== null) {\n                  finalResults.push(current);\n                }\n              }\n              return finalResults;\n            case \"Comparator\":\n              first = this.visit(node.children[0], value);\n              second = this.visit(node.children[1], value);\n              switch(node.name) {\n                case TOK_EQ:\n                  result = strictDeepEqual(first, second);\n                  break;\n                case TOK_NE:\n                  result = !strictDeepEqual(first, second);\n                  break;\n                case TOK_GT:\n                  result = first > second;\n                  break;\n                case TOK_GTE:\n                  result = first >= second;\n                  break;\n                case TOK_LT:\n                  result = first < second;\n                  break;\n                case TOK_LTE:\n                  result = first <= second;\n                  break;\n                default:\n                  throw new Error(\"Unknown comparator: \" + node.name);\n              }\n              return result;\n            case TOK_FLATTEN:\n              var original = this.visit(node.children[0], value);\n              if (!isArray(original)) {\n                return null;\n              }\n              var merged = [];\n              for (i = 0; i < original.length; i++) {\n                current = original[i];\n                if (isArray(current)) {\n                  merged.push.apply(merged, current);\n                } else {\n                  merged.push(current);\n                }\n              }\n              return merged;\n            case \"Identity\":\n              return value;\n            case \"MultiSelectList\":\n              if (value === null) {\n                return null;\n              }\n              collected = [];\n              for (i = 0; i < node.children.length; i++) {\n                  collected.push(this.visit(node.children[i], value));\n              }\n              return collected;\n            case \"MultiSelectHash\":\n              if (value === null) {\n                return null;\n              }\n              collected = {};\n              var child;\n              for (i = 0; i < node.children.length; i++) {\n                child = node.children[i];\n                collected[child.name] = this.visit(child.value, value);\n              }\n              return collected;\n            case \"OrExpression\":\n              matched = this.visit(node.children[0], value);\n              if (isFalse(matched)) {\n                  matched = this.visit(node.children[1], value);\n              }\n              return matched;\n            case \"AndExpression\":\n              first = this.visit(node.children[0], value);\n\n              if (isFalse(first) === true) {\n                return first;\n              }\n              return this.visit(node.children[1], value);\n            case \"NotExpression\":\n              first = this.visit(node.children[0], value);\n              return isFalse(first);\n            case \"Literal\":\n              return node.value;\n            case TOK_PIPE:\n              left = this.visit(node.children[0], value);\n              return this.visit(node.children[1], left);\n            case TOK_CURRENT:\n              return value;\n            case \"Function\":\n              var resolvedArgs = [];\n              for (i = 0; i < node.children.length; i++) {\n                  resolvedArgs.push(this.visit(node.children[i], value));\n              }\n              return this.runtime.callFunction(node.name, resolvedArgs);\n            case \"ExpressionReference\":\n              var refNode = node.children[0];\n              // Tag the node with a specific attribute so the type\n              // checker verify the type.\n              refNode.jmespathType = TOK_EXPREF;\n              return refNode;\n            default:\n              throw new Error(\"Unknown node type: \" + node.type);\n          }\n      },\n\n      computeSliceParams: function(arrayLength, sliceParams) {\n        var start = sliceParams[0];\n        var stop = sliceParams[1];\n        var step = sliceParams[2];\n        var computed = [null, null, null];\n        if (step === null) {\n          step = 1;\n        } else if (step === 0) {\n          var error = new Error(\"Invalid slice, step cannot be 0\");\n          error.name = \"RuntimeError\";\n          throw error;\n        }\n        var stepValueNegative = step < 0 ? true : false;\n\n        if (start === null) {\n            start = stepValueNegative ? arrayLength - 1 : 0;\n        } else {\n            start = this.capSliceRange(arrayLength, start, step);\n        }\n\n        if (stop === null) {\n            stop = stepValueNegative ? -1 : arrayLength;\n        } else {\n            stop = this.capSliceRange(arrayLength, stop, step);\n        }\n        computed[0] = start;\n        computed[1] = stop;\n        computed[2] = step;\n        return computed;\n      },\n\n      capSliceRange: function(arrayLength, actualValue, step) {\n          if (actualValue < 0) {\n              actualValue += arrayLength;\n              if (actualValue < 0) {\n                  actualValue = step < 0 ? -1 : 0;\n              }\n          } else if (actualValue >= arrayLength) {\n              actualValue = step < 0 ? arrayLength - 1 : arrayLength;\n          }\n          return actualValue;\n      }\n\n  };\n\n  function Runtime(interpreter) {\n    this._interpreter = interpreter;\n    this.functionTable = {\n        // name: [function, <signature>]\n        // The <signature> can be:\n        //\n        // {\n        //   args: [[type1, type2], [type1, type2]],\n        //   variadic: true|false\n        // }\n        //\n        // Each arg in the arg list is a list of valid types\n        // (if the function is overloaded and supports multiple\n        // types.  If the type is \"any\" then no type checking\n        // occurs on the argument.  Variadic is optional\n        // and if not provided is assumed to be false.\n        abs: {_func: this._functionAbs, _signature: [{types: [TYPE_NUMBER]}]},\n        avg: {_func: this._functionAvg, _signature: [{types: [TYPE_ARRAY_NUMBER]}]},\n        ceil: {_func: this._functionCeil, _signature: [{types: [TYPE_NUMBER]}]},\n        contains: {\n            _func: this._functionContains,\n            _signature: [{types: [TYPE_STRING, TYPE_ARRAY]},\n                        {types: [TYPE_ANY]}]},\n        \"ends_with\": {\n            _func: this._functionEndsWith,\n            _signature: [{types: [TYPE_STRING]}, {types: [TYPE_STRING]}]},\n        floor: {_func: this._functionFloor, _signature: [{types: [TYPE_NUMBER]}]},\n        length: {\n            _func: this._functionLength,\n            _signature: [{types: [TYPE_STRING, TYPE_ARRAY, TYPE_OBJECT]}]},\n        map: {\n            _func: this._functionMap,\n            _signature: [{types: [TYPE_EXPREF]}, {types: [TYPE_ARRAY]}]},\n        max: {\n            _func: this._functionMax,\n            _signature: [{types: [TYPE_ARRAY_NUMBER, TYPE_ARRAY_STRING]}]},\n        \"merge\": {\n            _func: this._functionMerge,\n            _signature: [{types: [TYPE_OBJECT], variadic: true}]\n        },\n        \"max_by\": {\n          _func: this._functionMaxBy,\n          _signature: [{types: [TYPE_ARRAY]}, {types: [TYPE_EXPREF]}]\n        },\n        sum: {_func: this._functionSum, _signature: [{types: [TYPE_ARRAY_NUMBER]}]},\n        \"starts_with\": {\n            _func: this._functionStartsWith,\n            _signature: [{types: [TYPE_STRING]}, {types: [TYPE_STRING]}]},\n        min: {\n            _func: this._functionMin,\n            _signature: [{types: [TYPE_ARRAY_NUMBER, TYPE_ARRAY_STRING]}]},\n        \"min_by\": {\n          _func: this._functionMinBy,\n          _signature: [{types: [TYPE_ARRAY]}, {types: [TYPE_EXPREF]}]\n        },\n        type: {_func: this._functionType, _signature: [{types: [TYPE_ANY]}]},\n        keys: {_func: this._functionKeys, _signature: [{types: [TYPE_OBJECT]}]},\n        values: {_func: this._functionValues, _signature: [{types: [TYPE_OBJECT]}]},\n        sort: {_func: this._functionSort, _signature: [{types: [TYPE_ARRAY_STRING, TYPE_ARRAY_NUMBER]}]},\n        \"sort_by\": {\n          _func: this._functionSortBy,\n          _signature: [{types: [TYPE_ARRAY]}, {types: [TYPE_EXPREF]}]\n        },\n        join: {\n            _func: this._functionJoin,\n            _signature: [\n                {types: [TYPE_STRING]},\n                {types: [TYPE_ARRAY_STRING]}\n            ]\n        },\n        reverse: {\n            _func: this._functionReverse,\n            _signature: [{types: [TYPE_STRING, TYPE_ARRAY]}]},\n        \"to_array\": {_func: this._functionToArray, _signature: [{types: [TYPE_ANY]}]},\n        \"to_string\": {_func: this._functionToString, _signature: [{types: [TYPE_ANY]}]},\n        \"to_number\": {_func: this._functionToNumber, _signature: [{types: [TYPE_ANY]}]},\n        \"not_null\": {\n            _func: this._functionNotNull,\n            _signature: [{types: [TYPE_ANY], variadic: true}]\n        }\n    };\n  }\n\n  Runtime.prototype = {\n    callFunction: function(name, resolvedArgs) {\n      var functionEntry = this.functionTable[name];\n      if (functionEntry === undefined) {\n          throw new Error(\"Unknown function: \" + name + \"()\");\n      }\n      this._validateArgs(name, resolvedArgs, functionEntry._signature);\n      return functionEntry._func.call(this, resolvedArgs);\n    },\n\n    _validateArgs: function(name, args, signature) {\n        // Validating the args requires validating\n        // the correct arity and the correct type of each arg.\n        // If the last argument is declared as variadic, then we need\n        // a minimum number of args to be required.  Otherwise it has to\n        // be an exact amount.\n        var pluralized;\n        if (signature[signature.length - 1].variadic) {\n            if (args.length < signature.length) {\n                pluralized = signature.length === 1 ? \" argument\" : \" arguments\";\n                throw new Error(\"ArgumentError: \" + name + \"() \" +\n                                \"takes at least\" + signature.length + pluralized +\n                                \" but received \" + args.length);\n            }\n        } else if (args.length !== signature.length) {\n            pluralized = signature.length === 1 ? \" argument\" : \" arguments\";\n            throw new Error(\"ArgumentError: \" + name + \"() \" +\n                            \"takes \" + signature.length + pluralized +\n                            \" but received \" + args.length);\n        }\n        var currentSpec;\n        var actualType;\n        var typeMatched;\n        for (var i = 0; i < signature.length; i++) {\n            typeMatched = false;\n            currentSpec = signature[i].types;\n            actualType = this._getTypeName(args[i]);\n            for (var j = 0; j < currentSpec.length; j++) {\n                if (this._typeMatches(actualType, currentSpec[j], args[i])) {\n                    typeMatched = true;\n                    break;\n                }\n            }\n            if (!typeMatched) {\n                var expected = currentSpec\n                    .map(function(typeIdentifier) {\n                        return TYPE_NAME_TABLE[typeIdentifier];\n                    })\n                    .join(',');\n                throw new Error(\"TypeError: \" + name + \"() \" +\n                                \"expected argument \" + (i + 1) +\n                                \" to be type \" + expected +\n                                \" but received type \" +\n                                TYPE_NAME_TABLE[actualType] + \" instead.\");\n            }\n        }\n    },\n\n    _typeMatches: function(actual, expected, argValue) {\n        if (expected === TYPE_ANY) {\n            return true;\n        }\n        if (expected === TYPE_ARRAY_STRING ||\n            expected === TYPE_ARRAY_NUMBER ||\n            expected === TYPE_ARRAY) {\n            // The expected type can either just be array,\n            // or it can require a specific subtype (array of numbers).\n            //\n            // The simplest case is if \"array\" with no subtype is specified.\n            if (expected === TYPE_ARRAY) {\n                return actual === TYPE_ARRAY;\n            } else if (actual === TYPE_ARRAY) {\n                // Otherwise we need to check subtypes.\n                // I think this has potential to be improved.\n                var subtype;\n                if (expected === TYPE_ARRAY_NUMBER) {\n                  subtype = TYPE_NUMBER;\n                } else if (expected === TYPE_ARRAY_STRING) {\n                  subtype = TYPE_STRING;\n                }\n                for (var i = 0; i < argValue.length; i++) {\n                    if (!this._typeMatches(\n                            this._getTypeName(argValue[i]), subtype,\n                                             argValue[i])) {\n                        return false;\n                    }\n                }\n                return true;\n            }\n        } else {\n            return actual === expected;\n        }\n    },\n    _getTypeName: function(obj) {\n        switch (Object.prototype.toString.call(obj)) {\n            case \"[object String]\":\n              return TYPE_STRING;\n            case \"[object Number]\":\n              return TYPE_NUMBER;\n            case \"[object Array]\":\n              return TYPE_ARRAY;\n            case \"[object Boolean]\":\n              return TYPE_BOOLEAN;\n            case \"[object Null]\":\n              return TYPE_NULL;\n            case \"[object Object]\":\n              // Check if it's an expref.  If it has, it's been\n              // tagged with a jmespathType attr of 'Expref';\n              if (obj.jmespathType === TOK_EXPREF) {\n                return TYPE_EXPREF;\n              } else {\n                return TYPE_OBJECT;\n              }\n        }\n    },\n\n    _functionStartsWith: function(resolvedArgs) {\n        return resolvedArgs[0].lastIndexOf(resolvedArgs[1]) === 0;\n    },\n\n    _functionEndsWith: function(resolvedArgs) {\n        var searchStr = resolvedArgs[0];\n        var suffix = resolvedArgs[1];\n        return searchStr.indexOf(suffix, searchStr.length - suffix.length) !== -1;\n    },\n\n    _functionReverse: function(resolvedArgs) {\n        var typeName = this._getTypeName(resolvedArgs[0]);\n        if (typeName === TYPE_STRING) {\n          var originalStr = resolvedArgs[0];\n          var reversedStr = \"\";\n          for (var i = originalStr.length - 1; i >= 0; i--) {\n              reversedStr += originalStr[i];\n          }\n          return reversedStr;\n        } else {\n          var reversedArray = resolvedArgs[0].slice(0);\n          reversedArray.reverse();\n          return reversedArray;\n        }\n    },\n\n    _functionAbs: function(resolvedArgs) {\n      return Math.abs(resolvedArgs[0]);\n    },\n\n    _functionCeil: function(resolvedArgs) {\n        return Math.ceil(resolvedArgs[0]);\n    },\n\n    _functionAvg: function(resolvedArgs) {\n        var sum = 0;\n        var inputArray = resolvedArgs[0];\n        for (var i = 0; i < inputArray.length; i++) {\n            sum += inputArray[i];\n        }\n        return sum / inputArray.length;\n    },\n\n    _functionContains: function(resolvedArgs) {\n        return resolvedArgs[0].indexOf(resolvedArgs[1]) >= 0;\n    },\n\n    _functionFloor: function(resolvedArgs) {\n        return Math.floor(resolvedArgs[0]);\n    },\n\n    _functionLength: function(resolvedArgs) {\n       if (!isObject(resolvedArgs[0])) {\n         return resolvedArgs[0].length;\n       } else {\n         // As far as I can tell, there's no way to get the length\n         // of an object without O(n) iteration through the object.\n         return Object.keys(resolvedArgs[0]).length;\n       }\n    },\n\n    _functionMap: function(resolvedArgs) {\n      var mapped = [];\n      var interpreter = this._interpreter;\n      var exprefNode = resolvedArgs[0];\n      var elements = resolvedArgs[1];\n      for (var i = 0; i < elements.length; i++) {\n          mapped.push(interpreter.visit(exprefNode, elements[i]));\n      }\n      return mapped;\n    },\n\n    _functionMerge: function(resolvedArgs) {\n      var merged = {};\n      for (var i = 0; i < resolvedArgs.length; i++) {\n        var current = resolvedArgs[i];\n        for (var key in current) {\n          merged[key] = current[key];\n        }\n      }\n      return merged;\n    },\n\n    _functionMax: function(resolvedArgs) {\n      if (resolvedArgs[0].length > 0) {\n        var typeName = this._getTypeName(resolvedArgs[0][0]);\n        if (typeName === TYPE_NUMBER) {\n          return Math.max.apply(Math, resolvedArgs[0]);\n        } else {\n          var elements = resolvedArgs[0];\n          var maxElement = elements[0];\n          for (var i = 1; i < elements.length; i++) {\n              if (maxElement.localeCompare(elements[i]) < 0) {\n                  maxElement = elements[i];\n              }\n          }\n          return maxElement;\n        }\n      } else {\n          return null;\n      }\n    },\n\n    _functionMin: function(resolvedArgs) {\n      if (resolvedArgs[0].length > 0) {\n        var typeName = this._getTypeName(resolvedArgs[0][0]);\n        if (typeName === TYPE_NUMBER) {\n          return Math.min.apply(Math, resolvedArgs[0]);\n        } else {\n          var elements = resolvedArgs[0];\n          var minElement = elements[0];\n          for (var i = 1; i < elements.length; i++) {\n              if (elements[i].localeCompare(minElement) < 0) {\n                  minElement = elements[i];\n              }\n          }\n          return minElement;\n        }\n      } else {\n        return null;\n      }\n    },\n\n    _functionSum: function(resolvedArgs) {\n      var sum = 0;\n      var listToSum = resolvedArgs[0];\n      for (var i = 0; i < listToSum.length; i++) {\n        sum += listToSum[i];\n      }\n      return sum;\n    },\n\n    _functionType: function(resolvedArgs) {\n        switch (this._getTypeName(resolvedArgs[0])) {\n          case TYPE_NUMBER:\n            return \"number\";\n          case TYPE_STRING:\n            return \"string\";\n          case TYPE_ARRAY:\n            return \"array\";\n          case TYPE_OBJECT:\n            return \"object\";\n          case TYPE_BOOLEAN:\n            return \"boolean\";\n          case TYPE_EXPREF:\n            return \"expref\";\n          case TYPE_NULL:\n            return \"null\";\n        }\n    },\n\n    _functionKeys: function(resolvedArgs) {\n        return Object.keys(resolvedArgs[0]);\n    },\n\n    _functionValues: function(resolvedArgs) {\n        var obj = resolvedArgs[0];\n        var keys = Object.keys(obj);\n        var values = [];\n        for (var i = 0; i < keys.length; i++) {\n            values.push(obj[keys[i]]);\n        }\n        return values;\n    },\n\n    _functionJoin: function(resolvedArgs) {\n        var joinChar = resolvedArgs[0];\n        var listJoin = resolvedArgs[1];\n        return listJoin.join(joinChar);\n    },\n\n    _functionToArray: function(resolvedArgs) {\n        if (this._getTypeName(resolvedArgs[0]) === TYPE_ARRAY) {\n            return resolvedArgs[0];\n        } else {\n            return [resolvedArgs[0]];\n        }\n    },\n\n    _functionToString: function(resolvedArgs) {\n        if (this._getTypeName(resolvedArgs[0]) === TYPE_STRING) {\n            return resolvedArgs[0];\n        } else {\n            return JSON.stringify(resolvedArgs[0]);\n        }\n    },\n\n    _functionToNumber: function(resolvedArgs) {\n        var typeName = this._getTypeName(resolvedArgs[0]);\n        var convertedValue;\n        if (typeName === TYPE_NUMBER) {\n            return resolvedArgs[0];\n        } else if (typeName === TYPE_STRING) {\n            convertedValue = +resolvedArgs[0];\n            if (!isNaN(convertedValue)) {\n                return convertedValue;\n            }\n        }\n        return null;\n    },\n\n    _functionNotNull: function(resolvedArgs) {\n        for (var i = 0; i < resolvedArgs.length; i++) {\n            if (this._getTypeName(resolvedArgs[i]) !== TYPE_NULL) {\n                return resolvedArgs[i];\n            }\n        }\n        return null;\n    },\n\n    _functionSort: function(resolvedArgs) {\n        var sortedArray = resolvedArgs[0].slice(0);\n        sortedArray.sort();\n        return sortedArray;\n    },\n\n    _functionSortBy: function(resolvedArgs) {\n        var sortedArray = resolvedArgs[0].slice(0);\n        if (sortedArray.length === 0) {\n            return sortedArray;\n        }\n        var interpreter = this._interpreter;\n        var exprefNode = resolvedArgs[1];\n        var requiredType = this._getTypeName(\n            interpreter.visit(exprefNode, sortedArray[0]));\n        if ([TYPE_NUMBER, TYPE_STRING].indexOf(requiredType) < 0) {\n            throw new Error(\"TypeError\");\n        }\n        var that = this;\n        // In order to get a stable sort out of an unstable\n        // sort algorithm, we decorate/sort/undecorate (DSU)\n        // by creating a new list of [index, element] pairs.\n        // In the cmp function, if the evaluated elements are\n        // equal, then the index will be used as the tiebreaker.\n        // After the decorated list has been sorted, it will be\n        // undecorated to extract the original elements.\n        var decorated = [];\n        for (var i = 0; i < sortedArray.length; i++) {\n          decorated.push([i, sortedArray[i]]);\n        }\n        decorated.sort(function(a, b) {\n          var exprA = interpreter.visit(exprefNode, a[1]);\n          var exprB = interpreter.visit(exprefNode, b[1]);\n          if (that._getTypeName(exprA) !== requiredType) {\n              throw new Error(\n                  \"TypeError: expected \" + requiredType + \", received \" +\n                  that._getTypeName(exprA));\n          } else if (that._getTypeName(exprB) !== requiredType) {\n              throw new Error(\n                  \"TypeError: expected \" + requiredType + \", received \" +\n                  that._getTypeName(exprB));\n          }\n          if (exprA > exprB) {\n            return 1;\n          } else if (exprA < exprB) {\n            return -1;\n          } else {\n            // If they're equal compare the items by their\n            // order to maintain relative order of equal keys\n            // (i.e. to get a stable sort).\n            return a[0] - b[0];\n          }\n        });\n        // Undecorate: extract out the original list elements.\n        for (var j = 0; j < decorated.length; j++) {\n          sortedArray[j] = decorated[j][1];\n        }\n        return sortedArray;\n    },\n\n    _functionMaxBy: function(resolvedArgs) {\n      var exprefNode = resolvedArgs[1];\n      var resolvedArray = resolvedArgs[0];\n      var keyFunction = this.createKeyFunction(exprefNode, [TYPE_NUMBER, TYPE_STRING]);\n      var maxNumber = -Infinity;\n      var maxRecord;\n      var current;\n      for (var i = 0; i < resolvedArray.length; i++) {\n        current = keyFunction(resolvedArray[i]);\n        if (current > maxNumber) {\n          maxNumber = current;\n          maxRecord = resolvedArray[i];\n        }\n      }\n      return maxRecord;\n    },\n\n    _functionMinBy: function(resolvedArgs) {\n      var exprefNode = resolvedArgs[1];\n      var resolvedArray = resolvedArgs[0];\n      var keyFunction = this.createKeyFunction(exprefNode, [TYPE_NUMBER, TYPE_STRING]);\n      var minNumber = Infinity;\n      var minRecord;\n      var current;\n      for (var i = 0; i < resolvedArray.length; i++) {\n        current = keyFunction(resolvedArray[i]);\n        if (current < minNumber) {\n          minNumber = current;\n          minRecord = resolvedArray[i];\n        }\n      }\n      return minRecord;\n    },\n\n    createKeyFunction: function(exprefNode, allowedTypes) {\n      var that = this;\n      var interpreter = this._interpreter;\n      var keyFunc = function(x) {\n        var current = interpreter.visit(exprefNode, x);\n        if (allowedTypes.indexOf(that._getTypeName(current)) < 0) {\n          var msg = \"TypeError: expected one of \" + allowedTypes +\n                    \", received \" + that._getTypeName(current);\n          throw new Error(msg);\n        }\n        return current;\n      };\n      return keyFunc;\n    }\n\n  };\n\n  function compile(stream) {\n    var parser = new Parser();\n    var ast = parser.parse(stream);\n    return ast;\n  }\n\n  function tokenize(stream) {\n      var lexer = new Lexer();\n      return lexer.tokenize(stream);\n  }\n\n  function search(data, expression) {\n      var parser = new Parser();\n      // This needs to be improved.  Both the interpreter and runtime depend on\n      // each other.  The runtime needs the interpreter to support exprefs.\n      // There's likely a clean way to avoid the cyclic dependency.\n      var runtime = new Runtime();\n      var interpreter = new TreeInterpreter(runtime);\n      runtime._interpreter = interpreter;\n      var node = parser.parse(expression);\n      return interpreter.search(node, data);\n  }\n\n  exports.tokenize = tokenize;\n  exports.compile = compile;\n  exports.search = search;\n  exports.strictDeepEqual = strictDeepEqual;\n})( false ? 0 : exports);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/jmespath@0.16.0/node_modules/jmespath/jmespath.js\n");

/***/ })

};
;