"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57";
exports.ids = ["vendor-chunks/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALIGN_OPTIONS: () => (/* binding */ ALIGN_OPTIONS),\n/* harmony export */   Anchor: () => (/* binding */ Anchor),\n/* harmony export */   Arrow: () => (/* binding */ Arrow),\n/* harmony export */   Content: () => (/* binding */ Content),\n/* harmony export */   Popper: () => (/* binding */ Popper),\n/* harmony export */   PopperAnchor: () => (/* binding */ PopperAnchor),\n/* harmony export */   PopperArrow: () => (/* binding */ PopperArrow),\n/* harmony export */   PopperContent: () => (/* binding */ PopperContent),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   SIDE_OPTIONS: () => (/* binding */ SIDE_OPTIONS),\n/* harmony export */   createPopperScope: () => (/* binding */ createPopperScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+react-dom@2.1._22f34d6c5d327f6ea6a6ae0160972b20/node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs\");\n/* harmony import */ var _floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @floating-ui/react-dom */ \"(ssr)/./node_modules/.pnpm/@floating-ui+dom@1.7.2/node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs\");\n/* harmony import */ var _radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-arrow */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-arrow@1.1.7_e9e31f839ccc03b965a9c76fb12e37fb/node_modules/@radix-ui/react-arrow/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_d352c0cd6a6afa5cbe132ca4d71633df/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_00f1a526ffd026e40bef218e06c12993/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._6892bc03897e7520d34e6acf45100b9a/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ ALIGN_OPTIONS,Anchor,Arrow,Content,Popper,PopperAnchor,PopperArrow,PopperContent,Root,SIDE_OPTIONS,createPopperScope auto */ // src/popper.tsx\n\n\n\n\n\n\n\n\n\n\nvar SIDE_OPTIONS = [\n    \"top\",\n    \"right\",\n    \"bottom\",\n    \"left\"\n];\nvar ALIGN_OPTIONS = [\n    \"start\",\n    \"center\",\n    \"end\"\n];\nvar POPPER_NAME = \"Popper\";\nvar [createPopperContext, createPopperScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPPER_NAME);\nvar [PopperProvider, usePopperContext] = createPopperContext(POPPER_NAME);\nvar Popper = (props)=>{\n    const { __scopePopper, children } = props;\n    const [anchor, setAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperProvider, {\n        scope: __scopePopper,\n        anchor,\n        onAnchorChange: setAnchor,\n        children\n    });\n};\nPopper.displayName = POPPER_NAME;\nvar ANCHOR_NAME = \"PopperAnchor\";\nvar PopperAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, virtualRef, ...anchorProps } = props;\n    const context = usePopperContext(ANCHOR_NAME, __scopePopper);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, ref);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        context.onAnchorChange(virtualRef?.current || ref.current);\n    });\n    return virtualRef ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        ...anchorProps,\n        ref: composedRefs\n    });\n});\nPopperAnchor.displayName = ANCHOR_NAME;\nvar CONTENT_NAME = \"PopperContent\";\nvar [PopperContentProvider, useContentContext] = createPopperContext(CONTENT_NAME);\nvar PopperContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopper, side = \"bottom\", sideOffset = 0, align = \"center\", alignOffset = 0, arrowPadding = 0, avoidCollisions = true, collisionBoundary = [], collisionPadding: collisionPaddingProp = 0, sticky = \"partial\", hideWhenDetached = false, updatePositionStrategy = \"optimized\", onPlaced, ...contentProps } = props;\n    const context = usePopperContext(CONTENT_NAME, __scopePopper);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [arrow, setArrow] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const arrowSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_5__.useSize)(arrow);\n    const arrowWidth = arrowSize?.width ?? 0;\n    const arrowHeight = arrowSize?.height ?? 0;\n    const desiredPlacement = side + (align !== \"center\" ? \"-\" + align : \"\");\n    const collisionPadding = typeof collisionPaddingProp === \"number\" ? collisionPaddingProp : {\n        top: 0,\n        right: 0,\n        bottom: 0,\n        left: 0,\n        ...collisionPaddingProp\n    };\n    const boundary = Array.isArray(collisionBoundary) ? collisionBoundary : [\n        collisionBoundary\n    ];\n    const hasExplicitBoundaries = boundary.length > 0;\n    const detectOverflowOptions = {\n        padding: collisionPadding,\n        boundary: boundary.filter(isNotNull),\n        // with `strategy: 'fixed'`, this is the only way to get it to respect boundaries\n        altBoundary: hasExplicitBoundaries\n    };\n    const { refs, floatingStyles, placement, isPositioned, middlewareData } = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.useFloating)({\n        // default to `fixed` strategy so users don't have to pick and we also avoid focus scroll issues\n        strategy: \"fixed\",\n        placement: desiredPlacement,\n        whileElementsMounted: (...args)=>{\n            const cleanup = (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_7__.autoUpdate)(...args, {\n                animationFrame: updatePositionStrategy === \"always\"\n            });\n            return cleanup;\n        },\n        elements: {\n            reference: context.anchor\n        },\n        middleware: [\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.offset)({\n                mainAxis: sideOffset + arrowHeight,\n                alignmentAxis: alignOffset\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.shift)({\n                mainAxis: true,\n                crossAxis: false,\n                limiter: sticky === \"partial\" ? (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.limitShift)() : void 0,\n                ...detectOverflowOptions\n            }),\n            avoidCollisions && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.flip)({\n                ...detectOverflowOptions\n            }),\n            (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.size)({\n                ...detectOverflowOptions,\n                apply: ({ elements, rects, availableWidth, availableHeight })=>{\n                    const { width: anchorWidth, height: anchorHeight } = rects.reference;\n                    const contentStyle = elements.floating.style;\n                    contentStyle.setProperty(\"--radix-popper-available-width\", `${availableWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-available-height\", `${availableHeight}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-width\", `${anchorWidth}px`);\n                    contentStyle.setProperty(\"--radix-popper-anchor-height\", `${anchorHeight}px`);\n                }\n            }),\n            arrow && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.arrow)({\n                element: arrow,\n                padding: arrowPadding\n            }),\n            transformOrigin({\n                arrowWidth,\n                arrowHeight\n            }),\n            hideWhenDetached && (0,_floating_ui_react_dom__WEBPACK_IMPORTED_MODULE_6__.hide)({\n                strategy: \"referenceHidden\",\n                ...detectOverflowOptions\n            })\n        ]\n    });\n    const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n    const handlePlaced = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_8__.useCallbackRef)(onPlaced);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (isPositioned) {\n            handlePlaced?.();\n        }\n    }, [\n        isPositioned,\n        handlePlaced\n    ]);\n    const arrowX = middlewareData.arrow?.x;\n    const arrowY = middlewareData.arrow?.y;\n    const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_9__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n        ref: refs.setFloating,\n        \"data-radix-popper-content-wrapper\": \"\",\n        style: {\n            ...floatingStyles,\n            transform: isPositioned ? floatingStyles.transform : \"translate(0, -200%)\",\n            // keep off the page when measuring\n            minWidth: \"max-content\",\n            zIndex: contentZIndex,\n            [\"--radix-popper-transform-origin\"]: [\n                middlewareData.transformOrigin?.x,\n                middlewareData.transformOrigin?.y\n            ].join(\" \"),\n            // hide the content if using the hide middleware and should be hidden\n            // set visibility to hidden and disable pointer events so the UI behaves\n            // as if the PopperContent isn't there at all\n            ...middlewareData.hide?.referenceHidden && {\n                visibility: \"hidden\",\n                pointerEvents: \"none\"\n            }\n        },\n        dir: props.dir,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopperContentProvider, {\n            scope: __scopePopper,\n            placedSide,\n            onArrowChange: setArrow,\n            arrowX,\n            arrowY,\n            shouldHideArrow: cannotCenterArrow,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n                \"data-side\": placedSide,\n                \"data-align\": placedAlign,\n                ...contentProps,\n                ref: composedRefs,\n                style: {\n                    ...contentProps.style,\n                    // if the PopperContent hasn't been placed yet (not all measurements done)\n                    // we prevent animations so that users's animation don't kick in too early referring wrong sides\n                    animation: !isPositioned ? \"none\" : void 0\n                }\n            })\n        })\n    });\n});\nPopperContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"PopperArrow\";\nvar OPPOSITE_SIDE = {\n    top: \"bottom\",\n    right: \"left\",\n    bottom: \"top\",\n    left: \"right\"\n};\nvar PopperArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function PopperArrow2(props, forwardedRef) {\n    const { __scopePopper, ...arrowProps } = props;\n    const contentContext = useContentContext(ARROW_NAME, __scopePopper);\n    const baseSide = OPPOSITE_SIDE[contentContext.placedSide];\n    return(// we have to use an extra wrapper because `ResizeObserver` (used by `useSize`)\n    // doesn't report size as we'd expect on SVG elements.\n    // it reports their bounding box which is effectively the largest path inside the SVG.\n    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        ref: contentContext.onArrowChange,\n        style: {\n            position: \"absolute\",\n            left: contentContext.arrowX,\n            top: contentContext.arrowY,\n            [baseSide]: 0,\n            transformOrigin: {\n                top: \"\",\n                right: \"0 0\",\n                bottom: \"center 0\",\n                left: \"100% 0\"\n            }[contentContext.placedSide],\n            transform: {\n                top: \"translateY(100%)\",\n                right: \"translateY(50%) rotate(90deg) translateX(-50%)\",\n                bottom: `rotate(180deg)`,\n                left: \"translateY(50%) rotate(-90deg) translateX(50%)\"\n            }[contentContext.placedSide],\n            visibility: contentContext.shouldHideArrow ? \"hidden\" : void 0\n        },\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_arrow__WEBPACK_IMPORTED_MODULE_10__.Root, {\n            ...arrowProps,\n            ref: forwardedRef,\n            style: {\n                ...arrowProps.style,\n                // ensures the element can be measured correctly (mostly for if SVG)\n                display: \"block\"\n            }\n        })\n    }));\n});\nPopperArrow.displayName = ARROW_NAME;\nfunction isNotNull(value) {\n    return value !== null;\n}\nvar transformOrigin = (options)=>({\n        name: \"transformOrigin\",\n        options,\n        fn (data) {\n            const { placement, rects, middlewareData } = data;\n            const cannotCenterArrow = middlewareData.arrow?.centerOffset !== 0;\n            const isArrowHidden = cannotCenterArrow;\n            const arrowWidth = isArrowHidden ? 0 : options.arrowWidth;\n            const arrowHeight = isArrowHidden ? 0 : options.arrowHeight;\n            const [placedSide, placedAlign] = getSideAndAlignFromPlacement(placement);\n            const noArrowAlign = {\n                start: \"0%\",\n                center: \"50%\",\n                end: \"100%\"\n            }[placedAlign];\n            const arrowXCenter = (middlewareData.arrow?.x ?? 0) + arrowWidth / 2;\n            const arrowYCenter = (middlewareData.arrow?.y ?? 0) + arrowHeight / 2;\n            let x = \"\";\n            let y = \"\";\n            if (placedSide === \"bottom\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${-arrowHeight}px`;\n            } else if (placedSide === \"top\") {\n                x = isArrowHidden ? noArrowAlign : `${arrowXCenter}px`;\n                y = `${rects.floating.height + arrowHeight}px`;\n            } else if (placedSide === \"right\") {\n                x = `${-arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            } else if (placedSide === \"left\") {\n                x = `${rects.floating.width + arrowHeight}px`;\n                y = isArrowHidden ? noArrowAlign : `${arrowYCenter}px`;\n            }\n            return {\n                data: {\n                    x,\n                    y\n                }\n            };\n        }\n    });\nfunction getSideAndAlignFromPlacement(placement) {\n    const [side, align = \"center\"] = placement.split(\"-\");\n    return [\n        side,\n        align\n    ];\n}\nvar Root2 = Popper;\nvar Anchor = PopperAnchor;\nvar Content = PopperContent;\nvar Arrow = PopperArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\n");

/***/ })

};
;