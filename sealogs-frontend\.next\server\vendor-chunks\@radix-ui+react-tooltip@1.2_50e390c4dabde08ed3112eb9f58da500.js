"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500";
exports.ids = ["vendor-chunks/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Provider: () => (/* binding */ Provider),\n/* harmony export */   Root: () => (/* binding */ Root3),\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipArrow: () => (/* binding */ TooltipArrow),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipPortal: () => (/* binding */ TooltipPortal),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createTooltipScope: () => (/* binding */ createTooltipScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_bd769e2c7ddceeff6e63be21c84dfac7/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Portal,Provider,Root,Tooltip,TooltipArrow,TooltipContent,TooltipPortal,TooltipProvider,TooltipTrigger,Trigger,createTooltipScope auto */ // src/tooltip.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar [createTooltipContext, createTooltipScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(\"Tooltip\", [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar PROVIDER_NAME = \"TooltipProvider\";\nvar DEFAULT_DELAY_DURATION = 700;\nvar TOOLTIP_OPEN = \"tooltip.open\";\nvar [TooltipProviderContextProvider, useTooltipProviderContext] = createTooltipContext(PROVIDER_NAME);\nvar TooltipProvider = (props)=>{\n    const { __scopeTooltip, delayDuration = DEFAULT_DELAY_DURATION, skipDelayDuration = 300, disableHoverableContent = false, children } = props;\n    const isOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const isPointerInTransitRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const skipDelayTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const skipDelayTimer = skipDelayTimerRef.current;\n        return ()=>window.clearTimeout(skipDelayTimer);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipProviderContextProvider, {\n        scope: __scopeTooltip,\n        isOpenDelayedRef,\n        delayDuration,\n        onOpen: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            isOpenDelayedRef.current = false;\n        }, []),\n        onClose: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n            window.clearTimeout(skipDelayTimerRef.current);\n            skipDelayTimerRef.current = window.setTimeout(()=>isOpenDelayedRef.current = true, skipDelayDuration);\n        }, [\n            skipDelayDuration\n        ]),\n        isPointerInTransitRef,\n        onPointerInTransitChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((inTransit)=>{\n            isPointerInTransitRef.current = inTransit;\n        }, []),\n        disableHoverableContent,\n        children\n    });\n};\nTooltipProvider.displayName = PROVIDER_NAME;\nvar TOOLTIP_NAME = \"Tooltip\";\nvar [TooltipContextProvider, useTooltipContext] = createTooltipContext(TOOLTIP_NAME);\nvar Tooltip = (props)=>{\n    const { __scopeTooltip, children, open: openProp, defaultOpen, onOpenChange, disableHoverableContent: disableHoverableContentProp, delayDuration: delayDurationProp } = props;\n    const providerContext = useTooltipProviderContext(TOOLTIP_NAME, props.__scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const contentId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_4__.useId)();\n    const openTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const disableHoverableContent = disableHoverableContentProp ?? providerContext.disableHoverableContent;\n    const delayDuration = delayDurationProp ?? providerContext.delayDuration;\n    const wasOpenDelayedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_5__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: (open2)=>{\n            if (open2) {\n                providerContext.onOpen();\n                document.dispatchEvent(new CustomEvent(TOOLTIP_OPEN));\n            } else {\n                providerContext.onClose();\n            }\n            onOpenChange?.(open2);\n        },\n        caller: TOOLTIP_NAME\n    });\n    const stateAttribute = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>{\n        return open ? wasOpenDelayedRef.current ? \"delayed-open\" : \"instant-open\" : \"closed\";\n    }, [\n        open\n    ]);\n    const handleOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        wasOpenDelayedRef.current = false;\n        setOpen(true);\n    }, [\n        setOpen\n    ]);\n    const handleClose = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = 0;\n        setOpen(false);\n    }, [\n        setOpen\n    ]);\n    const handleDelayedOpen = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        window.clearTimeout(openTimerRef.current);\n        openTimerRef.current = window.setTimeout(()=>{\n            wasOpenDelayedRef.current = true;\n            setOpen(true);\n            openTimerRef.current = 0;\n        }, delayDuration);\n    }, [\n        delayDuration,\n        setOpen\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>{\n            if (openTimerRef.current) {\n                window.clearTimeout(openTimerRef.current);\n                openTimerRef.current = 0;\n            }\n        };\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContextProvider, {\n            scope: __scopeTooltip,\n            contentId,\n            open,\n            stateAttribute,\n            trigger,\n            onTriggerChange: setTrigger,\n            onTriggerEnter: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (providerContext.isOpenDelayedRef.current) handleDelayedOpen();\n                else handleOpen();\n            }, [\n                providerContext.isOpenDelayedRef,\n                handleDelayedOpen,\n                handleOpen\n            ]),\n            onTriggerLeave: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n                if (disableHoverableContent) {\n                    handleClose();\n                } else {\n                    window.clearTimeout(openTimerRef.current);\n                    openTimerRef.current = 0;\n                }\n            }, [\n                handleClose,\n                disableHoverableContent\n            ]),\n            onOpen: handleOpen,\n            onClose: handleClose,\n            disableHoverableContent,\n            children\n        })\n    });\n};\nTooltip.displayName = TOOLTIP_NAME;\nvar TRIGGER_NAME = \"TooltipTrigger\";\nvar TooltipTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...triggerProps } = props;\n    const context = useTooltipContext(TRIGGER_NAME, __scopeTooltip);\n    const providerContext = useTooltipProviderContext(TRIGGER_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref, context.onTriggerChange);\n    const isPointerDownRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerMoveOpenedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const handlePointerUp = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>isPointerDownRef.current = false, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>document.removeEventListener(\"pointerup\", handlePointerUp);\n    }, [\n        handlePointerUp\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n            \"aria-describedby\": context.open ? context.contentId : void 0,\n            \"data-state\": context.stateAttribute,\n            ...triggerProps,\n            ref: composedRefs,\n            onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerMove, (event)=>{\n                if (event.pointerType === \"touch\") return;\n                if (!hasPointerMoveOpenedRef.current && !providerContext.isPointerInTransitRef.current) {\n                    context.onTriggerEnter();\n                    hasPointerMoveOpenedRef.current = true;\n                }\n            }),\n            onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerLeave, ()=>{\n                context.onTriggerLeave();\n                hasPointerMoveOpenedRef.current = false;\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDown, ()=>{\n                if (context.open) {\n                    context.onClose();\n                }\n                isPointerDownRef.current = true;\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    once: true\n                });\n            }),\n            onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocus, ()=>{\n                if (!isPointerDownRef.current) context.onOpen();\n            }),\n            onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onBlur, context.onClose),\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onClose)\n        })\n    });\n});\nTooltipTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"TooltipPortal\";\nvar [PortalProvider, usePortalContext] = createTooltipContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar TooltipPortal = (props)=>{\n    const { __scopeTooltip, forceMount, children, container } = props;\n    const context = useTooltipContext(PORTAL_NAME, __scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopeTooltip,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nTooltipPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"TooltipContent\";\nvar TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopeTooltip);\n    const { forceMount = portalContext.forceMount, side = \"top\", ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.disableHoverableContent ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentHoverable, {\n            side,\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nvar TooltipContentHoverable = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useTooltipContext(CONTENT_NAME, props.__scopeTooltip);\n    const providerContext = useTooltipProviderContext(CONTENT_NAME, props.__scopeTooltip);\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, ref);\n    const [pointerGraceArea, setPointerGraceArea] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const { trigger, onClose } = context;\n    const content = ref.current;\n    const { onPointerInTransitChange } = providerContext;\n    const handleRemoveGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        setPointerGraceArea(null);\n        onPointerInTransitChange(false);\n    }, [\n        onPointerInTransitChange\n    ]);\n    const handleCreateGraceArea = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event, hoverTarget)=>{\n        const currentTarget = event.currentTarget;\n        const exitPoint = {\n            x: event.clientX,\n            y: event.clientY\n        };\n        const exitSide = getExitSideFromRect(exitPoint, currentTarget.getBoundingClientRect());\n        const paddedExitPoints = getPaddedExitPoints(exitPoint, exitSide);\n        const hoverTargetPoints = getPointsFromRect(hoverTarget.getBoundingClientRect());\n        const graceArea = getHull([\n            ...paddedExitPoints,\n            ...hoverTargetPoints\n        ]);\n        setPointerGraceArea(graceArea);\n        onPointerInTransitChange(true);\n    }, [\n        onPointerInTransitChange\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>handleRemoveGraceArea();\n    }, [\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trigger && content) {\n            const handleTriggerLeave = (event)=>handleCreateGraceArea(event, content);\n            const handleContentLeave = (event)=>handleCreateGraceArea(event, trigger);\n            trigger.addEventListener(\"pointerleave\", handleTriggerLeave);\n            content.addEventListener(\"pointerleave\", handleContentLeave);\n            return ()=>{\n                trigger.removeEventListener(\"pointerleave\", handleTriggerLeave);\n                content.removeEventListener(\"pointerleave\", handleContentLeave);\n            };\n        }\n    }, [\n        trigger,\n        content,\n        handleCreateGraceArea,\n        handleRemoveGraceArea\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (pointerGraceArea) {\n            const handleTrackPointerGrace = (event)=>{\n                const target = event.target;\n                const pointerPosition = {\n                    x: event.clientX,\n                    y: event.clientY\n                };\n                const hasEnteredTarget = trigger?.contains(target) || content?.contains(target);\n                const isPointerOutsideGraceArea = !isPointInPolygon(pointerPosition, pointerGraceArea);\n                if (hasEnteredTarget) {\n                    handleRemoveGraceArea();\n                } else if (isPointerOutsideGraceArea) {\n                    handleRemoveGraceArea();\n                    onClose();\n                }\n            };\n            document.addEventListener(\"pointermove\", handleTrackPointerGrace);\n            return ()=>document.removeEventListener(\"pointermove\", handleTrackPointerGrace);\n        }\n    }, [\n        trigger,\n        content,\n        pointerGraceArea,\n        onClose,\n        handleRemoveGraceArea\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(TooltipContentImpl, {\n        ...props,\n        ref: composedRefs\n    });\n});\nvar [VisuallyHiddenContentContextProvider, useVisuallyHiddenContentContext] = createTooltipContext(TOOLTIP_NAME, {\n    isInside: false\n});\nvar Slottable = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlottable)(\"TooltipContent\");\nvar TooltipContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, children, \"aria-label\": ariaLabel, onEscapeKeyDown, onPointerDownOutside, ...contentProps } = props;\n    const context = useTooltipContext(CONTENT_NAME, __scopeTooltip);\n    const popperScope = usePopperScope(__scopeTooltip);\n    const { onClose } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        document.addEventListener(TOOLTIP_OPEN, onClose);\n        return ()=>document.removeEventListener(TOOLTIP_OPEN, onClose);\n    }, [\n        onClose\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (context.trigger) {\n            const handleScroll = (event)=>{\n                const target = event.target;\n                if (target?.contains(context.trigger)) onClose();\n            };\n            window.addEventListener(\"scroll\", handleScroll, {\n                capture: true\n            });\n            return ()=>window.removeEventListener(\"scroll\", handleScroll, {\n                    capture: true\n                });\n        }\n    }, [\n        context.trigger,\n        onClose\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_12__.DismissableLayer, {\n        asChild: true,\n        disableOutsidePointerEvents: false,\n        onEscapeKeyDown,\n        onPointerDownOutside,\n        onFocusOutside: (event)=>event.preventDefault(),\n        onDismiss: onClose,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            \"data-state\": context.stateAttribute,\n            ...popperScope,\n            ...contentProps,\n            ref: forwardedRef,\n            style: {\n                ...contentProps.style,\n                // re-namespace exposed content custom properties\n                ...{\n                    \"--radix-tooltip-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                    \"--radix-tooltip-content-available-width\": \"var(--radix-popper-available-width)\",\n                    \"--radix-tooltip-content-available-height\": \"var(--radix-popper-available-height)\",\n                    \"--radix-tooltip-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                    \"--radix-tooltip-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                }\n            },\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Slottable, {\n                    children\n                }),\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(VisuallyHiddenContentContextProvider, {\n                    scope: __scopeTooltip,\n                    isInside: true,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_13__.Root, {\n                        id: context.contentId,\n                        role: \"tooltip\",\n                        children: ariaLabel || children\n                    })\n                })\n            ]\n        })\n    });\n});\nTooltipContent.displayName = CONTENT_NAME;\nvar ARROW_NAME = \"TooltipArrow\";\nvar TooltipArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeTooltip, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeTooltip);\n    const visuallyHiddenContentContext = useVisuallyHiddenContentContext(ARROW_NAME, __scopeTooltip);\n    return visuallyHiddenContentContext.isInside ? null : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nTooltipArrow.displayName = ARROW_NAME;\nfunction getExitSideFromRect(point, rect) {\n    const top = Math.abs(rect.top - point.y);\n    const bottom = Math.abs(rect.bottom - point.y);\n    const right = Math.abs(rect.right - point.x);\n    const left = Math.abs(rect.left - point.x);\n    switch(Math.min(top, bottom, right, left)){\n        case left:\n            return \"left\";\n        case right:\n            return \"right\";\n        case top:\n            return \"top\";\n        case bottom:\n            return \"bottom\";\n        default:\n            throw new Error(\"unreachable\");\n    }\n}\nfunction getPaddedExitPoints(exitPoint, exitSide, padding = 5) {\n    const paddedExitPoints = [];\n    switch(exitSide){\n        case \"top\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"bottom\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            });\n            break;\n        case \"left\":\n            paddedExitPoints.push({\n                x: exitPoint.x + padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x + padding,\n                y: exitPoint.y + padding\n            });\n            break;\n        case \"right\":\n            paddedExitPoints.push({\n                x: exitPoint.x - padding,\n                y: exitPoint.y - padding\n            }, {\n                x: exitPoint.x - padding,\n                y: exitPoint.y + padding\n            });\n            break;\n    }\n    return paddedExitPoints;\n}\nfunction getPointsFromRect(rect) {\n    const { top, right, bottom, left } = rect;\n    return [\n        {\n            x: left,\n            y: top\n        },\n        {\n            x: right,\n            y: top\n        },\n        {\n            x: right,\n            y: bottom\n        },\n        {\n            x: left,\n            y: bottom\n        }\n    ];\n}\nfunction isPointInPolygon(point, polygon) {\n    const { x, y } = point;\n    let inside = false;\n    for(let i = 0, j = polygon.length - 1; i < polygon.length; j = i++){\n        const ii = polygon[i];\n        const jj = polygon[j];\n        const xi = ii.x;\n        const yi = ii.y;\n        const xj = jj.x;\n        const yj = jj.y;\n        const intersect = yi > y !== yj > y && x < (xj - xi) * (y - yi) / (yj - yi) + xi;\n        if (intersect) inside = !inside;\n    }\n    return inside;\n}\nfunction getHull(points) {\n    const newPoints = points.slice();\n    newPoints.sort((a, b)=>{\n        if (a.x < b.x) return -1;\n        else if (a.x > b.x) return 1;\n        else if (a.y < b.y) return -1;\n        else if (a.y > b.y) return 1;\n        else return 0;\n    });\n    return getHullPresorted(newPoints);\n}\nfunction getHullPresorted(points) {\n    if (points.length <= 1) return points.slice();\n    const upperHull = [];\n    for(let i = 0; i < points.length; i++){\n        const p = points[i];\n        while(upperHull.length >= 2){\n            const q = upperHull[upperHull.length - 1];\n            const r = upperHull[upperHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) upperHull.pop();\n            else break;\n        }\n        upperHull.push(p);\n    }\n    upperHull.pop();\n    const lowerHull = [];\n    for(let i = points.length - 1; i >= 0; i--){\n        const p = points[i];\n        while(lowerHull.length >= 2){\n            const q = lowerHull[lowerHull.length - 1];\n            const r = lowerHull[lowerHull.length - 2];\n            if ((q.x - r.x) * (p.y - r.y) >= (q.y - r.y) * (p.x - r.x)) lowerHull.pop();\n            else break;\n        }\n        lowerHull.push(p);\n    }\n    lowerHull.pop();\n    if (upperHull.length === 1 && lowerHull.length === 1 && upperHull[0].x === lowerHull[0].x && upperHull[0].y === lowerHull[0].y) {\n        return upperHull;\n    } else {\n        return upperHull.concat(lowerHull);\n    }\n}\nvar Provider = TooltipProvider;\nvar Root3 = Tooltip;\nvar Trigger = TooltipTrigger;\nvar Portal = TooltipPortal;\nvar Content2 = TooltipContent;\nvar Arrow2 = TooltipArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-tooltip@1.2_50e390c4dabde08ed3112eb9f58da500/node_modules/@radix-ui/react-tooltip/dist/index.mjs\n");

/***/ })

};
;