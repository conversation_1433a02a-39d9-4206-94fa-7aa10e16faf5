"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/simple-fuel-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/reporting/simple-fuel-report.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleFuelReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n// Create columns for the fuel report table\nconst createFuelReportColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-medium\",\n                    children: item.vesselName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 51,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"logbookDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Log Entry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/M/YY\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 63,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelTankName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Tank\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelTankName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelStart\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Start\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelStart.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelAdded\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Added\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 93,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelAdded.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelEnd\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel End\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 105,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelEnd.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelUsed\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Used\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelUsed.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 123,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"comments\",\n            header: \"Comments\",\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.comments || \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 133,\n                    columnNumber: 24\n                }, undefined);\n            }\n        }\n    ]);\n// Custom export actions component for the fuel report\nfunction FuelReportExportActions(param) {\n    let { onDownloadCsv, onDownloadPdf } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex gap-2\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                onClick: onDownloadCsv,\n                children: \"Export CSV\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 148,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                variant: \"outline\",\n                size: \"sm\",\n                onClick: onDownloadPdf,\n                children: \"Export PDF\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 151,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 147,\n        columnNumber: 9\n    }, this);\n}\n_c = FuelReportExportActions;\nfunction SimpleFuelReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    // Create columns for the table\n    const columns = createFuelReportColumns();\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery)(_app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__.GET_SIMPLE_FUEL_REPORT_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            filter[\"startDate\"] = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"vehicleID\"] = {\n                in: selectedVessels.map((v)=>v.value)\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readLogBookEntries;\n        var _data_readLogBookEntries_nodes;\n        const fetchedData = (_data_readLogBookEntries_nodes = data === null || data === void 0 ? void 0 : (_data_readLogBookEntries = data.readLogBookEntries) === null || _data_readLogBookEntries === void 0 ? void 0 : _data_readLogBookEntries.nodes) !== null && _data_readLogBookEntries_nodes !== void 0 ? _data_readLogBookEntries_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const filteredItems = fetchedData.filter(function(item) {\n            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== \"0\";\n        });\n        if (filteredItems.length === 0) {\n            return [];\n        }\n        const items = [];\n        filteredItems.forEach((item)=>{\n            if (item.state !== \"Locked\") {\n                return;\n            }\n            const logBookDate = new Date(item.startDate);\n            const vessel = item.vehicle;\n            const fuelLogs = item.fuelLog.nodes.filter((item)=>item.fuelTank.id != 0);\n            const fuelTanks = fuelLogs.reduce((acc, log)=>{\n                return {\n                    ...acc,\n                    [log.fuelTank.id]: log.fuelTank.title\n                };\n            }, {});\n            const logBookEntrySections = item.logBookEntrySections.nodes;\n            const tripEvents = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.tripEvents.nodes\n                ];\n            }, []);\n            const sectionMemberComments = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.sectionMemberComments.nodes\n                ];\n            }, []).map((sectionComment)=>sectionComment === null || sectionComment === void 0 ? void 0 : sectionComment.comment).filter((value)=>value != null || value != \"\");\n            for(const key in fuelTanks){\n                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {\n                    const fuelTankName = fuelTanks[key];\n                    const fuelTankLogs = fuelLogs.filter((item)=>item.fuelTankID == key);\n                    const fuelLogStart = fuelTankLogs[0];\n                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1];\n                    var _fuelLogStart_fuelBefore;\n                    const fuelStart = (_fuelLogStart_fuelBefore = fuelLogStart === null || fuelLogStart === void 0 ? void 0 : fuelLogStart.fuelBefore) !== null && _fuelLogStart_fuelBefore !== void 0 ? _fuelLogStart_fuelBefore : 0;\n                    const fuelAdded = calculateFuelAddedFromTripEvents(tripEvents, key);\n                    var _fuelLogEnd_fuelAfter;\n                    const fuelEnd = (_fuelLogEnd_fuelAfter = fuelLogEnd === null || fuelLogEnd === void 0 ? void 0 : fuelLogEnd.fuelAfter) !== null && _fuelLogEnd_fuelAfter !== void 0 ? _fuelLogEnd_fuelAfter : 0;\n                    const fuelUsed = fuelStart + fuelAdded - fuelEnd;\n                    const reportItem = {\n                        logBookEntryID: item.id,\n                        vesselID: vessel.id,\n                        logbookDate: logBookDate,\n                        vesselName: vessel.title,\n                        fuelTankID: Number(key),\n                        fuelTankName: fuelTankName,\n                        fuelStart,\n                        fuelAdded,\n                        fuelEnd,\n                        fuelUsed,\n                        comments: sectionMemberComments.join(\", \")\n                    };\n                    items.push(reportItem);\n                }\n            }\n        });\n        return items;\n    }, [\n        data,\n        called,\n        loading\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"vessel\",\n            \"log entry\",\n            \"fuel tank\",\n            \"fuel start\",\n            \"fuel added\",\n            \"fuel end\",\n            \"fuel used\",\n            \"comments\"\n        ]);\n        reportData.forEach((item)=>{\n            var _item_comments;\n            csvEntries.push([\n                item.vesselName,\n                item.logbookDate.toISOString(),\n                item.fuelTankName,\n                item.fuelStart,\n                item.fuelAdded,\n                item.fuelEnd,\n                item.fuelUsed,\n                (_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\"\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const data = reportData.map(function(item) {\n            var _item_comments;\n            return [\n                item.vesselName + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/MM/YY\") + \"\",\n                item.fuelTankName + \"\",\n                item.fuelStart.toLocaleString(),\n                item.fuelAdded.toLocaleString(),\n                item.fuelEnd.toLocaleString(),\n                item.fuelUsed.toLocaleString(),\n                \"\".concat((_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\", \" \")\n            ];\n        });\n        const totalUsedFuel = reportData.reduce((acc, current)=>acc + current.fuelUsed, 0);\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers: [\n                [\n                    {\n                        content: \"Vessel\"\n                    },\n                    {\n                        content: \"Log Entry\"\n                    },\n                    {\n                        content: \"Fuel Tank\"\n                    },\n                    {\n                        content: \"Fuel Start\"\n                    },\n                    {\n                        content: \"Fuel Added\"\n                    },\n                    {\n                        content: \"Fuel End\"\n                    },\n                    {\n                        content: \"Fuel Used\"\n                    },\n                    {\n                        content: \"Comments\",\n                        styles: {\n                            cellWidth: 60\n                        }\n                    }\n                ]\n            ],\n            body: data,\n            footers: [\n                [\n                    {\n                        colSpan: 6,\n                        content: \"Total Fuel Used\"\n                    },\n                    {\n                        content: totalUsedFuel.toLocaleString()\n                    },\n                    {\n                        content: \"\"\n                    }\n                ]\n            ],\n            userOptions: {\n                showFoot: \"lastPage\"\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ListHeader, {\n                title: \"Simple Fuel Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 408,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 404,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                        className: \"gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                                onChange: handleFilterOnChange,\n                                onClick: generateReport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExportButton, {\n                                onDownloadPdf: downloadPdf,\n                                onDownloadCsv: downloadCsv\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 417,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                                        children: tableHeadings.map((heading)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: heading\n                                            }, heading, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                    isLoading: called && loading,\n                                    reportData: reportData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 439,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 428,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 416,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SimpleFuelReport, \"4Sq0Rys+Pi85vYpHZIdEH/CExw8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_11__.useLazyQuery\n    ];\n});\n_c1 = SimpleFuelReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    _s1();\n    const totalFuelUsed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return reportData.reduce((acc, current)=>current.fuelUsed + acc, 0);\n    }, [\n        reportData\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: tableHeadings.length,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 467,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n            lineNumber: 466,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: tableHeadings.length,\n                className: \"text-center  h-32\",\n                children: \"No Data Available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 479,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n            lineNumber: 478,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableBody, {\n                children: reportData.map((element, index)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                        className: \"group border-b  hover: \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[15%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block ml-3\",\n                                    children: element.vesselName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 497,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 496,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.logbookDate).format(\"DD/M/YY\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 502,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelTankName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 509,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 508,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelStart.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 514,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 513,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelAdded.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 519,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 518,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelEnd.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 523,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelUsed.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 529,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 528,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[25%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.comments\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 534,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 533,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, \"\".concat(element.logBookEntryID, \"-\").concat(element.fuelTankID, \"-\").concat(element.vesselID), true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 493,\n                        columnNumber: 25\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 490,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                    className: \"group border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left\",\n                            scope: \"col\",\n                            colSpan: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block ml-3\",\n                                children: \"Total Fuel Used\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 548,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 544,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left w-[10%]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block \",\n                                children: totalFuelUsed.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left w-[25%]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 543,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 542,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TableContent, \"7uTdTl0jLv1kvQbg8+bH+v904uQ=\");\n_c2 = TableContent;\nfunction getFuelAddedByFuelTankID(fuelLogs, fuelTankID) {\n    if (fuelLogs.length === 0) {\n        return 0;\n    }\n    const fuelTankLogs = fuelLogs.filter((log)=>log.fuelTankID == fuelTankID);\n    return fuelTankLogs.reduce((acc, log)=>acc + log.fuelAdded, 0);\n}\nfunction calculateFuelAddedFromTripEvents(tripEvents, fuelTankID) {\n    const fuelAddedLogs = tripEvents.map(function(tripEvent) {\n        if (tripEvent.eventCategory === \"RefuellingBunkering\") {\n            const fuelLogs = tripEvent.eventType_RefuellingBunkering.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"Tasking\") {\n            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"PassengerDropFacility\") {\n            const fuelLogs = tripEvent.eventType_PassengerDropFacility.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        return 0;\n    });\n    return fuelAddedLogs.reduce((acc, val)=>acc + val, 0);\n}\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"FuelReportExportActions\");\n$RefreshReg$(_c1, \"SimpleFuelReport\");\n$RefreshReg$(_c2, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx\n"));

/***/ })

});