"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/svg-pathdata@6.0.3";
exports.ids = ["vendor-chunks/svg-pathdata@6.0.3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/svg-pathdata@6.0.3/node_modules/svg-pathdata/lib/SVGPathData.module.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/.pnpm/svg-pathdata@6.0.3/node_modules/svg-pathdata/lib/SVGPathData.module.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   COMMAND_ARG_COUNTS: () => (/* binding */ N),\n/* harmony export */   SVGPathData: () => (/* binding */ _),\n/* harmony export */   SVGPathDataParser: () => (/* binding */ f),\n/* harmony export */   SVGPathDataTransformer: () => (/* binding */ u),\n/* harmony export */   encodeSVGPath: () => (/* binding */ e)\n/* harmony export */ });\n/*! *****************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\nvar t=function(r,e){return(t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,r){t.__proto__=r}||function(t,r){for(var e in r)Object.prototype.hasOwnProperty.call(r,e)&&(t[e]=r[e])})(r,e)};function r(r,e){if(\"function\"!=typeof e&&null!==e)throw new TypeError(\"Class extends value \"+String(e)+\" is not a constructor or null\");function i(){this.constructor=r}t(r,e),r.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}function e(t){var r=\"\";Array.isArray(t)||(t=[t]);for(var e=0;e<t.length;e++){var i=t[e];if(i.type===_.CLOSE_PATH)r+=\"z\";else if(i.type===_.HORIZ_LINE_TO)r+=(i.relative?\"h\":\"H\")+i.x;else if(i.type===_.VERT_LINE_TO)r+=(i.relative?\"v\":\"V\")+i.y;else if(i.type===_.MOVE_TO)r+=(i.relative?\"m\":\"M\")+i.x+\" \"+i.y;else if(i.type===_.LINE_TO)r+=(i.relative?\"l\":\"L\")+i.x+\" \"+i.y;else if(i.type===_.CURVE_TO)r+=(i.relative?\"c\":\"C\")+i.x1+\" \"+i.y1+\" \"+i.x2+\" \"+i.y2+\" \"+i.x+\" \"+i.y;else if(i.type===_.SMOOTH_CURVE_TO)r+=(i.relative?\"s\":\"S\")+i.x2+\" \"+i.y2+\" \"+i.x+\" \"+i.y;else if(i.type===_.QUAD_TO)r+=(i.relative?\"q\":\"Q\")+i.x1+\" \"+i.y1+\" \"+i.x+\" \"+i.y;else if(i.type===_.SMOOTH_QUAD_TO)r+=(i.relative?\"t\":\"T\")+i.x+\" \"+i.y;else{if(i.type!==_.ARC)throw new Error('Unexpected command type \"'+i.type+'\" at index '+e+\".\");r+=(i.relative?\"a\":\"A\")+i.rX+\" \"+i.rY+\" \"+i.xRot+\" \"+ +i.lArcFlag+\" \"+ +i.sweepFlag+\" \"+i.x+\" \"+i.y}}return r}function i(t,r){var e=t[0],i=t[1];return[e*Math.cos(r)-i*Math.sin(r),e*Math.sin(r)+i*Math.cos(r)]}function a(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var e=0;e<t.length;e++)if(\"number\"!=typeof t[e])throw new Error(\"assertNumbers arguments[\"+e+\"] is not a number. \"+typeof t[e]+\" == typeof \"+t[e]);return!0}var n=Math.PI;function o(t,r,e){t.lArcFlag=0===t.lArcFlag?0:1,t.sweepFlag=0===t.sweepFlag?0:1;var a=t.rX,o=t.rY,s=t.x,u=t.y;a=Math.abs(t.rX),o=Math.abs(t.rY);var h=i([(r-s)/2,(e-u)/2],-t.xRot/180*n),c=h[0],y=h[1],p=Math.pow(c,2)/Math.pow(a,2)+Math.pow(y,2)/Math.pow(o,2);1<p&&(a*=Math.sqrt(p),o*=Math.sqrt(p)),t.rX=a,t.rY=o;var m=Math.pow(a,2)*Math.pow(y,2)+Math.pow(o,2)*Math.pow(c,2),O=(t.lArcFlag!==t.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(a,2)*Math.pow(o,2)-m)/m)),l=a*y/o*O,T=-o*c/a*O,v=i([l,T],t.xRot/180*n);t.cX=v[0]+(r+s)/2,t.cY=v[1]+(e+u)/2,t.phi1=Math.atan2((y-T)/o,(c-l)/a),t.phi2=Math.atan2((-y-T)/o,(-c-l)/a),0===t.sweepFlag&&t.phi2>t.phi1&&(t.phi2-=2*n),1===t.sweepFlag&&t.phi2<t.phi1&&(t.phi2+=2*n),t.phi1*=180/n,t.phi2*=180/n}function s(t,r,e){a(t,r,e);var i=t*t+r*r-e*e;if(0>i)return[];if(0===i)return[[t*e/(t*t+r*r),r*e/(t*t+r*r)]];var n=Math.sqrt(i);return[[(t*e+r*n)/(t*t+r*r),(r*e-t*n)/(t*t+r*r)],[(t*e-r*n)/(t*t+r*r),(r*e+t*n)/(t*t+r*r)]]}var u,h=Math.PI/180;function c(t,r,e){return(1-e)*t+e*r}function y(t,r,e,i){return t+Math.cos(i/180*n)*r+Math.sin(i/180*n)*e}function p(t,r,e,i){var a=1e-6,n=r-t,o=e-r,s=3*n+3*(i-e)-6*o,u=6*(o-n),h=3*n;return Math.abs(s)<a?[-h/u]:function(t,r,e){void 0===e&&(e=1e-6);var i=t*t/4-r;if(i<-e)return[];if(i<=e)return[-t/2];var a=Math.sqrt(i);return[-t/2-a,-t/2+a]}(u/s,h/s,a)}function m(t,r,e,i,a){var n=1-a;return t*(n*n*n)+r*(3*n*n*a)+e*(3*n*a*a)+i*(a*a*a)}!function(t){function r(){return u((function(t,r,e){return t.relative&&(void 0!==t.x1&&(t.x1+=r),void 0!==t.y1&&(t.y1+=e),void 0!==t.x2&&(t.x2+=r),void 0!==t.y2&&(t.y2+=e),void 0!==t.x&&(t.x+=r),void 0!==t.y&&(t.y+=e),t.relative=!1),t}))}function e(){var t=NaN,r=NaN,e=NaN,i=NaN;return u((function(a,n,o){return a.type&_.SMOOTH_CURVE_TO&&(a.type=_.CURVE_TO,t=isNaN(t)?n:t,r=isNaN(r)?o:r,a.x1=a.relative?n-t:2*n-t,a.y1=a.relative?o-r:2*o-r),a.type&_.CURVE_TO?(t=a.relative?n+a.x2:a.x2,r=a.relative?o+a.y2:a.y2):(t=NaN,r=NaN),a.type&_.SMOOTH_QUAD_TO&&(a.type=_.QUAD_TO,e=isNaN(e)?n:e,i=isNaN(i)?o:i,a.x1=a.relative?n-e:2*n-e,a.y1=a.relative?o-i:2*o-i),a.type&_.QUAD_TO?(e=a.relative?n+a.x1:a.x1,i=a.relative?o+a.y1:a.y1):(e=NaN,i=NaN),a}))}function n(){var t=NaN,r=NaN;return u((function(e,i,a){if(e.type&_.SMOOTH_QUAD_TO&&(e.type=_.QUAD_TO,t=isNaN(t)?i:t,r=isNaN(r)?a:r,e.x1=e.relative?i-t:2*i-t,e.y1=e.relative?a-r:2*a-r),e.type&_.QUAD_TO){t=e.relative?i+e.x1:e.x1,r=e.relative?a+e.y1:e.y1;var n=e.x1,o=e.y1;e.type=_.CURVE_TO,e.x1=((e.relative?0:i)+2*n)/3,e.y1=((e.relative?0:a)+2*o)/3,e.x2=(e.x+2*n)/3,e.y2=(e.y+2*o)/3}else t=NaN,r=NaN;return e}))}function u(t){var r=0,e=0,i=NaN,a=NaN;return function(n){if(isNaN(i)&&!(n.type&_.MOVE_TO))throw new Error(\"path must start with moveto\");var o=t(n,r,e,i,a);return n.type&_.CLOSE_PATH&&(r=i,e=a),void 0!==n.x&&(r=n.relative?r+n.x:n.x),void 0!==n.y&&(e=n.relative?e+n.y:n.y),n.type&_.MOVE_TO&&(i=r,a=e),o}}function O(t,r,e,i,n,o){return a(t,r,e,i,n,o),u((function(a,s,u,h){var c=a.x1,y=a.x2,p=a.relative&&!isNaN(h),m=void 0!==a.x?a.x:p?0:s,O=void 0!==a.y?a.y:p?0:u;function l(t){return t*t}a.type&_.HORIZ_LINE_TO&&0!==r&&(a.type=_.LINE_TO,a.y=a.relative?0:u),a.type&_.VERT_LINE_TO&&0!==e&&(a.type=_.LINE_TO,a.x=a.relative?0:s),void 0!==a.x&&(a.x=a.x*t+O*e+(p?0:n)),void 0!==a.y&&(a.y=m*r+a.y*i+(p?0:o)),void 0!==a.x1&&(a.x1=a.x1*t+a.y1*e+(p?0:n)),void 0!==a.y1&&(a.y1=c*r+a.y1*i+(p?0:o)),void 0!==a.x2&&(a.x2=a.x2*t+a.y2*e+(p?0:n)),void 0!==a.y2&&(a.y2=y*r+a.y2*i+(p?0:o));var T=t*i-r*e;if(void 0!==a.xRot&&(1!==t||0!==r||0!==e||1!==i))if(0===T)delete a.rX,delete a.rY,delete a.xRot,delete a.lArcFlag,delete a.sweepFlag,a.type=_.LINE_TO;else{var v=a.xRot*Math.PI/180,f=Math.sin(v),N=Math.cos(v),x=1/l(a.rX),d=1/l(a.rY),E=l(N)*x+l(f)*d,A=2*f*N*(x-d),C=l(f)*x+l(N)*d,M=E*i*i-A*r*i+C*r*r,R=A*(t*i+r*e)-2*(E*e*i+C*t*r),g=E*e*e-A*t*e+C*t*t,I=(Math.atan2(R,M-g)+Math.PI)%Math.PI/2,S=Math.sin(I),L=Math.cos(I);a.rX=Math.abs(T)/Math.sqrt(M*l(L)+R*S*L+g*l(S)),a.rY=Math.abs(T)/Math.sqrt(M*l(S)-R*S*L+g*l(L)),a.xRot=180*I/Math.PI}return void 0!==a.sweepFlag&&0>T&&(a.sweepFlag=+!a.sweepFlag),a}))}function l(){return function(t){var r={};for(var e in t)r[e]=t[e];return r}}t.ROUND=function(t){function r(r){return Math.round(r*t)/t}return void 0===t&&(t=1e13),a(t),function(t){return void 0!==t.x1&&(t.x1=r(t.x1)),void 0!==t.y1&&(t.y1=r(t.y1)),void 0!==t.x2&&(t.x2=r(t.x2)),void 0!==t.y2&&(t.y2=r(t.y2)),void 0!==t.x&&(t.x=r(t.x)),void 0!==t.y&&(t.y=r(t.y)),void 0!==t.rX&&(t.rX=r(t.rX)),void 0!==t.rY&&(t.rY=r(t.rY)),t}},t.TO_ABS=r,t.TO_REL=function(){return u((function(t,r,e){return t.relative||(void 0!==t.x1&&(t.x1-=r),void 0!==t.y1&&(t.y1-=e),void 0!==t.x2&&(t.x2-=r),void 0!==t.y2&&(t.y2-=e),void 0!==t.x&&(t.x-=r),void 0!==t.y&&(t.y-=e),t.relative=!0),t}))},t.NORMALIZE_HVZ=function(t,r,e){return void 0===t&&(t=!0),void 0===r&&(r=!0),void 0===e&&(e=!0),u((function(i,a,n,o,s){if(isNaN(o)&&!(i.type&_.MOVE_TO))throw new Error(\"path must start with moveto\");return r&&i.type&_.HORIZ_LINE_TO&&(i.type=_.LINE_TO,i.y=i.relative?0:n),e&&i.type&_.VERT_LINE_TO&&(i.type=_.LINE_TO,i.x=i.relative?0:a),t&&i.type&_.CLOSE_PATH&&(i.type=_.LINE_TO,i.x=i.relative?o-a:o,i.y=i.relative?s-n:s),i.type&_.ARC&&(0===i.rX||0===i.rY)&&(i.type=_.LINE_TO,delete i.rX,delete i.rY,delete i.xRot,delete i.lArcFlag,delete i.sweepFlag),i}))},t.NORMALIZE_ST=e,t.QT_TO_C=n,t.INFO=u,t.SANITIZE=function(t){void 0===t&&(t=0),a(t);var r=NaN,e=NaN,i=NaN,n=NaN;return u((function(a,o,s,u,h){var c=Math.abs,y=!1,p=0,m=0;if(a.type&_.SMOOTH_CURVE_TO&&(p=isNaN(r)?0:o-r,m=isNaN(e)?0:s-e),a.type&(_.CURVE_TO|_.SMOOTH_CURVE_TO)?(r=a.relative?o+a.x2:a.x2,e=a.relative?s+a.y2:a.y2):(r=NaN,e=NaN),a.type&_.SMOOTH_QUAD_TO?(i=isNaN(i)?o:2*o-i,n=isNaN(n)?s:2*s-n):a.type&_.QUAD_TO?(i=a.relative?o+a.x1:a.x1,n=a.relative?s+a.y1:a.y2):(i=NaN,n=NaN),a.type&_.LINE_COMMANDS||a.type&_.ARC&&(0===a.rX||0===a.rY||!a.lArcFlag)||a.type&_.CURVE_TO||a.type&_.SMOOTH_CURVE_TO||a.type&_.QUAD_TO||a.type&_.SMOOTH_QUAD_TO){var O=void 0===a.x?0:a.relative?a.x:a.x-o,l=void 0===a.y?0:a.relative?a.y:a.y-s;p=isNaN(i)?void 0===a.x1?p:a.relative?a.x:a.x1-o:i-o,m=isNaN(n)?void 0===a.y1?m:a.relative?a.y:a.y1-s:n-s;var T=void 0===a.x2?0:a.relative?a.x:a.x2-o,v=void 0===a.y2?0:a.relative?a.y:a.y2-s;c(O)<=t&&c(l)<=t&&c(p)<=t&&c(m)<=t&&c(T)<=t&&c(v)<=t&&(y=!0)}return a.type&_.CLOSE_PATH&&c(o-u)<=t&&c(s-h)<=t&&(y=!0),y?[]:a}))},t.MATRIX=O,t.ROTATE=function(t,r,e){void 0===r&&(r=0),void 0===e&&(e=0),a(t,r,e);var i=Math.sin(t),n=Math.cos(t);return O(n,i,-i,n,r-r*n+e*i,e-r*i-e*n)},t.TRANSLATE=function(t,r){return void 0===r&&(r=0),a(t,r),O(1,0,0,1,t,r)},t.SCALE=function(t,r){return void 0===r&&(r=t),a(t,r),O(t,0,0,r,0,0)},t.SKEW_X=function(t){return a(t),O(1,0,Math.atan(t),1,0,0)},t.SKEW_Y=function(t){return a(t),O(1,Math.atan(t),0,1,0,0)},t.X_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),a(t),O(-1,0,0,1,t,0)},t.Y_AXIS_SYMMETRY=function(t){return void 0===t&&(t=0),a(t),O(1,0,0,-1,0,t)},t.A_TO_C=function(){return u((function(t,r,e){return _.ARC===t.type?function(t,r,e){var a,n,s,u;t.cX||o(t,r,e);for(var y=Math.min(t.phi1,t.phi2),p=Math.max(t.phi1,t.phi2)-y,m=Math.ceil(p/90),O=new Array(m),l=r,T=e,v=0;v<m;v++){var f=c(t.phi1,t.phi2,v/m),N=c(t.phi1,t.phi2,(v+1)/m),x=N-f,d=4/3*Math.tan(x*h/4),E=[Math.cos(f*h)-d*Math.sin(f*h),Math.sin(f*h)+d*Math.cos(f*h)],A=E[0],C=E[1],M=[Math.cos(N*h),Math.sin(N*h)],R=M[0],g=M[1],I=[R+d*Math.sin(N*h),g-d*Math.cos(N*h)],S=I[0],L=I[1];O[v]={relative:t.relative,type:_.CURVE_TO};var H=function(r,e){var a=i([r*t.rX,e*t.rY],t.xRot),n=a[0],o=a[1];return[t.cX+n,t.cY+o]};a=H(A,C),O[v].x1=a[0],O[v].y1=a[1],n=H(S,L),O[v].x2=n[0],O[v].y2=n[1],s=H(R,g),O[v].x=s[0],O[v].y=s[1],t.relative&&(O[v].x1-=l,O[v].y1-=T,O[v].x2-=l,O[v].y2-=T,O[v].x-=l,O[v].y-=T),l=(u=[O[v].x,O[v].y])[0],T=u[1]}return O}(t,t.relative?0:r,t.relative?0:e):t}))},t.ANNOTATE_ARCS=function(){return u((function(t,r,e){return t.relative&&(r=0,e=0),_.ARC===t.type&&o(t,r,e),t}))},t.CLONE=l,t.CALCULATE_BOUNDS=function(){var t=function(t){var r={};for(var e in t)r[e]=t[e];return r},i=r(),a=n(),h=e(),c=u((function(r,e,n){var u=h(a(i(t(r))));function O(t){t>c.maxX&&(c.maxX=t),t<c.minX&&(c.minX=t)}function l(t){t>c.maxY&&(c.maxY=t),t<c.minY&&(c.minY=t)}if(u.type&_.DRAWING_COMMANDS&&(O(e),l(n)),u.type&_.HORIZ_LINE_TO&&O(u.x),u.type&_.VERT_LINE_TO&&l(u.y),u.type&_.LINE_TO&&(O(u.x),l(u.y)),u.type&_.CURVE_TO){O(u.x),l(u.y);for(var T=0,v=p(e,u.x1,u.x2,u.x);T<v.length;T++){0<(w=v[T])&&1>w&&O(m(e,u.x1,u.x2,u.x,w))}for(var f=0,N=p(n,u.y1,u.y2,u.y);f<N.length;f++){0<(w=N[f])&&1>w&&l(m(n,u.y1,u.y2,u.y,w))}}if(u.type&_.ARC){O(u.x),l(u.y),o(u,e,n);for(var x=u.xRot/180*Math.PI,d=Math.cos(x)*u.rX,E=Math.sin(x)*u.rX,A=-Math.sin(x)*u.rY,C=Math.cos(x)*u.rY,M=u.phi1<u.phi2?[u.phi1,u.phi2]:-180>u.phi2?[u.phi2+360,u.phi1+360]:[u.phi2,u.phi1],R=M[0],g=M[1],I=function(t){var r=t[0],e=t[1],i=180*Math.atan2(e,r)/Math.PI;return i<R?i+360:i},S=0,L=s(A,-d,0).map(I);S<L.length;S++){(w=L[S])>R&&w<g&&O(y(u.cX,d,A,w))}for(var H=0,U=s(C,-E,0).map(I);H<U.length;H++){var w;(w=U[H])>R&&w<g&&l(y(u.cY,E,C,w))}}return r}));return c.minX=1/0,c.maxX=-1/0,c.minY=1/0,c.maxY=-1/0,c}}(u||(u={}));var O,l=function(){function t(){}return t.prototype.round=function(t){return this.transform(u.ROUND(t))},t.prototype.toAbs=function(){return this.transform(u.TO_ABS())},t.prototype.toRel=function(){return this.transform(u.TO_REL())},t.prototype.normalizeHVZ=function(t,r,e){return this.transform(u.NORMALIZE_HVZ(t,r,e))},t.prototype.normalizeST=function(){return this.transform(u.NORMALIZE_ST())},t.prototype.qtToC=function(){return this.transform(u.QT_TO_C())},t.prototype.aToC=function(){return this.transform(u.A_TO_C())},t.prototype.sanitize=function(t){return this.transform(u.SANITIZE(t))},t.prototype.translate=function(t,r){return this.transform(u.TRANSLATE(t,r))},t.prototype.scale=function(t,r){return this.transform(u.SCALE(t,r))},t.prototype.rotate=function(t,r,e){return this.transform(u.ROTATE(t,r,e))},t.prototype.matrix=function(t,r,e,i,a,n){return this.transform(u.MATRIX(t,r,e,i,a,n))},t.prototype.skewX=function(t){return this.transform(u.SKEW_X(t))},t.prototype.skewY=function(t){return this.transform(u.SKEW_Y(t))},t.prototype.xSymmetry=function(t){return this.transform(u.X_AXIS_SYMMETRY(t))},t.prototype.ySymmetry=function(t){return this.transform(u.Y_AXIS_SYMMETRY(t))},t.prototype.annotateArcs=function(){return this.transform(u.ANNOTATE_ARCS())},t}(),T=function(t){return\" \"===t||\"\\t\"===t||\"\\r\"===t||\"\\n\"===t},v=function(t){return\"0\".charCodeAt(0)<=t.charCodeAt(0)&&t.charCodeAt(0)<=\"9\".charCodeAt(0)},f=function(t){function e(){var r=t.call(this)||this;return r.curNumber=\"\",r.curCommandType=-1,r.curCommandRelative=!1,r.canParseCommandOrComma=!0,r.curNumberHasExp=!1,r.curNumberHasExpDigits=!1,r.curNumberHasDecimal=!1,r.curArgs=[],r}return r(e,t),e.prototype.finish=function(t){if(void 0===t&&(t=[]),this.parse(\" \",t),0!==this.curArgs.length||!this.canParseCommandOrComma)throw new SyntaxError(\"Unterminated command at the path end.\");return t},e.prototype.parse=function(t,r){var e=this;void 0===r&&(r=[]);for(var i=function(t){r.push(t),e.curArgs.length=0,e.canParseCommandOrComma=!0},a=0;a<t.length;a++){var n=t[a],o=!(this.curCommandType!==_.ARC||3!==this.curArgs.length&&4!==this.curArgs.length||1!==this.curNumber.length||\"0\"!==this.curNumber&&\"1\"!==this.curNumber),s=v(n)&&(\"0\"===this.curNumber&&\"0\"===n||o);if(!v(n)||s)if(\"e\"!==n&&\"E\"!==n)if(\"-\"!==n&&\"+\"!==n||!this.curNumberHasExp||this.curNumberHasExpDigits)if(\".\"!==n||this.curNumberHasExp||this.curNumberHasDecimal||o){if(this.curNumber&&-1!==this.curCommandType){var u=Number(this.curNumber);if(isNaN(u))throw new SyntaxError(\"Invalid number ending at \"+a);if(this.curCommandType===_.ARC)if(0===this.curArgs.length||1===this.curArgs.length){if(0>u)throw new SyntaxError('Expected positive number, got \"'+u+'\" at index \"'+a+'\"')}else if((3===this.curArgs.length||4===this.curArgs.length)&&\"0\"!==this.curNumber&&\"1\"!==this.curNumber)throw new SyntaxError('Expected a flag, got \"'+this.curNumber+'\" at index \"'+a+'\"');this.curArgs.push(u),this.curArgs.length===N[this.curCommandType]&&(_.HORIZ_LINE_TO===this.curCommandType?i({type:_.HORIZ_LINE_TO,relative:this.curCommandRelative,x:u}):_.VERT_LINE_TO===this.curCommandType?i({type:_.VERT_LINE_TO,relative:this.curCommandRelative,y:u}):this.curCommandType===_.MOVE_TO||this.curCommandType===_.LINE_TO||this.curCommandType===_.SMOOTH_QUAD_TO?(i({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),_.MOVE_TO===this.curCommandType&&(this.curCommandType=_.LINE_TO)):this.curCommandType===_.CURVE_TO?i({type:_.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===_.SMOOTH_CURVE_TO?i({type:_.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===_.QUAD_TO?i({type:_.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===_.ARC&&i({type:_.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber=\"\",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!T(n))if(\",\"===n&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if(\"+\"!==n&&\"-\"!==n&&\".\"!==n)if(s)this.curNumber=n,this.curNumberHasDecimal=!1;else{if(0!==this.curArgs.length)throw new SyntaxError(\"Unterminated command at index \"+a+\".\");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character \"'+n+'\" at index '+a+\". Command cannot follow comma\");if(this.canParseCommandOrComma=!1,\"z\"!==n&&\"Z\"!==n)if(\"h\"===n||\"H\"===n)this.curCommandType=_.HORIZ_LINE_TO,this.curCommandRelative=\"h\"===n;else if(\"v\"===n||\"V\"===n)this.curCommandType=_.VERT_LINE_TO,this.curCommandRelative=\"v\"===n;else if(\"m\"===n||\"M\"===n)this.curCommandType=_.MOVE_TO,this.curCommandRelative=\"m\"===n;else if(\"l\"===n||\"L\"===n)this.curCommandType=_.LINE_TO,this.curCommandRelative=\"l\"===n;else if(\"c\"===n||\"C\"===n)this.curCommandType=_.CURVE_TO,this.curCommandRelative=\"c\"===n;else if(\"s\"===n||\"S\"===n)this.curCommandType=_.SMOOTH_CURVE_TO,this.curCommandRelative=\"s\"===n;else if(\"q\"===n||\"Q\"===n)this.curCommandType=_.QUAD_TO,this.curCommandRelative=\"q\"===n;else if(\"t\"===n||\"T\"===n)this.curCommandType=_.SMOOTH_QUAD_TO,this.curCommandRelative=\"t\"===n;else{if(\"a\"!==n&&\"A\"!==n)throw new SyntaxError('Unexpected character \"'+n+'\" at index '+a+\".\");this.curCommandType=_.ARC,this.curCommandRelative=\"a\"===n}else r.push({type:_.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=n,this.curNumberHasDecimal=\".\"===n}else this.curNumber+=n,this.curNumberHasDecimal=!0;else this.curNumber+=n;else this.curNumber+=n,this.curNumberHasExp=!0;else this.curNumber+=n,this.curNumberHasExpDigits=this.curNumberHasExp}return r},e.prototype.transform=function(t){return Object.create(this,{parse:{value:function(r,e){void 0===e&&(e=[]);for(var i=0,a=Object.getPrototypeOf(this).parse.call(this,r);i<a.length;i++){var n=a[i],o=t(n);Array.isArray(o)?e.push.apply(e,o):e.push(o)}return e}}})},e}(l),_=function(t){function i(r){var e=t.call(this)||this;return e.commands=\"string\"==typeof r?i.parse(r):r,e}return r(i,t),i.prototype.encode=function(){return i.encode(this.commands)},i.prototype.getBounds=function(){var t=u.CALCULATE_BOUNDS();return this.transform(t),t},i.prototype.transform=function(t){for(var r=[],e=0,i=this.commands;e<i.length;e++){var a=t(i[e]);Array.isArray(a)?r.push.apply(r,a):r.push(a)}return this.commands=r,this},i.encode=function(t){return e(t)},i.parse=function(t){var r=new f,e=[];return r.parse(t,e),r.finish(e),e},i.CLOSE_PATH=1,i.MOVE_TO=2,i.HORIZ_LINE_TO=4,i.VERT_LINE_TO=8,i.LINE_TO=16,i.CURVE_TO=32,i.SMOOTH_CURVE_TO=64,i.QUAD_TO=128,i.SMOOTH_QUAD_TO=256,i.ARC=512,i.LINE_COMMANDS=i.LINE_TO|i.HORIZ_LINE_TO|i.VERT_LINE_TO,i.DRAWING_COMMANDS=i.HORIZ_LINE_TO|i.VERT_LINE_TO|i.LINE_TO|i.CURVE_TO|i.SMOOTH_CURVE_TO|i.QUAD_TO|i.SMOOTH_QUAD_TO|i.ARC,i}(l),N=((O={})[_.MOVE_TO]=2,O[_.LINE_TO]=2,O[_.HORIZ_LINE_TO]=1,O[_.VERT_LINE_TO]=1,O[_.CLOSE_PATH]=0,O[_.QUAD_TO]=4,O[_.SMOOTH_QUAD_TO]=2,O[_.CURVE_TO]=6,O[_.SMOOTH_CURVE_TO]=4,O[_.ARC]=7,O);\n//# sourceMappingURL=SVGPathData.module.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/svg-pathdata@6.0.3/node_modules/svg-pathdata/lib/SVGPathData.module.js\n");

/***/ })

};
;