"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/simple-fuel-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/reporting/simple-fuel-report.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleFuelReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create columns for the fuel report table\nconst createFuelReportColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-medium\",\n                    children: item.vesselName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"logbookDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Log Entry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/M/YY\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelTankName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Tank\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelTankName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelStart\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Start\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelStart.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelAdded\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Added\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelAdded.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelEnd\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel End\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelEnd.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelUsed\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Used\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelUsed.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"comments\",\n            header: \"Comments\",\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.comments || \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 24\n                }, undefined);\n            }\n        }\n    ]);\n// Custom dropdown actions component for the fuel report\nfunction FuelReportFilterActions(param) {\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_13__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 159,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadPdf,\n                            children: \"Download PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadCsv,\n                            children: \"Download CSV\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 162,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 158,\n        columnNumber: 9\n    }, this);\n}\n_s(FuelReportFilterActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = FuelReportFilterActions;\nfunction SimpleFuelReport() {\n    _s1();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    // Create columns for the table\n    const columns = createFuelReportColumns();\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__.GET_SIMPLE_FUEL_REPORT_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = ()=>{\n        const filter = {};\n        // Only apply date filter if both dates are selected\n        if ((dateRange === null || dateRange === void 0 ? void 0 : dateRange.startDate) && (dateRange === null || dateRange === void 0 ? void 0 : dateRange.endDate)) {\n            filter[\"startDate\"] = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"vehicleID\"] = {\n                in: selectedVessels.map((v)=>v.value)\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readLogBookEntries;\n        var _data_readLogBookEntries_nodes;\n        const fetchedData = (_data_readLogBookEntries_nodes = data === null || data === void 0 ? void 0 : (_data_readLogBookEntries = data.readLogBookEntries) === null || _data_readLogBookEntries === void 0 ? void 0 : _data_readLogBookEntries.nodes) !== null && _data_readLogBookEntries_nodes !== void 0 ? _data_readLogBookEntries_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const filteredItems = fetchedData.filter(function(item) {\n            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== \"0\";\n        });\n        if (filteredItems.length === 0) {\n            return [];\n        }\n        const items = [];\n        filteredItems.forEach((item)=>{\n            if (item.state !== \"Locked\") {\n                return;\n            }\n            const logBookDate = new Date(item.startDate);\n            const vessel = item.vehicle;\n            const fuelLogs = item.fuelLog.nodes.filter((item)=>item.fuelTank.id != 0);\n            const fuelTanks = fuelLogs.reduce((acc, log)=>{\n                return {\n                    ...acc,\n                    [log.fuelTank.id]: log.fuelTank.title\n                };\n            }, {});\n            const logBookEntrySections = item.logBookEntrySections.nodes;\n            const tripEvents = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.tripEvents.nodes\n                ];\n            }, []);\n            const sectionMemberComments = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.sectionMemberComments.nodes\n                ];\n            }, []).map((sectionComment)=>sectionComment === null || sectionComment === void 0 ? void 0 : sectionComment.comment).filter((value)=>value != null || value != \"\");\n            for(const key in fuelTanks){\n                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {\n                    const fuelTankName = fuelTanks[key];\n                    const fuelTankLogs = fuelLogs.filter((item)=>item.fuelTankID == key);\n                    const fuelLogStart = fuelTankLogs[0];\n                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1];\n                    var _fuelLogStart_fuelBefore;\n                    const fuelStart = (_fuelLogStart_fuelBefore = fuelLogStart === null || fuelLogStart === void 0 ? void 0 : fuelLogStart.fuelBefore) !== null && _fuelLogStart_fuelBefore !== void 0 ? _fuelLogStart_fuelBefore : 0;\n                    const fuelAdded = calculateFuelAddedFromTripEvents(tripEvents, key);\n                    var _fuelLogEnd_fuelAfter;\n                    const fuelEnd = (_fuelLogEnd_fuelAfter = fuelLogEnd === null || fuelLogEnd === void 0 ? void 0 : fuelLogEnd.fuelAfter) !== null && _fuelLogEnd_fuelAfter !== void 0 ? _fuelLogEnd_fuelAfter : 0;\n                    const fuelUsed = fuelStart + fuelAdded - fuelEnd;\n                    const reportItem = {\n                        logBookEntryID: item.id,\n                        vesselID: vessel.id,\n                        logbookDate: logBookDate,\n                        vesselName: vessel.title,\n                        fuelTankID: Number(key),\n                        fuelTankName: fuelTankName,\n                        fuelStart,\n                        fuelAdded,\n                        fuelEnd,\n                        fuelUsed,\n                        comments: sectionMemberComments.join(\", \")\n                    };\n                    items.push(reportItem);\n                }\n            }\n        });\n        return items;\n    }, [\n        data,\n        called,\n        loading\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"vessel\",\n            \"log entry\",\n            \"fuel tank\",\n            \"fuel start\",\n            \"fuel added\",\n            \"fuel end\",\n            \"fuel used\",\n            \"comments\"\n        ]);\n        reportData.forEach((item)=>{\n            var _item_comments;\n            csvEntries.push([\n                item.vesselName,\n                item.logbookDate.toISOString(),\n                item.fuelTankName,\n                item.fuelStart,\n                item.fuelAdded,\n                item.fuelEnd,\n                item.fuelUsed,\n                (_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\"\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const data = reportData.map(function(item) {\n            var _item_comments;\n            return [\n                item.vesselName + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/MM/YY\") + \"\",\n                item.fuelTankName + \"\",\n                item.fuelStart.toLocaleString(),\n                item.fuelAdded.toLocaleString(),\n                item.fuelEnd.toLocaleString(),\n                item.fuelUsed.toLocaleString(),\n                \"\".concat((_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\", \" \")\n            ];\n        });\n        const totalUsedFuel = reportData.reduce((acc, current)=>acc + current.fuelUsed, 0);\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers: [\n                [\n                    {\n                        content: \"Vessel\"\n                    },\n                    {\n                        content: \"Log Entry\"\n                    },\n                    {\n                        content: \"Fuel Tank\"\n                    },\n                    {\n                        content: \"Fuel Start\"\n                    },\n                    {\n                        content: \"Fuel Added\"\n                    },\n                    {\n                        content: \"Fuel End\"\n                    },\n                    {\n                        content: \"Fuel Used\"\n                    },\n                    {\n                        content: \"Comments\",\n                        styles: {\n                            cellWidth: 60\n                        }\n                    }\n                ]\n            ],\n            body: data,\n            footers: [\n                [\n                    {\n                        colSpan: 6,\n                        content: \"Total Fuel Used\"\n                    },\n                    {\n                        content: totalUsedFuel.toLocaleString()\n                    },\n                    {\n                        content: \"\"\n                    }\n                ]\n            ],\n            userOptions: {\n                showFoot: \"lastPage\"\n            }\n        });\n    };\n    // Calculate total fuel used for footer\n    const totalFuelUsed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return reportData.reduce((acc, current)=>current.fuelUsed + acc, 0);\n    }, [\n        reportData\n    ]);\n    // Create footer content for the table\n    const footerContent = reportData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n        className: \"group border-b\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-left font-medium\",\n                colSpan: 6,\n                children: \"Total Fuel Used\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 443,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-right font-medium\",\n                children: totalFuelUsed.toLocaleString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 448,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 451,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 442,\n        columnNumber: 13\n    }, this) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ListHeader, {\n                title: \"Simple Fuel Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FuelReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 460,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 457,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    noDataText: \"No fuel data found, try clicking generate report to view results\",\n                    showToolbar: true,\n                    footer: footerContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 467,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 466,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(SimpleFuelReport, \"FjUG+MWP+kYXAKFh5HOCMHzr5ag=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c1 = SimpleFuelReport;\nfunction getFuelAddedByFuelTankID(fuelLogs, fuelTankID) {\n    if (fuelLogs.length === 0) {\n        return 0;\n    }\n    const fuelTankLogs = fuelLogs.filter((log)=>log.fuelTankID == fuelTankID);\n    return fuelTankLogs.reduce((acc, log)=>acc + log.fuelAdded, 0);\n}\nfunction calculateFuelAddedFromTripEvents(tripEvents, fuelTankID) {\n    const fuelAddedLogs = tripEvents.map(function(tripEvent) {\n        if (tripEvent.eventCategory === \"RefuellingBunkering\") {\n            const fuelLogs = tripEvent.eventType_RefuellingBunkering.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"Tasking\") {\n            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"PassengerDropFacility\") {\n            const fuelLogs = tripEvent.eventType_PassengerDropFacility.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        return 0;\n    });\n    return fuelAddedLogs.reduce((acc, val)=>acc + val, 0);\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"FuelReportFilterActions\");\n$RefreshReg$(_c1, \"SimpleFuelReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx\n"));

/***/ })

});