"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf";
exports.ids = ["vendor-chunks/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Group: () => (/* binding */ Group),\n/* harmony export */   Icon: () => (/* binding */ Icon),\n/* harmony export */   Item: () => (/* binding */ Item),\n/* harmony export */   ItemIndicator: () => (/* binding */ ItemIndicator),\n/* harmony export */   ItemText: () => (/* binding */ ItemText),\n/* harmony export */   Label: () => (/* binding */ Label),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   ScrollDownButton: () => (/* binding */ ScrollDownButton),\n/* harmony export */   ScrollUpButton: () => (/* binding */ ScrollUpButton),\n/* harmony export */   Select: () => (/* binding */ Select),\n/* harmony export */   SelectArrow: () => (/* binding */ SelectArrow),\n/* harmony export */   SelectContent: () => (/* binding */ SelectContent),\n/* harmony export */   SelectGroup: () => (/* binding */ SelectGroup),\n/* harmony export */   SelectIcon: () => (/* binding */ SelectIcon),\n/* harmony export */   SelectItem: () => (/* binding */ SelectItem),\n/* harmony export */   SelectItemIndicator: () => (/* binding */ SelectItemIndicator),\n/* harmony export */   SelectItemText: () => (/* binding */ SelectItemText),\n/* harmony export */   SelectLabel: () => (/* binding */ SelectLabel),\n/* harmony export */   SelectPortal: () => (/* binding */ SelectPortal),\n/* harmony export */   SelectScrollDownButton: () => (/* binding */ SelectScrollDownButton),\n/* harmony export */   SelectScrollUpButton: () => (/* binding */ SelectScrollUpButton),\n/* harmony export */   SelectSeparator: () => (/* binding */ SelectSeparator),\n/* harmony export */   SelectTrigger: () => (/* binding */ SelectTrigger),\n/* harmony export */   SelectValue: () => (/* binding */ SelectValue),\n/* harmony export */   SelectViewport: () => (/* binding */ SelectViewport),\n/* harmony export */   Separator: () => (/* binding */ Separator),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   Value: () => (/* binding */ Value),\n/* harmony export */   Viewport: () => (/* binding */ Viewport),\n/* harmony export */   createSelectScope: () => (/* binding */ createSelectScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var _radix_ui_number__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @radix-ui/number */ \"(ssr)/./node_modules/.pnpm/@radix-ui+number@1.1.1/node_modules/@radix-ui/number/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-collection */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-collection@_8ed47621286c24058c66cffcdce48db5/node_modules/@radix-ui/react-collection/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-direction */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-direction@1_26df26fd03f5ad5f8b1ef200265210a9/node_modules/@radix-ui/react-direction/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_172eed378379c5fa3b568ee810e2dcd9/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_d352c0cd6a6afa5cbe132ca4d71633df/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @radix-ui/react-use-layout-effect */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-layout-_00f1a526ffd026e40bef218e06c12993/node_modules/@radix-ui/react-use-layout-effect/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_43bf2522b11b4054129913e6ef284501/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! @radix-ui/react-visually-hidden */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-visually-hi_bd769e2c7ddceeff6e63be21c84dfac7/node_modules/@radix-ui/react-visually-hidden/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Arrow,Content,Group,Icon,Item,ItemIndicator,ItemText,Label,Portal,Root,ScrollDownButton,ScrollUpButton,Select,SelectArrow,SelectContent,SelectGroup,SelectIcon,SelectItem,SelectItemIndicator,SelectItemText,SelectLabel,SelectPortal,SelectScrollDownButton,SelectScrollUpButton,SelectSeparator,SelectTrigger,SelectValue,SelectViewport,Separator,Trigger,Value,Viewport,createSelectScope auto */ // src/select.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar OPEN_KEYS = [\n    \" \",\n    \"Enter\",\n    \"ArrowUp\",\n    \"ArrowDown\"\n];\nvar SELECTION_KEYS = [\n    \" \",\n    \"Enter\"\n];\nvar SELECT_NAME = \"Select\";\nvar [Collection, useCollection, createCollectionScope] = (0,_radix_ui_react_collection__WEBPACK_IMPORTED_MODULE_3__.createCollection)(SELECT_NAME);\nvar [createSelectContext, createSelectScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_4__.createContextScope)(SELECT_NAME, [\n    createCollectionScope,\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.createPopperScope)();\nvar [SelectProvider, useSelectContext] = createSelectContext(SELECT_NAME);\nvar [SelectNativeOptionsProvider, useSelectNativeOptionsContext] = createSelectContext(SELECT_NAME);\nvar Select = (props)=>{\n    const { __scopeSelect, children, open: openProp, defaultOpen, onOpenChange, value: valueProp, defaultValue, onValueChange, dir, name, autoComplete, disabled, required, form } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const [trigger, setTrigger] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNode, setValueNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [valueNodeHasChildren, setValueNodeHasChildren] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const direction = (0,_radix_ui_react_direction__WEBPACK_IMPORTED_MODULE_6__.useDirection)(dir);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: SELECT_NAME\n    });\n    const [value, setValue] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_7__.useControllableState)({\n        prop: valueProp,\n        defaultProp: defaultValue,\n        onChange: onValueChange,\n        caller: SELECT_NAME\n    });\n    const triggerPointerDownPosRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFormControl = trigger ? form || !!trigger.closest(\"form\") : true;\n    const [nativeOptionsSet, setNativeOptionsSet] = react__WEBPACK_IMPORTED_MODULE_0__.useState(/* @__PURE__ */ new Set());\n    const nativeSelectKey = Array.from(nativeOptionsSet).map((option)=>option.props.value).join(\";\");\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectProvider, {\n            required,\n            scope: __scopeSelect,\n            trigger,\n            onTriggerChange: setTrigger,\n            valueNode,\n            onValueNodeChange: setValueNode,\n            valueNodeHasChildren,\n            onValueNodeHasChildrenChange: setValueNodeHasChildren,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)(),\n            value,\n            onValueChange: setValue,\n            open,\n            onOpenChange: setOpen,\n            dir: direction,\n            triggerPointerDownPosRef,\n            disabled,\n            children: [\n                /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Provider, {\n                    scope: __scopeSelect,\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectNativeOptionsProvider, {\n                        scope: props.__scopeSelect,\n                        onNativeOptionAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>new Set(prev).add(option));\n                        }, []),\n                        onNativeOptionRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((option)=>{\n                            setNativeOptionsSet((prev)=>{\n                                const optionsSet = new Set(prev);\n                                optionsSet.delete(option);\n                                return optionsSet;\n                            });\n                        }, []),\n                        children\n                    })\n                }),\n                isFormControl ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(SelectBubbleInput, {\n                    \"aria-hidden\": true,\n                    required,\n                    tabIndex: -1,\n                    name,\n                    autoComplete,\n                    value,\n                    onChange: (event)=>setValue(event.target.value),\n                    disabled,\n                    form,\n                    children: [\n                        value === void 0 ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n                            value: \"\"\n                        }) : null,\n                        Array.from(nativeOptionsSet)\n                    ]\n                }, nativeSelectKey) : null\n            ]\n        })\n    });\n};\nSelect.displayName = SELECT_NAME;\nvar TRIGGER_NAME = \"SelectTrigger\";\nvar SelectTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, disabled = false, ...triggerProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(TRIGGER_NAME, __scopeSelect);\n    const isDisabled = context.disabled || disabled;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onTriggerChange);\n    const getItems = useCollection(__scopeSelect);\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const [searchRef, handleTypeaheadSearch, resetTypeahead] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.value === context.value);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem !== void 0) {\n            context.onValueChange(nextItem.value);\n        }\n    });\n    const handleOpen = (pointerEvent)=>{\n        if (!isDisabled) {\n            context.onOpenChange(true);\n            resetTypeahead();\n        }\n        if (pointerEvent) {\n            context.triggerPointerDownPosRef.current = {\n                x: Math.round(pointerEvent.pageX),\n                y: Math.round(pointerEvent.pageY)\n            };\n        }\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.button, {\n            type: \"button\",\n            role: \"combobox\",\n            \"aria-controls\": context.contentId,\n            \"aria-expanded\": context.open,\n            \"aria-required\": context.required,\n            \"aria-autocomplete\": \"none\",\n            dir: context.dir,\n            \"data-state\": context.open ? \"open\" : \"closed\",\n            disabled: isDisabled,\n            \"data-disabled\": isDisabled ? \"\" : void 0,\n            \"data-placeholder\": shouldShowPlaceholder(context.value) ? \"\" : void 0,\n            ...triggerProps,\n            ref: composedRefs,\n            onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onClick, (event)=>{\n                event.currentTarget.focus();\n                if (pointerTypeRef.current !== \"mouse\") {\n                    handleOpen(event);\n                }\n            }),\n            onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onPointerDown, (event)=>{\n                pointerTypeRef.current = event.pointerType;\n                const target = event.target;\n                if (target.hasPointerCapture(event.pointerId)) {\n                    target.releasePointerCapture(event.pointerId);\n                }\n                if (event.button === 0 && event.ctrlKey === false && event.pointerType === \"mouse\") {\n                    handleOpen(event);\n                    event.preventDefault();\n                }\n            }),\n            onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(triggerProps.onKeyDown, (event)=>{\n                const isTypingAhead = searchRef.current !== \"\";\n                const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                if (isTypingAhead && event.key === \" \") return;\n                if (OPEN_KEYS.includes(event.key)) {\n                    handleOpen();\n                    event.preventDefault();\n                }\n            })\n        })\n    });\n});\nSelectTrigger.displayName = TRIGGER_NAME;\nvar VALUE_NAME = \"SelectValue\";\nvar SelectValue = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, children, placeholder = \"\", ...valueProps } = props;\n    const context = useSelectContext(VALUE_NAME, __scopeSelect);\n    const { onValueNodeHasChildrenChange } = context;\n    const hasChildren = children !== void 0;\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, context.onValueNodeChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onValueNodeHasChildrenChange(hasChildren);\n    }, [\n        onValueNodeHasChildrenChange,\n        hasChildren\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        ...valueProps,\n        ref: composedRefs,\n        style: {\n            pointerEvents: \"none\"\n        },\n        children: shouldShowPlaceholder(context.value) ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n            children: placeholder\n        }) : children\n    });\n});\nSelectValue.displayName = VALUE_NAME;\nvar ICON_NAME = \"SelectIcon\";\nvar SelectIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, children, ...iconProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...iconProps,\n        ref: forwardedRef,\n        children: children || \"▼\"\n    });\n});\nSelectIcon.displayName = ICON_NAME;\nvar PORTAL_NAME = \"SelectPortal\";\nvar SelectPortal = (props)=>{\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_13__.Portal, {\n        asChild: true,\n        ...props\n    });\n};\nSelectPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"SelectContent\";\nvar SelectContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = useSelectContext(CONTENT_NAME, props.__scopeSelect);\n    const [fragment, setFragment] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        setFragment(new DocumentFragment());\n    }, []);\n    if (!context.open) {\n        const frag = fragment;\n        return frag ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n            scope: props.__scopeSelect,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: props.__scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n                    children: props.children\n                })\n            })\n        }), frag) : null;\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentImpl, {\n        ...props,\n        ref: forwardedRef\n    });\n});\nSelectContent.displayName = CONTENT_NAME;\nvar CONTENT_MARGIN = 10;\nvar [SelectContentProvider, useSelectContentContext] = createSelectContext(CONTENT_NAME);\nvar CONTENT_IMPL_NAME = \"SelectContentImpl\";\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_14__.createSlot)(\"SelectContent.RemoveScroll\");\nvar SelectContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, position = \"item-aligned\", onCloseAutoFocus, onEscapeKeyDown, onPointerDownOutside, //\n    // PopperContent props\n    side, sideOffset, align, alignOffset, arrowPadding, collisionBoundary, collisionPadding, sticky, hideWhenDetached, avoidCollisions, //\n    ...contentProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [viewport, setViewport] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const [selectedItem, setSelectedItem] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [selectedItemText, setSelectedItemText] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const getItems = useCollection(__scopeSelect);\n    const [isPositioned, setIsPositioned] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const firstValidItemFoundRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_15__.hideOthers)(content);\n    }, [\n        content\n    ]);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_16__.useFocusGuards)();\n    const focusFirst = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((candidates)=>{\n        const [firstItem, ...restItems] = getItems().map((item)=>item.ref.current);\n        const [lastItem] = restItems.slice(-1);\n        const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n        for (const candidate of candidates){\n            if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n            candidate?.scrollIntoView({\n                block: \"nearest\"\n            });\n            if (candidate === firstItem && viewport) viewport.scrollTop = 0;\n            if (candidate === lastItem && viewport) viewport.scrollTop = viewport.scrollHeight;\n            candidate?.focus();\n            if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n        }\n    }, [\n        getItems,\n        viewport\n    ]);\n    const focusSelectedItem = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>focusFirst([\n            selectedItem,\n            content\n        ]), [\n        focusFirst,\n        selectedItem,\n        content\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (isPositioned) {\n            focusSelectedItem();\n        }\n    }, [\n        isPositioned,\n        focusSelectedItem\n    ]);\n    const { onOpenChange, triggerPointerDownPosRef } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (content) {\n            let pointerMoveDelta = {\n                x: 0,\n                y: 0\n            };\n            const handlePointerMove = (event)=>{\n                pointerMoveDelta = {\n                    x: Math.abs(Math.round(event.pageX) - (triggerPointerDownPosRef.current?.x ?? 0)),\n                    y: Math.abs(Math.round(event.pageY) - (triggerPointerDownPosRef.current?.y ?? 0))\n                };\n            };\n            const handlePointerUp = (event)=>{\n                if (pointerMoveDelta.x <= 10 && pointerMoveDelta.y <= 10) {\n                    event.preventDefault();\n                } else {\n                    if (!content.contains(event.target)) {\n                        onOpenChange(false);\n                    }\n                }\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                triggerPointerDownPosRef.current = null;\n            };\n            if (triggerPointerDownPosRef.current !== null) {\n                document.addEventListener(\"pointermove\", handlePointerMove);\n                document.addEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true,\n                    once: true\n                });\n            }\n            return ()=>{\n                document.removeEventListener(\"pointermove\", handlePointerMove);\n                document.removeEventListener(\"pointerup\", handlePointerUp, {\n                    capture: true\n                });\n            };\n        }\n    }, [\n        content,\n        onOpenChange,\n        triggerPointerDownPosRef\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const close = ()=>onOpenChange(false);\n        window.addEventListener(\"blur\", close);\n        window.addEventListener(\"resize\", close);\n        return ()=>{\n            window.removeEventListener(\"blur\", close);\n            window.removeEventListener(\"resize\", close);\n        };\n    }, [\n        onOpenChange\n    ]);\n    const [searchRef, handleTypeaheadSearch] = useTypeaheadSearch((search)=>{\n        const enabledItems = getItems().filter((item)=>!item.disabled);\n        const currentItem = enabledItems.find((item)=>item.ref.current === document.activeElement);\n        const nextItem = findNextItem(enabledItems, search, currentItem);\n        if (nextItem) {\n            setTimeout(()=>nextItem.ref.current.focus());\n        }\n    });\n    const itemRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItem(node);\n            if (isFirstValidItem) firstValidItemFoundRef.current = true;\n        }\n    }, [\n        context.value\n    ]);\n    const handleItemLeave = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>content?.focus(), [\n        content\n    ]);\n    const itemTextRefCallback = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node, value, disabled)=>{\n        const isFirstValidItem = !firstValidItemFoundRef.current && !disabled;\n        const isSelectedItem = context.value !== void 0 && context.value === value;\n        if (isSelectedItem || isFirstValidItem) {\n            setSelectedItemText(node);\n        }\n    }, [\n        context.value\n    ]);\n    const SelectPosition = position === \"popper\" ? SelectPopperPosition : SelectItemAlignedPosition;\n    const popperContentProps = SelectPosition === SelectPopperPosition ? {\n        side,\n        sideOffset,\n        align,\n        alignOffset,\n        arrowPadding,\n        collisionBoundary,\n        collisionPadding,\n        sticky,\n        hideWhenDetached,\n        avoidCollisions\n    } : {};\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectContentProvider, {\n        scope: __scopeSelect,\n        content,\n        viewport,\n        onViewportChange: setViewport,\n        itemRefCallback,\n        selectedItem,\n        onItemLeave: handleItemLeave,\n        itemTextRefCallback,\n        focusSelectedItem,\n        selectedItemText,\n        position,\n        isPositioned,\n        searchRef,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n            as: Slot,\n            allowPinchZoom: true,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_18__.FocusScope, {\n                asChild: true,\n                trapped: context.open,\n                onMountAutoFocus: (event)=>{\n                    event.preventDefault();\n                },\n                onUnmountAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(onCloseAutoFocus, (event)=>{\n                    context.trigger?.focus({\n                        preventScroll: true\n                    });\n                    event.preventDefault();\n                }),\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_19__.DismissableLayer, {\n                    asChild: true,\n                    disableOutsidePointerEvents: true,\n                    onEscapeKeyDown,\n                    onPointerDownOutside,\n                    onFocusOutside: (event)=>event.preventDefault(),\n                    onDismiss: ()=>context.onOpenChange(false),\n                    children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectPosition, {\n                        role: \"listbox\",\n                        id: context.contentId,\n                        \"data-state\": context.open ? \"open\" : \"closed\",\n                        dir: context.dir,\n                        onContextMenu: (event)=>event.preventDefault(),\n                        ...contentProps,\n                        ...popperContentProps,\n                        onPlaced: ()=>setIsPositioned(true),\n                        ref: composedRefs,\n                        style: {\n                            // flex layout so we can place the scroll buttons properly\n                            display: \"flex\",\n                            flexDirection: \"column\",\n                            // reset the outline by default as the content MAY get focused\n                            outline: \"none\",\n                            ...contentProps.style\n                        },\n                        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(contentProps.onKeyDown, (event)=>{\n                            const isModifierKey = event.ctrlKey || event.altKey || event.metaKey;\n                            if (event.key === \"Tab\") event.preventDefault();\n                            if (!isModifierKey && event.key.length === 1) handleTypeaheadSearch(event.key);\n                            if ([\n                                \"ArrowUp\",\n                                \"ArrowDown\",\n                                \"Home\",\n                                \"End\"\n                            ].includes(event.key)) {\n                                const items = getItems().filter((item)=>!item.disabled);\n                                let candidateNodes = items.map((item)=>item.ref.current);\n                                if ([\n                                    \"ArrowUp\",\n                                    \"End\"\n                                ].includes(event.key)) {\n                                    candidateNodes = candidateNodes.slice().reverse();\n                                }\n                                if ([\n                                    \"ArrowUp\",\n                                    \"ArrowDown\"\n                                ].includes(event.key)) {\n                                    const currentElement = event.target;\n                                    const currentIndex = candidateNodes.indexOf(currentElement);\n                                    candidateNodes = candidateNodes.slice(currentIndex + 1);\n                                }\n                                setTimeout(()=>focusFirst(candidateNodes));\n                                event.preventDefault();\n                            }\n                        })\n                    })\n                })\n            })\n        })\n    });\n});\nSelectContentImpl.displayName = CONTENT_IMPL_NAME;\nvar ITEM_ALIGNED_POSITION_NAME = \"SelectItemAlignedPosition\";\nvar SelectItemAlignedPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onPlaced, ...popperProps } = props;\n    const context = useSelectContext(CONTENT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(CONTENT_NAME, __scopeSelect);\n    const [contentWrapper, setContentWrapper] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [content, setContent] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setContent(node));\n    const getItems = useCollection(__scopeSelect);\n    const shouldExpandOnScrollRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const shouldRepositionRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(true);\n    const { viewport, selectedItem, selectedItemText, focusSelectedItem } = contentContext;\n    const position = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (context.trigger && context.valueNode && contentWrapper && content && viewport && selectedItem && selectedItemText) {\n            const triggerRect = context.trigger.getBoundingClientRect();\n            const contentRect = content.getBoundingClientRect();\n            const valueNodeRect = context.valueNode.getBoundingClientRect();\n            const itemTextRect = selectedItemText.getBoundingClientRect();\n            if (context.dir !== \"rtl\") {\n                const itemTextOffset = itemTextRect.left - contentRect.left;\n                const left = valueNodeRect.left - itemTextOffset;\n                const leftDelta = triggerRect.left - left;\n                const minContentWidth = triggerRect.width + leftDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const rightEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedLeft = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(left, [\n                    CONTENT_MARGIN,\n                    // Prevents the content from going off the starting edge of the\n                    // viewport. It may still go off the ending edge, but this can be\n                    // controlled by the user since they may want to manage overflow in a\n                    // specific way.\n                    // https://github.com/radix-ui/primitives/issues/2049\n                    Math.max(CONTENT_MARGIN, rightEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.left = clampedLeft + \"px\";\n            } else {\n                const itemTextOffset = contentRect.right - itemTextRect.right;\n                const right = window.innerWidth - valueNodeRect.right - itemTextOffset;\n                const rightDelta = window.innerWidth - triggerRect.right - right;\n                const minContentWidth = triggerRect.width + rightDelta;\n                const contentWidth = Math.max(minContentWidth, contentRect.width);\n                const leftEdge = window.innerWidth - CONTENT_MARGIN;\n                const clampedRight = (0,_radix_ui_number__WEBPACK_IMPORTED_MODULE_20__.clamp)(right, [\n                    CONTENT_MARGIN,\n                    Math.max(CONTENT_MARGIN, leftEdge - contentWidth)\n                ]);\n                contentWrapper.style.minWidth = minContentWidth + \"px\";\n                contentWrapper.style.right = clampedRight + \"px\";\n            }\n            const items = getItems();\n            const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n            const itemsHeight = viewport.scrollHeight;\n            const contentStyles = window.getComputedStyle(content);\n            const contentBorderTopWidth = parseInt(contentStyles.borderTopWidth, 10);\n            const contentPaddingTop = parseInt(contentStyles.paddingTop, 10);\n            const contentBorderBottomWidth = parseInt(contentStyles.borderBottomWidth, 10);\n            const contentPaddingBottom = parseInt(contentStyles.paddingBottom, 10);\n            const fullContentHeight = contentBorderTopWidth + contentPaddingTop + itemsHeight + contentPaddingBottom + contentBorderBottomWidth;\n            const minContentHeight = Math.min(selectedItem.offsetHeight * 5, fullContentHeight);\n            const viewportStyles = window.getComputedStyle(viewport);\n            const viewportPaddingTop = parseInt(viewportStyles.paddingTop, 10);\n            const viewportPaddingBottom = parseInt(viewportStyles.paddingBottom, 10);\n            const topEdgeToTriggerMiddle = triggerRect.top + triggerRect.height / 2 - CONTENT_MARGIN;\n            const triggerMiddleToBottomEdge = availableHeight - topEdgeToTriggerMiddle;\n            const selectedItemHalfHeight = selectedItem.offsetHeight / 2;\n            const itemOffsetMiddle = selectedItem.offsetTop + selectedItemHalfHeight;\n            const contentTopToItemMiddle = contentBorderTopWidth + contentPaddingTop + itemOffsetMiddle;\n            const itemMiddleToContentBottom = fullContentHeight - contentTopToItemMiddle;\n            const willAlignWithoutTopOverflow = contentTopToItemMiddle <= topEdgeToTriggerMiddle;\n            if (willAlignWithoutTopOverflow) {\n                const isLastItem = items.length > 0 && selectedItem === items[items.length - 1].ref.current;\n                contentWrapper.style.bottom = \"0px\";\n                const viewportOffsetBottom = content.clientHeight - viewport.offsetTop - viewport.offsetHeight;\n                const clampedTriggerMiddleToBottomEdge = Math.max(triggerMiddleToBottomEdge, selectedItemHalfHeight + // viewport might have padding bottom, include it to avoid a scrollable viewport\n                (isLastItem ? viewportPaddingBottom : 0) + viewportOffsetBottom + contentBorderBottomWidth);\n                const height = contentTopToItemMiddle + clampedTriggerMiddleToBottomEdge;\n                contentWrapper.style.height = height + \"px\";\n            } else {\n                const isFirstItem = items.length > 0 && selectedItem === items[0].ref.current;\n                contentWrapper.style.top = \"0px\";\n                const clampedTopEdgeToTriggerMiddle = Math.max(topEdgeToTriggerMiddle, contentBorderTopWidth + viewport.offsetTop + // viewport might have padding top, include it to avoid a scrollable viewport\n                (isFirstItem ? viewportPaddingTop : 0) + selectedItemHalfHeight);\n                const height = clampedTopEdgeToTriggerMiddle + itemMiddleToContentBottom;\n                contentWrapper.style.height = height + \"px\";\n                viewport.scrollTop = contentTopToItemMiddle - topEdgeToTriggerMiddle + viewport.offsetTop;\n            }\n            contentWrapper.style.margin = `${CONTENT_MARGIN}px 0`;\n            contentWrapper.style.minHeight = minContentHeight + \"px\";\n            contentWrapper.style.maxHeight = availableHeight + \"px\";\n            onPlaced?.();\n            requestAnimationFrame(()=>shouldExpandOnScrollRef.current = true);\n        }\n    }, [\n        getItems,\n        context.trigger,\n        context.valueNode,\n        contentWrapper,\n        content,\n        viewport,\n        selectedItem,\n        selectedItemText,\n        context.dir,\n        onPlaced\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>position(), [\n        position\n    ]);\n    const [contentZIndex, setContentZIndex] = react__WEBPACK_IMPORTED_MODULE_0__.useState();\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (content) setContentZIndex(window.getComputedStyle(content).zIndex);\n    }, [\n        content\n    ]);\n    const handleScrollButtonChange = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n        if (node && shouldRepositionRef.current === true) {\n            position();\n            focusSelectedItem?.();\n            shouldRepositionRef.current = false;\n        }\n    }, [\n        position,\n        focusSelectedItem\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectViewportProvider, {\n        scope: __scopeSelect,\n        contentWrapper,\n        shouldExpandOnScrollRef,\n        onScrollButtonChange: handleScrollButtonChange,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"div\", {\n            ref: setContentWrapper,\n            style: {\n                display: \"flex\",\n                flexDirection: \"column\",\n                position: \"fixed\",\n                zIndex: contentZIndex\n            },\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                ...popperProps,\n                ref: composedRefs,\n                style: {\n                    // When we get the height of the content, it includes borders. If we were to set\n                    // the height without having `boxSizing: 'border-box'` it would be too big.\n                    boxSizing: \"border-box\",\n                    // We need to ensure the content doesn't get taller than the wrapper\n                    maxHeight: \"100%\",\n                    ...popperProps.style\n                }\n            })\n        })\n    });\n});\nSelectItemAlignedPosition.displayName = ITEM_ALIGNED_POSITION_NAME;\nvar POPPER_POSITION_NAME = \"SelectPopperPosition\";\nvar SelectPopperPosition = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, align = \"start\", collisionPadding = CONTENT_MARGIN, ...popperProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Content, {\n        ...popperScope,\n        ...popperProps,\n        ref: forwardedRef,\n        align,\n        collisionPadding,\n        style: {\n            // Ensure border-box for floating-ui calculations\n            boxSizing: \"border-box\",\n            ...popperProps.style,\n            // re-namespace exposed content custom properties\n            ...{\n                \"--radix-select-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                \"--radix-select-content-available-width\": \"var(--radix-popper-available-width)\",\n                \"--radix-select-content-available-height\": \"var(--radix-popper-available-height)\",\n                \"--radix-select-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                \"--radix-select-trigger-height\": \"var(--radix-popper-anchor-height)\"\n            }\n        }\n    });\n});\nSelectPopperPosition.displayName = POPPER_POSITION_NAME;\nvar [SelectViewportProvider, useSelectViewportContext] = createSelectContext(CONTENT_NAME, {});\nvar VIEWPORT_NAME = \"SelectViewport\";\nvar SelectViewport = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, nonce, ...viewportProps } = props;\n    const contentContext = useSelectContentContext(VIEWPORT_NAME, __scopeSelect);\n    const viewportContext = useSelectViewportContext(VIEWPORT_NAME, __scopeSelect);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, contentContext.onViewportChange);\n    const prevScrollTopRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"style\", {\n                dangerouslySetInnerHTML: {\n                    __html: `[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}`\n                },\n                nonce\n            }),\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.Slot, {\n                scope: __scopeSelect,\n                children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                    \"data-radix-select-viewport\": \"\",\n                    role: \"presentation\",\n                    ...viewportProps,\n                    ref: composedRefs,\n                    style: {\n                        // we use position: 'relative' here on the `viewport` so that when we call\n                        // `selectedItem.offsetTop` in calculations, the offset is relative to the viewport\n                        // (independent of the scrollUpButton).\n                        position: \"relative\",\n                        flex: 1,\n                        // Viewport should only be scrollable in the vertical direction.\n                        // This won't work in vertical writing modes, so we'll need to\n                        // revisit this if/when that is supported\n                        // https://developer.chrome.com/blog/vertical-form-controls\n                        overflow: \"hidden auto\",\n                        ...viewportProps.style\n                    },\n                    onScroll: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(viewportProps.onScroll, (event)=>{\n                        const viewport = event.currentTarget;\n                        const { contentWrapper, shouldExpandOnScrollRef } = viewportContext;\n                        if (shouldExpandOnScrollRef?.current && contentWrapper) {\n                            const scrolledBy = Math.abs(prevScrollTopRef.current - viewport.scrollTop);\n                            if (scrolledBy > 0) {\n                                const availableHeight = window.innerHeight - CONTENT_MARGIN * 2;\n                                const cssMinHeight = parseFloat(contentWrapper.style.minHeight);\n                                const cssHeight = parseFloat(contentWrapper.style.height);\n                                const prevHeight = Math.max(cssMinHeight, cssHeight);\n                                if (prevHeight < availableHeight) {\n                                    const nextHeight = prevHeight + scrolledBy;\n                                    const clampedNextHeight = Math.min(availableHeight, nextHeight);\n                                    const heightDiff = nextHeight - clampedNextHeight;\n                                    contentWrapper.style.height = clampedNextHeight + \"px\";\n                                    if (contentWrapper.style.bottom === \"0px\") {\n                                        viewport.scrollTop = heightDiff > 0 ? heightDiff : 0;\n                                        contentWrapper.style.justifyContent = \"flex-end\";\n                                    }\n                                }\n                            }\n                        }\n                        prevScrollTopRef.current = viewport.scrollTop;\n                    })\n                })\n            })\n        ]\n    });\n});\nSelectViewport.displayName = VIEWPORT_NAME;\nvar GROUP_NAME = \"SelectGroup\";\nvar [SelectGroupContextProvider, useSelectGroupContext] = createSelectContext(GROUP_NAME);\nvar SelectGroup = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...groupProps } = props;\n    const groupId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectGroupContextProvider, {\n        scope: __scopeSelect,\n        id: groupId,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n            role: \"group\",\n            \"aria-labelledby\": groupId,\n            ...groupProps,\n            ref: forwardedRef\n        })\n    });\n});\nSelectGroup.displayName = GROUP_NAME;\nvar LABEL_NAME = \"SelectLabel\";\nvar SelectLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...labelProps } = props;\n    const groupContext = useSelectGroupContext(LABEL_NAME, __scopeSelect);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        id: groupContext.id,\n        ...labelProps,\n        ref: forwardedRef\n    });\n});\nSelectLabel.displayName = LABEL_NAME;\nvar ITEM_NAME = \"SelectItem\";\nvar [SelectItemContextProvider, useSelectItemContext] = createSelectContext(ITEM_NAME);\nvar SelectItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, value, disabled = false, textValue: textValueProp, ...itemProps } = props;\n    const context = useSelectContext(ITEM_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_NAME, __scopeSelect);\n    const isSelected = context.value === value;\n    const [textValue, setTextValue] = react__WEBPACK_IMPORTED_MODULE_0__.useState(textValueProp ?? \"\");\n    const [isFocused, setIsFocused] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>contentContext.itemRefCallback?.(node, value, disabled));\n    const textId = (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_8__.useId)();\n    const pointerTypeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"touch\");\n    const handleSelect = ()=>{\n        if (!disabled) {\n            context.onValueChange(value);\n            context.onOpenChange(false);\n        }\n    };\n    if (value === \"\") {\n        throw new Error(\"A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.\");\n    }\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectItemContextProvider, {\n        scope: __scopeSelect,\n        value,\n        disabled,\n        textId,\n        isSelected,\n        onItemTextChange: react__WEBPACK_IMPORTED_MODULE_0__.useCallback((node)=>{\n            setTextValue((prevTextValue)=>prevTextValue || (node?.textContent ?? \"\").trim());\n        }, []),\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Collection.ItemSlot, {\n            scope: __scopeSelect,\n            value,\n            disabled,\n            textValue,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n                role: \"option\",\n                \"aria-labelledby\": textId,\n                \"data-highlighted\": isFocused ? \"\" : void 0,\n                \"aria-selected\": isSelected && isFocused,\n                \"data-state\": isSelected ? \"checked\" : \"unchecked\",\n                \"aria-disabled\": disabled || void 0,\n                \"data-disabled\": disabled ? \"\" : void 0,\n                tabIndex: disabled ? void 0 : -1,\n                ...itemProps,\n                ref: composedRefs,\n                onFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onFocus, ()=>setIsFocused(true)),\n                onBlur: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onBlur, ()=>setIsFocused(false)),\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onClick, ()=>{\n                    if (pointerTypeRef.current !== \"mouse\") handleSelect();\n                }),\n                onPointerUp: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerUp, ()=>{\n                    if (pointerTypeRef.current === \"mouse\") handleSelect();\n                }),\n                onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerDown, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                }),\n                onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerMove, (event)=>{\n                    pointerTypeRef.current = event.pointerType;\n                    if (disabled) {\n                        contentContext.onItemLeave?.();\n                    } else if (pointerTypeRef.current === \"mouse\") {\n                        event.currentTarget.focus({\n                            preventScroll: true\n                        });\n                    }\n                }),\n                onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onPointerLeave, (event)=>{\n                    if (event.currentTarget === document.activeElement) {\n                        contentContext.onItemLeave?.();\n                    }\n                }),\n                onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(itemProps.onKeyDown, (event)=>{\n                    const isTypingAhead = contentContext.searchRef?.current !== \"\";\n                    if (isTypingAhead && event.key === \" \") return;\n                    if (SELECTION_KEYS.includes(event.key)) handleSelect();\n                    if (event.key === \" \") event.preventDefault();\n                })\n            })\n        })\n    });\n});\nSelectItem.displayName = ITEM_NAME;\nvar ITEM_TEXT_NAME = \"SelectItemText\";\nvar SelectItemText = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, className, style, ...itemTextProps } = props;\n    const context = useSelectContext(ITEM_TEXT_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ITEM_TEXT_NAME, __scopeSelect);\n    const itemContext = useSelectItemContext(ITEM_TEXT_NAME, __scopeSelect);\n    const nativeOptionsContext = useSelectNativeOptionsContext(ITEM_TEXT_NAME, __scopeSelect);\n    const [itemTextNode, setItemTextNode] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, (node)=>setItemTextNode(node), itemContext.onItemTextChange, (node)=>contentContext.itemTextRefCallback?.(node, itemContext.value, itemContext.disabled));\n    const textContent = itemTextNode?.textContent;\n    const nativeOption = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(\"option\", {\n            value: itemContext.value,\n            disabled: itemContext.disabled,\n            children: textContent\n        }, itemContext.value), [\n        itemContext.disabled,\n        itemContext.value,\n        textContent\n    ]);\n    const { onNativeOptionAdd, onNativeOptionRemove } = nativeOptionsContext;\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        onNativeOptionAdd(nativeOption);\n        return ()=>onNativeOptionRemove(nativeOption);\n    }, [\n        onNativeOptionAdd,\n        onNativeOptionRemove,\n        nativeOption\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n                id: itemContext.textId,\n                ...itemTextProps,\n                ref: composedRefs\n            }),\n            itemContext.isSelected && context.valueNode && !context.valueNodeHasChildren ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal(itemTextProps.children, context.valueNode) : null\n        ]\n    });\n});\nSelectItemText.displayName = ITEM_TEXT_NAME;\nvar ITEM_INDICATOR_NAME = \"SelectItemIndicator\";\nvar SelectItemIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...itemIndicatorProps } = props;\n    const itemContext = useSelectItemContext(ITEM_INDICATOR_NAME, __scopeSelect);\n    return itemContext.isSelected ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.span, {\n        \"aria-hidden\": true,\n        ...itemIndicatorProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectItemIndicator.displayName = ITEM_INDICATOR_NAME;\nvar SCROLL_UP_BUTTON_NAME = \"SelectScrollUpButton\";\nvar SelectScrollUpButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_UP_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollUp, setCanScrollUp] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const canScrollUp2 = viewport.scrollTop > 0;\n                setCanScrollUp(canScrollUp2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollUp ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop - selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollUpButton.displayName = SCROLL_UP_BUTTON_NAME;\nvar SCROLL_DOWN_BUTTON_NAME = \"SelectScrollDownButton\";\nvar SelectScrollDownButton = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const contentContext = useSelectContentContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const viewportContext = useSelectViewportContext(SCROLL_DOWN_BUTTON_NAME, props.__scopeSelect);\n    const [canScrollDown, setCanScrollDown] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, viewportContext.onScrollButtonChange);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        if (contentContext.viewport && contentContext.isPositioned) {\n            let handleScroll2 = function() {\n                const maxScroll = viewport.scrollHeight - viewport.clientHeight;\n                const canScrollDown2 = Math.ceil(viewport.scrollTop) < maxScroll;\n                setCanScrollDown(canScrollDown2);\n            };\n            var handleScroll = handleScroll2;\n            const viewport = contentContext.viewport;\n            handleScroll2();\n            viewport.addEventListener(\"scroll\", handleScroll2);\n            return ()=>viewport.removeEventListener(\"scroll\", handleScroll2);\n        }\n    }, [\n        contentContext.viewport,\n        contentContext.isPositioned\n    ]);\n    return canScrollDown ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(SelectScrollButtonImpl, {\n        ...props,\n        ref: composedRefs,\n        onAutoScroll: ()=>{\n            const { viewport, selectedItem } = contentContext;\n            if (viewport && selectedItem) {\n                viewport.scrollTop = viewport.scrollTop + selectedItem.offsetHeight;\n            }\n        }\n    }) : null;\n});\nSelectScrollDownButton.displayName = SCROLL_DOWN_BUTTON_NAME;\nvar SelectScrollButtonImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, onAutoScroll, ...scrollIndicatorProps } = props;\n    const contentContext = useSelectContentContext(\"SelectScrollButton\", __scopeSelect);\n    const autoScrollTimerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const getItems = useCollection(__scopeSelect);\n    const clearAutoScrollTimer = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        if (autoScrollTimerRef.current !== null) {\n            window.clearInterval(autoScrollTimerRef.current);\n            autoScrollTimerRef.current = null;\n        }\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>clearAutoScrollTimer();\n    }, [\n        clearAutoScrollTimer\n    ]);\n    (0,_radix_ui_react_use_layout_effect__WEBPACK_IMPORTED_MODULE_12__.useLayoutEffect)(()=>{\n        const activeItem = getItems().find((item)=>item.ref.current === document.activeElement);\n        activeItem?.ref.current?.scrollIntoView({\n            block: \"nearest\"\n        });\n    }, [\n        getItems\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...scrollIndicatorProps,\n        ref: forwardedRef,\n        style: {\n            flexShrink: 0,\n            ...scrollIndicatorProps.style\n        },\n        onPointerDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerDown, ()=>{\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerMove: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerMove, ()=>{\n            contentContext.onItemLeave?.();\n            if (autoScrollTimerRef.current === null) {\n                autoScrollTimerRef.current = window.setInterval(onAutoScroll, 50);\n            }\n        }),\n        onPointerLeave: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_11__.composeEventHandlers)(scrollIndicatorProps.onPointerLeave, ()=>{\n            clearAutoScrollTimer();\n        })\n    });\n});\nvar SEPARATOR_NAME = \"SelectSeparator\";\nvar SelectSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...separatorProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.div, {\n        \"aria-hidden\": true,\n        ...separatorProps,\n        ref: forwardedRef\n    });\n});\nSelectSeparator.displayName = SEPARATOR_NAME;\nvar ARROW_NAME = \"SelectArrow\";\nvar SelectArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSelect, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopeSelect);\n    const context = useSelectContext(ARROW_NAME, __scopeSelect);\n    const contentContext = useSelectContentContext(ARROW_NAME, __scopeSelect);\n    return context.open && contentContext.position === \"popper\" ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_5__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    }) : null;\n});\nSelectArrow.displayName = ARROW_NAME;\nvar BUBBLE_INPUT_NAME = \"SelectBubbleInput\";\nvar SelectBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSelect, value, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_9__.useComposedRefs)(forwardedRef, ref);\n    const prevValue = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_21__.usePrevious)(value);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const select = ref.current;\n        if (!select) return;\n        const selectProto = window.HTMLSelectElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(selectProto, \"value\");\n        const setValue = descriptor.set;\n        if (prevValue !== value && setValue) {\n            const event = new Event(\"change\", {\n                bubbles: true\n            });\n            setValue.call(select, value);\n            select.dispatchEvent(event);\n        }\n    }, [\n        prevValue,\n        value\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_10__.Primitive.select, {\n        ...props,\n        style: {\n            ..._radix_ui_react_visually_hidden__WEBPACK_IMPORTED_MODULE_22__.VISUALLY_HIDDEN_STYLES,\n            ...props.style\n        },\n        ref: composedRefs,\n        defaultValue: value\n    });\n});\nSelectBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction shouldShowPlaceholder(value) {\n    return value === \"\" || value === void 0;\n}\nfunction useTypeaheadSearch(onSearchChange) {\n    const handleSearchChange = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_23__.useCallbackRef)(onSearchChange);\n    const searchRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(\"\");\n    const timerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const handleTypeaheadSearch = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((key)=>{\n        const search = searchRef.current + key;\n        handleSearchChange(search);\n        (function updateSearch(value) {\n            searchRef.current = value;\n            window.clearTimeout(timerRef.current);\n            if (value !== \"\") timerRef.current = window.setTimeout(()=>updateSearch(\"\"), 1e3);\n        })(search);\n    }, [\n        handleSearchChange\n    ]);\n    const resetTypeahead = react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>{\n        searchRef.current = \"\";\n        window.clearTimeout(timerRef.current);\n    }, []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        return ()=>window.clearTimeout(timerRef.current);\n    }, []);\n    return [\n        searchRef,\n        handleTypeaheadSearch,\n        resetTypeahead\n    ];\n}\nfunction findNextItem(items, search, currentItem) {\n    const isRepeated = search.length > 1 && Array.from(search).every((char)=>char === search[0]);\n    const normalizedSearch = isRepeated ? search[0] : search;\n    const currentItemIndex = currentItem ? items.indexOf(currentItem) : -1;\n    let wrappedItems = wrapArray(items, Math.max(currentItemIndex, 0));\n    const excludeCurrentItem = normalizedSearch.length === 1;\n    if (excludeCurrentItem) wrappedItems = wrappedItems.filter((v)=>v !== currentItem);\n    const nextItem = wrappedItems.find((item)=>item.textValue.toLowerCase().startsWith(normalizedSearch.toLowerCase()));\n    return nextItem !== currentItem ? nextItem : void 0;\n}\nfunction wrapArray(array, startIndex) {\n    return array.map((_, index)=>array[(startIndex + index) % array.length]);\n}\nvar Root2 = Select;\nvar Trigger = SelectTrigger;\nvar Value = SelectValue;\nvar Icon = SelectIcon;\nvar Portal = SelectPortal;\nvar Content2 = SelectContent;\nvar Viewport = SelectViewport;\nvar Group = SelectGroup;\nvar Label = SelectLabel;\nvar Item = SelectItem;\nvar ItemText = SelectItemText;\nvar ItemIndicator = SelectItemIndicator;\nvar ScrollUpButton = SelectScrollUpButton;\nvar ScrollDownButton = SelectScrollDownButton;\nvar Separator = SelectSeparator;\nvar Arrow2 = SelectArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-select@2.2._74ff1e179f6928d61bde19586f0439bf/node_modules/@radix-ui/react-select/dist/index.mjs\n");

/***/ })

};
;