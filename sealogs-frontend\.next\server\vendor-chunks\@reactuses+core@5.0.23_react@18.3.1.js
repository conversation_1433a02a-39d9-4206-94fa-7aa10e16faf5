"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@reactuses+core@5.0.23_react@18.3.1";
exports.ids = ["vendor-chunks/@reactuses+core@5.0.23_react@18.3.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs":
/*!************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs ***!
  \************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   assignRef: () => (/* binding */ assignRef),\n/* harmony export */   defaultOptions: () => (/* binding */ defaultOptions),\n/* harmony export */   mergeRefs: () => (/* binding */ mergeRefs),\n/* harmony export */   use: () => (/* binding */ use),\n/* harmony export */   useActiveElement: () => (/* binding */ useActiveElement),\n/* harmony export */   useAsyncEffect: () => (/* binding */ useAsyncEffect),\n/* harmony export */   useBroadcastChannel: () => (/* binding */ useBroadcastChannel),\n/* harmony export */   useClickOutside: () => (/* binding */ useClickOutside),\n/* harmony export */   useClipboard: () => (/* binding */ useClipboard),\n/* harmony export */   useControlled: () => (/* binding */ useControlled),\n/* harmony export */   useCookie: () => (/* binding */ useCookie),\n/* harmony export */   useCountDown: () => (/* binding */ useCountDown),\n/* harmony export */   useCounter: () => (/* binding */ useCounter),\n/* harmony export */   useCssVar: () => (/* binding */ useCssVar),\n/* harmony export */   useCustomCompareEffect: () => (/* binding */ useCustomCompareEffect),\n/* harmony export */   useCycleList: () => (/* binding */ useCycleList),\n/* harmony export */   useDarkMode: () => (/* binding */ useDarkMode),\n/* harmony export */   useDebounce: () => (/* binding */ useDebounce),\n/* harmony export */   useDebounceFn: () => (/* binding */ useDebounceFn),\n/* harmony export */   useDeepCompareEffect: () => (/* binding */ useDeepCompareEffect),\n/* harmony export */   useDevicePixelRatio: () => (/* binding */ useDevicePixelRatio),\n/* harmony export */   useDisclosure: () => (/* binding */ useDisclosure),\n/* harmony export */   useDocumentVisibility: () => (/* binding */ useDocumentVisibility),\n/* harmony export */   useDoubleClick: () => (/* binding */ useDoubleClick),\n/* harmony export */   useDraggable: () => (/* binding */ useDraggable),\n/* harmony export */   useDropZone: () => (/* binding */ useDropZone),\n/* harmony export */   useElementBounding: () => (/* binding */ useElementBounding),\n/* harmony export */   useElementByPoint: () => (/* binding */ useElementByPoint),\n/* harmony export */   useElementSize: () => (/* binding */ useElementSize),\n/* harmony export */   useElementVisibility: () => (/* binding */ useElementVisibility),\n/* harmony export */   useEvent: () => (/* binding */ useEvent),\n/* harmony export */   useEventEmitter: () => (/* binding */ useEventEmitter),\n/* harmony export */   useEventListener: () => (/* binding */ useEventListener),\n/* harmony export */   useEventSource: () => (/* binding */ useEventSource),\n/* harmony export */   useEyeDropper: () => (/* binding */ useEyeDropper),\n/* harmony export */   useFavicon: () => (/* binding */ useFavicon),\n/* harmony export */   useFetchEventSource: () => (/* binding */ useFetchEventSource),\n/* harmony export */   useFileDialog: () => (/* binding */ useFileDialog),\n/* harmony export */   useFirstMountState: () => (/* binding */ useFirstMountState),\n/* harmony export */   useFocus: () => (/* binding */ useFocus),\n/* harmony export */   useFps: () => (/* binding */ useFps),\n/* harmony export */   useFullscreen: () => (/* binding */ useFullscreen),\n/* harmony export */   useGeolocation: () => (/* binding */ useGeolocation),\n/* harmony export */   useHover: () => (/* binding */ useHover),\n/* harmony export */   useIdle: () => (/* binding */ useIdle),\n/* harmony export */   useInfiniteScroll: () => (/* binding */ useInfiniteScroll),\n/* harmony export */   useIntersectionObserver: () => (/* binding */ useIntersectionObserver),\n/* harmony export */   useInterval: () => (/* binding */ useInterval),\n/* harmony export */   useIsomorphicLayoutEffect: () => (/* binding */ useIsomorphicLayoutEffect),\n/* harmony export */   useKeyModifier: () => (/* binding */ useKeyModifier),\n/* harmony export */   useLatest: () => (/* binding */ useLatest),\n/* harmony export */   useLocalStorage: () => (/* binding */ useLocalStorage),\n/* harmony export */   useLocationSelector: () => (/* binding */ useLocationSelector),\n/* harmony export */   useLongPress: () => (/* binding */ useLongPress),\n/* harmony export */   useMeasure: () => (/* binding */ useMeasure),\n/* harmony export */   useMediaDevices: () => (/* binding */ useMediaDevices),\n/* harmony export */   useMediaQuery: () => (/* binding */ useMediaQuery),\n/* harmony export */   useMergedRefs: () => (/* binding */ useMergedRefs),\n/* harmony export */   useMobileLandscape: () => (/* binding */ useMobileLandscape),\n/* harmony export */   useMount: () => (/* binding */ useMount),\n/* harmony export */   useMountedState: () => (/* binding */ useMountedState),\n/* harmony export */   useMouse: () => (/* binding */ useMouse),\n/* harmony export */   useMousePressed: () => (/* binding */ useMousePressed),\n/* harmony export */   useMutationObserver: () => (/* binding */ useMutationObserver),\n/* harmony export */   useNetwork: () => (/* binding */ useNetwork),\n/* harmony export */   useObjectUrl: () => (/* binding */ useObjectUrl),\n/* harmony export */   useOnceEffect: () => (/* binding */ useOnceEffect),\n/* harmony export */   useOnceLayoutEffect: () => (/* binding */ useOnceLayoutEffect),\n/* harmony export */   useOnline: () => (/* binding */ useOnline),\n/* harmony export */   useOrientation: () => (/* binding */ useOrientation),\n/* harmony export */   usePageLeave: () => (/* binding */ usePageLeave),\n/* harmony export */   usePermission: () => (/* binding */ usePermission),\n/* harmony export */   usePlatform: () => (/* binding */ usePlatform),\n/* harmony export */   usePreferredColorScheme: () => (/* binding */ usePreferredColorScheme),\n/* harmony export */   usePreferredContrast: () => (/* binding */ usePreferredContrast),\n/* harmony export */   usePreferredDark: () => (/* binding */ usePreferredDark),\n/* harmony export */   usePreferredLanguages: () => (/* binding */ usePreferredLanguages),\n/* harmony export */   usePrevious: () => (/* binding */ usePrevious),\n/* harmony export */   useRafFn: () => (/* binding */ useRafFn),\n/* harmony export */   useRafState: () => (/* binding */ useRafState),\n/* harmony export */   useReducedMotion: () => (/* binding */ useReducedMotion),\n/* harmony export */   useResizeObserver: () => (/* binding */ useResizeObserver),\n/* harmony export */   useScreenSafeArea: () => (/* binding */ useScreenSafeArea),\n/* harmony export */   useScriptTag: () => (/* binding */ useScriptTag),\n/* harmony export */   useScroll: () => (/* binding */ useScroll),\n/* harmony export */   useScrollIntoView: () => (/* binding */ useScrollIntoView),\n/* harmony export */   useScrollLock: () => (/* binding */ useScrollLock),\n/* harmony export */   useSessionStorage: () => (/* binding */ useSessionStorage),\n/* harmony export */   useSetState: () => (/* binding */ useSetState),\n/* harmony export */   useSticky: () => (/* binding */ useSticky),\n/* harmony export */   useSupported: () => (/* binding */ useSupported),\n/* harmony export */   useTextDirection: () => (/* binding */ useTextDirection),\n/* harmony export */   useTextSelection: () => (/* binding */ useTextSelection),\n/* harmony export */   useThrottle: () => (/* binding */ useThrottle),\n/* harmony export */   useThrottleFn: () => (/* binding */ useThrottleFn),\n/* harmony export */   useTimeout: () => (/* binding */ useTimeout),\n/* harmony export */   useTimeoutFn: () => (/* binding */ useTimeoutFn),\n/* harmony export */   useTitle: () => (/* binding */ useTitle),\n/* harmony export */   useToggle: () => (/* binding */ useToggle),\n/* harmony export */   useUnmount: () => (/* binding */ useUnmount),\n/* harmony export */   useUpdate: () => (/* binding */ useUpdate),\n/* harmony export */   useUpdateEffect: () => (/* binding */ useUpdateEffect),\n/* harmony export */   useUpdateLayoutEffect: () => (/* binding */ useUpdateLayoutEffect),\n/* harmony export */   useWebNotification: () => (/* binding */ useWebNotification),\n/* harmony export */   useWindowScroll: () => (/* binding */ useWindowScroll),\n/* harmony export */   useWindowSize: () => (/* binding */ useWindowSize),\n/* harmony export */   useWindowsFocus: () => (/* binding */ useWindowsFocus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/isEqual.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/debounce.js\");\n/* harmony import */ var lodash_es__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lodash-es */ \"(ssr)/./node_modules/.pnpm/lodash-es@4.17.21/node_modules/lodash-es/throttle.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/.pnpm/js-cookie@3.0.5/node_modules/js-cookie/dist/js.cookie.mjs\");\n/* harmony import */ var screenfull__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! screenfull */ \"(ssr)/./node_modules/.pnpm/screenfull@5.2.0/node_modules/screenfull/dist/screenfull.js\");\n/* harmony import */ var use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! use-sync-external-store/shim/index.js */ \"(ssr)/./node_modules/.pnpm/use-sync-external-store@1.5.0_react@18.3.1/node_modules/use-sync-external-store/shim/index.js\");\n/* harmony import */ var _microsoft_fetch_event_source__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @microsoft/fetch-event-source */ \"(ssr)/./node_modules/.pnpm/@microsoft+fetch-event-source@2.0.1/node_modules/@microsoft/fetch-event-source/lib/esm/fetch.js\");\n\n\n\n\n\n\n\nconst useLatest = (value)=>{\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n    ref.current = value;\n    return ref;\n};\n\nvar _window_navigator, _window;\nfunction isFunction(val) {\n    return typeof val === 'function';\n}\nfunction isString(val) {\n    return typeof val === 'string';\n}\nconst isDev =  true || 0;\nconst isBrowser = typeof window !== 'undefined';\nconst isNavigator = typeof navigator !== 'undefined';\nfunction noop() {}\nconst isIOS = isBrowser && ((_window = window) == null ? void 0 : (_window_navigator = _window.navigator) == null ? void 0 : _window_navigator.userAgent) && /iP(?:ad|hone|od)/.test(window.navigator.userAgent);\n\nfunction on(obj, ...args) {\n    if (obj && obj.addEventListener) {\n        obj.addEventListener(...args);\n    }\n}\nfunction off(obj, ...args) {\n    if (obj && obj.removeEventListener) {\n        obj.removeEventListener(...args);\n    }\n}\nconst defaultWindow =  isBrowser ? window : undefined;\n\nconst defaultOptions$1 = {};\nfunction defaultOnError(e) {\n    console.error(e);\n}\n\nfunction getTargetElement(target, defaultElement) {\n    if (!isBrowser) {\n        return undefined;\n    }\n    if (!target) {\n        return defaultElement;\n    }\n    let targetElement;\n    if (isFunction(target)) {\n        targetElement = target();\n    } else if ('current' in target) {\n        targetElement = target.current;\n    } else {\n        targetElement = target;\n    }\n    return targetElement;\n}\n\nconst useCustomCompareEffect = (effect, deps, depsEqual)=>{\n    if (true) {\n        if (!Array.isArray(deps) || !deps.length) {\n            console.warn('`useCustomCompareEffect` should not be used with no dependencies. Use React.useEffect instead.');\n        }\n        if (typeof depsEqual !== 'function') {\n            console.warn('`useCustomCompareEffect` should be used with depsEqual callback for comparing deps list');\n        }\n    }\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(undefined);\n    if (!ref.current || !depsEqual(deps, ref.current)) {\n        ref.current = deps;\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(effect, ref.current);\n};\n\nconst useDeepCompareEffect = (effect, deps)=>{\n    if (true) {\n        if (!Array.isArray(deps) || !deps.length) {\n            console.warn('`useDeepCompareEffect` should not be used with no dependencies. Use React.useEffect instead.');\n        }\n    }\n    useCustomCompareEffect(effect, deps, lodash_es__WEBPACK_IMPORTED_MODULE_4__[\"default\"]);\n};\n\nfunction useEventListener(eventName, handler, element, options = defaultOptions$1) {\n    const savedHandler = useLatest(handler);\n    useDeepCompareEffect(()=>{\n        const targetElement = getTargetElement(element, defaultWindow);\n        if (!(targetElement && targetElement.addEventListener)) {\n            return;\n        }\n        const eventListener = (event)=>savedHandler.current(event);\n        on(targetElement, eventName, eventListener, options);\n        return ()=>{\n            if (!(targetElement && targetElement.removeEventListener)) {\n                return;\n            }\n            off(targetElement, eventName, eventListener);\n        };\n    }, [\n        eventName,\n        element,\n        options\n    ]);\n}\n\nconst useMount = (fn)=>{\n    if (isDev) {\n        if (!isFunction(fn)) {\n            console.error(`useMount: parameter \\`fn\\` expected to be a function, but got \"${typeof fn}\".`);\n        }\n    }\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        fn == null ? void 0 : fn();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, []);\n};\n\nconst useActiveElement = ()=>{\n    const [active, setActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const listener = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        var _window;\n        setActive((_window = window) == null ? void 0 : _window.document.activeElement);\n    }, []);\n    useEventListener('blur', listener, ()=>window, true);\n    useEventListener('focus', listener, ()=>window, true);\n    useMount(()=>{\n        var _window;\n        setActive((_window = window) == null ? void 0 : _window.document.activeElement);\n    });\n    return active;\n};\n\nfunction useMountedState() {\n    const mountedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const get = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>mountedRef.current, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        mountedRef.current = true;\n        return ()=>{\n            mountedRef.current = false;\n        };\n    }, []);\n    return get;\n}\n\nfunction asyncGeneratorStep$7(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$7(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$7(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$7(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nconst useAsyncEffect = (effect, cleanup = noop, deps)=>{\n    const mounted = useMountedState();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const execute = /*#__PURE__*/ _async_to_generator$7(function*() {\n            if (!mounted()) {\n                return;\n            }\n            yield effect();\n        });\n        execute();\n        return ()=>{\n            cleanup();\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, deps);\n};\n\nconst listerOptions = {\n    passive: true\n};\nconst useClickOutside = (target, handler, enabled = true)=>{\n    const savedHandler = useLatest(handler);\n    const listener = (event)=>{\n        if (!enabled) {\n            return;\n        }\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        const elements = event.composedPath();\n        if (element === event.target || elements.includes(element)) {\n            return;\n        }\n        savedHandler.current(event);\n    };\n    useEventListener('mousedown', listener, defaultWindow, listerOptions);\n    useEventListener('touchstart', listener, defaultWindow, listerOptions);\n};\n\nfunction getInitialState$5(key, defaultValue) {\n    // Prevent a React hydration mismatch when a default value is provided.\n    if (defaultValue !== undefined) {\n        return defaultValue;\n    }\n    if (isBrowser) {\n        return js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(key);\n    }\n    if (true) {\n        console.warn('`useCookie` When server side rendering, defaultValue should be defined to prevent a hydration mismatches.');\n    }\n    return '';\n}\nconst useCookie = (key, options = defaultOptions$1, defaultValue)=>{\n    const [cookieValue, setCookieValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState$5(key, defaultValue));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const getStoredValue = ()=>{\n            const raw = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(key);\n            if (raw !== undefined && raw !== null) {\n                return raw;\n            } else {\n                if (defaultValue === undefined) {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(key);\n                } else {\n                    js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(key, defaultValue, options);\n                }\n                return defaultValue;\n            }\n        };\n        setCookieValue(getStoredValue());\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        defaultValue,\n        key,\n        JSON.stringify(options)\n    ]);\n    const updateCookie = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newValue)=>{\n        const value = isFunction(newValue) ? newValue(cookieValue) : newValue;\n        if (value === undefined) {\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].remove(key);\n        } else {\n            js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].set(key, value, options);\n        }\n        setCookieValue(value);\n    }, // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        key,\n        cookieValue,\n        JSON.stringify(options)\n    ]);\n    const refreshCookie = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const cookieValue = js_cookie__WEBPACK_IMPORTED_MODULE_1__[\"default\"].get(key);\n        if (isString(cookieValue)) {\n            setCookieValue(cookieValue);\n        }\n    }, [\n        key\n    ]);\n    return [\n        cookieValue,\n        updateCookie,\n        refreshCookie\n    ];\n};\n\nconst useIsomorphicLayoutEffect = isBrowser ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * keep function reference immutable\n */ const useEvent = (fn)=>{\n    if (isDev) {\n        if (!isFunction(fn)) {\n            console.error(`useEvent expected parameter is a function, got ${typeof fn}`);\n        }\n    }\n    const handlerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(fn);\n    useIsomorphicLayoutEffect(()=>{\n        handlerRef.current = fn;\n    }, [\n        fn\n    ]);\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((...args)=>{\n        const fn = handlerRef.current;\n        return fn(...args);\n    }, []);\n};\n\nconst useInterval = (callback, delay, options = defaultOptions$1)=>{\n    const { immediate, controls } = options;\n    const savedCallback = useLatest(callback);\n    const isActive = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const timer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const clean = ()=>{\n        timer.current && clearInterval(timer.current);\n    };\n    const resume = useEvent(()=>{\n        isActive.current = true;\n        timer.current = setInterval(()=>savedCallback.current(), delay || 0);\n    });\n    const pause = useEvent(()=>{\n        isActive.current = false;\n        clean();\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (immediate) {\n            savedCallback.current();\n        }\n        if (controls) {\n            return;\n        }\n        if (delay !== null) {\n            resume();\n            return ()=>{\n                clean();\n            };\n        }\n        return undefined;\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        delay,\n        immediate\n    ]);\n    return {\n        isActive,\n        pause,\n        resume\n    };\n};\n\nfunction padZero(time) {\n    return `${time}`.length < 2 ? `0${time}` : `${time}`;\n}\nfunction getHMSTime(timeDiff) {\n    if (timeDiff <= 0) {\n        return [\n            '00',\n            '00',\n            '00'\n        ];\n    }\n    if (timeDiff > 100 * 3600) {\n        return [\n            '99',\n            '59',\n            '59'\n        ];\n    }\n    const hour = Math.floor(timeDiff / 3600);\n    const minute = Math.floor((timeDiff - hour * 3600) / 60);\n    const second = timeDiff - hour * 3600 - minute * 60;\n    return [\n        padZero(hour),\n        padZero(minute),\n        padZero(second)\n    ];\n}\nconst useCountDown = (time, format = getHMSTime, callback)=>{\n    const [remainTime, setRemainTime] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(time);\n    const [delay, setDelay] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1000);\n    useInterval(()=>{\n        if (remainTime <= 0) {\n            setDelay(null);\n            return;\n        }\n        setRemainTime(remainTime - 1);\n    }, delay);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (time > 0 && remainTime <= 0) {\n            callback && callback();\n        }\n    }, [\n        callback,\n        remainTime,\n        time\n    ]);\n    const [hour, minute, secoud] = format(remainTime);\n    return [\n        hour,\n        minute,\n        secoud\n    ];\n};\n\nconst useCounter = (initialValue = 0, max = null, min = null)=>{\n    // avoid exec init code every render\n    const initFunc = ()=>{\n        let init = typeof initialValue === 'function' ? initialValue() : initialValue;\n        typeof init !== 'number' && console.error(`initialValue has to be a number, got ${typeof initialValue}`);\n        if (typeof min === 'number') {\n            init = Math.max(init, min);\n        } else if (min !== null) {\n            console.error(`min has to be a number, got ${typeof min}`);\n        }\n        if (typeof max === 'number') {\n            init = Math.min(init, max);\n        } else if (max !== null) {\n            console.error(`max has to be a number, got ${typeof max}`);\n        }\n        return init;\n    };\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initFunc);\n    const set = useEvent((newState)=>{\n        setValue((v)=>{\n            let nextValue = typeof newState === 'function' ? newState(v) : newState;\n            if (typeof min === 'number') {\n                nextValue = Math.max(nextValue, min);\n            }\n            if (typeof max === 'number') {\n                nextValue = Math.min(nextValue, max);\n            }\n            return nextValue;\n        });\n    });\n    const inc = (delta = 1)=>{\n        set((value)=>value + delta);\n    };\n    const dec = (delta = 1)=>{\n        set((value)=>value - delta);\n    };\n    const reset = ()=>{\n        set(initFunc);\n    };\n    return [\n        value,\n        set,\n        inc,\n        dec,\n        reset\n    ];\n};\n\nconst defaultOptions = {\n    observe: false\n};\n\nfunction getInitialState$4(defaultValue) {\n    // Prevent a React hydration mismatch when a default value is provided.\n    if (defaultValue !== undefined) {\n        return defaultValue;\n    }\n    if (isBrowser) {\n        return '';\n    }\n    if (true) {\n        console.warn('`useCssVar` When server side rendering, defaultValue should be defined to prevent a hydration mismatches.');\n    }\n    return '';\n}\nconst useCssVar = (prop, target, defaultValue, options = defaultOptions)=>{\n    const { observe } = options;\n    const [variable, setVariable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState$4(defaultValue));\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const set = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((v)=>{\n        const element = getTargetElement(target);\n        if (element == null ? void 0 : element.style) {\n            element == null ? void 0 : element.style.setProperty(prop, v);\n            setVariable(v);\n        }\n    }, [\n        prop,\n        target\n    ]);\n    const updateCssVar = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const element = getTargetElement(target);\n        if (element) {\n            var _window_getComputedStyle_getPropertyValue;\n            const value = (_window_getComputedStyle_getPropertyValue = window.getComputedStyle(element).getPropertyValue(prop)) == null ? void 0 : _window_getComputedStyle_getPropertyValue.trim();\n            setVariable(value);\n        }\n    }, [\n        target,\n        prop\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _window_getComputedStyle_getPropertyValue;\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        const value = (_window_getComputedStyle_getPropertyValue = window.getComputedStyle(element).getPropertyValue(prop)) == null ? void 0 : _window_getComputedStyle_getPropertyValue.trim();\n        /** if var don't has value and defaultValue exist */ if (!value && defaultValue) {\n            set(defaultValue);\n        } else {\n            updateCssVar();\n        }\n        if (!observe) {\n            return;\n        }\n        observerRef.current = new MutationObserver(updateCssVar);\n        observerRef.current.observe(element, {\n            attributeFilter: [\n                'style',\n                'class'\n            ]\n        });\n        return ()=>{\n            if (observerRef.current) {\n                observerRef.current.disconnect();\n            }\n        };\n    }, [\n        observe,\n        target,\n        updateCssVar,\n        set,\n        defaultValue,\n        prop\n    ]);\n    return [\n        variable,\n        set\n    ];\n};\n\nconst useCycleList = (list, i = 0)=>{\n    const [index, setIndex] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(i);\n    const set = (i)=>{\n        const length = list.length;\n        const nextIndex = ((index + i) % length + length) % length;\n        setIndex(nextIndex);\n    };\n    const next = (i = 1)=>{\n        set(i);\n    };\n    const prev = (i = 1)=>{\n        set(-i);\n    };\n    return [\n        list[index],\n        next,\n        prev\n    ];\n};\n\nfunction guessSerializerType(rawInit) {\n    return rawInit == null || rawInit === undefined ? 'any' : rawInit instanceof Set ? 'set' : rawInit instanceof Map ? 'map' : rawInit instanceof Date ? 'date' : typeof rawInit === 'boolean' ? 'boolean' : typeof rawInit === 'string' ? 'string' : typeof rawInit === 'object' ? 'object' : Array.isArray(rawInit) ? 'object' : !Number.isNaN(rawInit) ? 'number' : 'any';\n}\n\nconst StorageSerializers = {\n    boolean: {\n        read: (v)=>v === 'true',\n        write: (v)=>String(v)\n    },\n    object: {\n        read: (v)=>JSON.parse(v),\n        write: (v)=>JSON.stringify(v)\n    },\n    number: {\n        read: (v)=>Number.parseFloat(v),\n        write: (v)=>String(v)\n    },\n    any: {\n        read: (v)=>v,\n        write: (v)=>String(v)\n    },\n    string: {\n        read: (v)=>v,\n        write: (v)=>String(v)\n    },\n    map: {\n        read: (v)=>new Map(JSON.parse(v)),\n        write: (v)=>JSON.stringify(Array.from(v.entries()))\n    },\n    set: {\n        read: (v)=>new Set(JSON.parse(v)),\n        write: (v)=>JSON.stringify(Array.from(v))\n    },\n    date: {\n        read: (v)=>new Date(v),\n        write: (v)=>v.toISOString()\n    }\n};\nfunction getInitialState$3(key, defaultValue, storage, serializer, onError) {\n    // Prevent a React hydration mismatch when a default value is provided.\n    if (defaultValue !== undefined) {\n        return defaultValue;\n    }\n    if (isBrowser) {\n        try {\n            const raw = storage == null ? void 0 : storage.getItem(key);\n            if (raw !== undefined && raw !== null) {\n                return serializer == null ? void 0 : serializer.read(raw);\n            }\n            return null;\n        } catch (error) {\n            onError == null ? void 0 : onError(error);\n        }\n    }\n    // A default value has not been provided, and you are rendering on the server, warn of a possible hydration mismatch when defaulting to false.\n    if (true) {\n        console.warn('`createStorage` When server side rendering, defaultValue should be defined to prevent a hydration mismatches.');\n    }\n    return null;\n}\nfunction useStorage(key, defaultValue, getStorage = ()=>isBrowser ? sessionStorage : undefined, options = defaultOptions$1) {\n    let storage;\n    const { onError = defaultOnError, effectStorageValue, mountStorageValue, listenToStorageChanges = true } = options;\n    const storageValue = mountStorageValue != null ? mountStorageValue : effectStorageValue;\n    try {\n        storage = getStorage();\n    } catch (err) {\n        onError(err);\n    }\n    const type = guessSerializerType(defaultValue);\n    var _options_serializer;\n    const serializer = (_options_serializer = options.serializer) != null ? _options_serializer : StorageSerializers[type];\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState$3(key, defaultValue, storage, serializer, onError));\n    useDeepCompareEffect(()=>{\n        var _ref;\n        const data = (_ref = storageValue ? isFunction(storageValue) ? storageValue() : storageValue : defaultValue) != null ? _ref : null;\n        const getStoredValue = ()=>{\n            try {\n                const raw = storage == null ? void 0 : storage.getItem(key);\n                if (raw !== undefined && raw !== null) {\n                    return serializer.read(raw);\n                } else {\n                    storage == null ? void 0 : storage.setItem(key, serializer.write(data));\n                    return data;\n                }\n            } catch (e) {\n                onError(e);\n            }\n        };\n        setState(getStoredValue());\n    }, [\n        key,\n        serializer,\n        storage,\n        onError,\n        storageValue\n    ]);\n    const updateState = useEvent((valOrFunc)=>{\n        const currentState = isFunction(valOrFunc) ? valOrFunc(state) : valOrFunc;\n        setState(currentState);\n        if (currentState === null) {\n            storage == null ? void 0 : storage.removeItem(key);\n        } else {\n            try {\n                storage == null ? void 0 : storage.setItem(key, serializer.write(currentState));\n            } catch (e) {\n                onError(e);\n            }\n        }\n    });\n    const listener = useEvent(()=>{\n        try {\n            const raw = storage == null ? void 0 : storage.getItem(key);\n            if (raw !== undefined && raw !== null) {\n                updateState(serializer.read(raw));\n            } else {\n                updateState(null);\n            }\n        } catch (e) {\n            onError(e);\n        }\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (listenToStorageChanges) {\n            window.addEventListener('storage', listener);\n            return ()=>window.removeEventListener('storage', listener);\n        }\n        return ()=>{};\n    }, [\n        listenToStorageChanges,\n        listener\n    ]);\n    return [\n        state,\n        updateState\n    ];\n}\n\nconst useDarkMode = (options)=>{\n    const { selector = 'html', attribute = 'class', classNameDark = '', classNameLight = '', storageKey = 'reactuses-color-scheme', storage = ()=>isBrowser ? localStorage : undefined, defaultValue = false } = options;\n    const value = ()=>{\n        return window.matchMedia('(prefers-color-scheme: dark)').matches;\n    };\n    const [dark, setDark] = useStorage(storageKey, defaultValue, storage, {\n        mountStorageValue: value\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _window;\n        const element = (_window = window) == null ? void 0 : _window.document.querySelector(selector);\n        if (!element) {\n            return;\n        }\n        if (attribute === 'class') {\n            dark && classNameDark && element.classList.add(classNameDark);\n            !dark && classNameLight && element.classList.add(classNameLight);\n        } else {\n            dark && classNameDark && element.setAttribute(attribute, classNameDark);\n            !dark && classNameLight && element.setAttribute(attribute, classNameLight);\n        }\n        return ()=>{\n            if (!element) {\n                return;\n            }\n            if (attribute === 'class') {\n                dark && classNameDark && element.classList.remove(classNameDark);\n                !dark && classNameLight && element.classList.remove(classNameLight);\n            } else {\n                dark && classNameDark && element.removeAttribute(attribute);\n                !dark && classNameLight && element.removeAttribute(attribute);\n            }\n        };\n    }, [\n        attribute,\n        classNameDark,\n        classNameLight,\n        dark,\n        selector\n    ]);\n    return [\n        dark,\n        ()=>setDark((dark)=>!dark),\n        setDark\n    ];\n};\n\nfunction useUnmount(fn) {\n    if (isDev) {\n        if (!isFunction(fn)) {\n            console.error(`useUnmount expected parameter is a function, got ${typeof fn}`);\n        }\n    }\n    const fnRef = useLatest(fn);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>()=>{\n            fnRef.current();\n        }, [\n        fnRef\n    ]);\n}\n\nconst useDebounceFn = (fn, wait, options)=>{\n    if (isDev) {\n        if (!isFunction(fn)) {\n            console.error(`useDebounceFn expected parameter is a function, got ${typeof fn}`);\n        }\n    }\n    const fnRef = useLatest(fn);\n    const debounced = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(0,lodash_es__WEBPACK_IMPORTED_MODULE_5__[\"default\"])((...args)=>{\n            return fnRef.current(...args);\n        }, wait, options), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        JSON.stringify(options),\n        wait\n    ]);\n    useUnmount(()=>{\n        debounced.cancel();\n    });\n    return {\n        run: debounced,\n        cancel: debounced.cancel,\n        flush: debounced.flush\n    };\n};\n\nconst useDebounce = (value, wait, options)=>{\n    const [debounced, setDebounced] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n    const { run } = useDebounceFn(()=>{\n        setDebounced(value);\n    }, wait, options);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        run();\n    }, [\n        run,\n        value\n    ]);\n    return debounced;\n};\n\nfunction getInitialState$2(defaultValue) {\n    // Prevent a React hydration mismatch when a default value is provided.\n    if (defaultValue !== undefined) {\n        return defaultValue;\n    }\n    if (isBrowser) {\n        return document.visibilityState;\n    }\n    if (true) {\n        console.warn('`useDocumentVisibility` When server side rendering, defaultValue should be defined to prevent a hydration mismatches.');\n    }\n    return 'visible';\n}\nfunction useDocumentVisibility(defaultValue) {\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState$2(defaultValue));\n    useEventListener('visibilitychange', ()=>{\n        setVisible(document.visibilityState);\n    }, ()=>document);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setVisible(document.visibilityState);\n    }, []);\n    return visible;\n}\n\nconst useDoubleClick = ({ target, latency = 300, onSingleClick = ()=>{}, onDoubleClick = ()=>{} })=>{\n    const handle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((onSingleClick, onDoubleClick)=>{\n        let count = 0;\n        return (e)=>{\n            // prevent ios double click slide\n            if (e.type === 'touchend') {\n                e.stopPropagation();\n                e.preventDefault();\n            }\n            count += 1;\n            setTimeout(()=>{\n                if (count === 1) {\n                    onSingleClick(e);\n                } else if (count === 2) {\n                    onDoubleClick(e);\n                }\n                count = 0;\n            }, latency);\n        };\n    }, [\n        latency\n    ]);\n    const handleClick = handle(onSingleClick, onDoubleClick);\n    const handleTouchEnd = handle(onSingleClick, onDoubleClick);\n    useEventListener('click', handleClick, target);\n    useEventListener('touchend', handleTouchEnd, target, {\n        passive: false\n    });\n};\n\nfunction isScrollX(node) {\n    if (!node) {\n        return false;\n    }\n    return getComputedStyle(node).overflowX === 'auto' || getComputedStyle(node).overflowX === 'scroll';\n}\nfunction isScrollY(node) {\n    if (!node) {\n        return false;\n    }\n    return getComputedStyle(node).overflowY === 'auto' || getComputedStyle(node).overflowY === 'scroll';\n}\nconst useDraggable = (target, options = {})=>{\n    const { draggingElement, containerElement } = options;\n    var _options_handle;\n    const draggingHandle = (_options_handle = options.handle) != null ? _options_handle : target;\n    var _options_initialValue;\n    const [position, setPositon] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)((_options_initialValue = options.initialValue) != null ? _options_initialValue : {\n        x: 0,\n        y: 0\n    });\n    useDeepCompareEffect(()=>{\n        var _options_initialValue;\n        setPositon((_options_initialValue = options.initialValue) != null ? _options_initialValue : {\n            x: 0,\n            y: 0\n        });\n    }, [\n        options.initialValue\n    ]);\n    const [pressedDelta, setPressedDelta] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const filterEvent = (e)=>{\n        if (options.pointerTypes) {\n            return options.pointerTypes.includes(e.pointerType);\n        }\n        return true;\n    };\n    const handleEvent = (e)=>{\n        if (options.preventDefault) {\n            e.preventDefault();\n        }\n        if (options.stopPropagation) {\n            e.stopPropagation();\n        }\n    };\n    const start = (e)=>{\n        var _container_getBoundingClientRect;\n        const element = getTargetElement(target);\n        if (!filterEvent(e) || !element) {\n            return;\n        }\n        if (options.exact && e.target !== element) {\n            return;\n        }\n        const container = getTargetElement(containerElement);\n        const containerRect = container == null ? void 0 : (_container_getBoundingClientRect = container.getBoundingClientRect) == null ? void 0 : _container_getBoundingClientRect.call(container);\n        const targetRect = element.getBoundingClientRect();\n        const pos = {\n            x: e.clientX - (container && containerRect ? targetRect.left - (containerRect == null ? void 0 : containerRect.left) + container.scrollLeft : targetRect.left),\n            y: e.clientY - (container && containerRect ? targetRect.top - containerRect.top + container.scrollTop : targetRect.top)\n        };\n        if ((options.onStart == null ? void 0 : options.onStart.call(options, pos, e)) === false) {\n            return;\n        }\n        setPressedDelta(pos);\n        handleEvent(e);\n    };\n    const move = (e)=>{\n        const element = getTargetElement(target);\n        if (!filterEvent(e) || !element) {\n            return;\n        }\n        if (!pressedDelta) {\n            return;\n        }\n        const container = getTargetElement(containerElement);\n        const targetRect = element.getBoundingClientRect();\n        let { x, y } = position;\n        x = e.clientX - pressedDelta.x;\n        y = e.clientY - pressedDelta.y;\n        if (container) {\n            const containerWidth = isScrollX(container) ? container.scrollWidth : container.clientWidth;\n            const containerHeight = isScrollY(container) ? container.scrollHeight : container.clientHeight;\n            x = Math.min(Math.max(0, x), containerWidth - targetRect.width);\n            y = Math.min(Math.max(0, y), containerHeight - targetRect.height);\n        }\n        setPositon({\n            x,\n            y\n        });\n        options.onMove == null ? void 0 : options.onMove.call(options, position, e);\n        handleEvent(e);\n    };\n    const end = (e)=>{\n        if (!filterEvent(e)) {\n            return;\n        }\n        if (!pressedDelta) {\n            return;\n        }\n        setPressedDelta(undefined);\n        options.onEnd == null ? void 0 : options.onEnd.call(options, position, e);\n        handleEvent(e);\n    };\n    useEventListener('pointerdown', start, draggingHandle, true);\n    useEventListener('pointermove', move, draggingElement, true);\n    useEventListener('pointerup', end, draggingElement, true);\n    return [\n        position.x,\n        position.y,\n        !!pressedDelta,\n        setPositon\n    ];\n};\n\nconst useDropZone = (target, onDrop)=>{\n    const [over, setOver] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const counter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    useEventListener('dragenter', (event)=>{\n        event.preventDefault();\n        counter.current += 1;\n        setOver(true);\n    }, target);\n    useEventListener('dragover', (event)=>{\n        event.preventDefault();\n    }, target);\n    useEventListener('dragleave', (event)=>{\n        event.preventDefault();\n        counter.current -= 1;\n        if (counter.current === 0) {\n            setOver(false);\n        }\n    }, target);\n    useEventListener('drop', (event)=>{\n        var _event_dataTransfer;\n        event.preventDefault();\n        counter.current = 0;\n        setOver(false);\n        var _event_dataTransfer_files;\n        const files = Array.from((_event_dataTransfer_files = (_event_dataTransfer = event.dataTransfer) == null ? void 0 : _event_dataTransfer.files) != null ? _event_dataTransfer_files : []);\n        onDrop == null ? void 0 : onDrop(files.length === 0 ? null : files);\n    }, target);\n    return over;\n};\n\nconst useResizeObserver = (target, callback, options = defaultOptions$1)=>{\n    const savedCallback = useLatest(callback);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n        }\n    }, []);\n    useDeepCompareEffect(()=>{\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        observerRef.current = new ResizeObserver(savedCallback.current);\n        observerRef.current.observe(element, options);\n        return stop;\n    }, [\n        savedCallback,\n        stop,\n        target,\n        options\n    ]);\n    return stop;\n};\n\nconst useElementBounding = (target, options = defaultOptions$1)=>{\n    const { reset = true, windowResize = true, windowScroll = true, immediate = true } = options;\n    const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [bottom, setBottom] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [left, setLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [right, setRight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [top, setTop] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [x, setX] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [y, setY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const update = useEvent(()=>{\n        const element = getTargetElement(target);\n        if (!element) {\n            if (reset) {\n                setHeight(0);\n                setBottom(0);\n                setLeft(0);\n                setRight(0);\n                setTop(0);\n                setWidth(0);\n                setX(0);\n                setY(0);\n            }\n            return;\n        }\n        const rect = element.getBoundingClientRect();\n        setHeight(rect.height);\n        setBottom(rect.bottom);\n        setLeft(rect.left);\n        setRight(rect.right);\n        setTop(rect.top);\n        setWidth(rect.width);\n        setX(rect.x);\n        setY(rect.y);\n    });\n    useResizeObserver(target, update);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (immediate) {\n            update();\n        }\n    }, [\n        immediate,\n        update\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (windowScroll) {\n            window.addEventListener('scroll', update, {\n                passive: true\n            });\n        }\n        if (windowResize) {\n            window.addEventListener('resize', update, {\n                passive: true\n            });\n        }\n        return ()=>{\n            if (windowScroll) {\n                window.removeEventListener('scroll', update);\n            }\n            if (windowResize) {\n                window.removeEventListener('resize', update);\n            }\n        };\n    }, [\n        update,\n        windowResize,\n        windowScroll\n    ]);\n    return {\n        height,\n        bottom,\n        left,\n        right,\n        top,\n        width,\n        x,\n        y,\n        update\n    };\n};\n\nconst useElementSize = (target, options = defaultOptions$1)=>{\n    const { box = 'content-box' } = options;\n    const [width, setWidth] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [height, setHeight] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    useResizeObserver(target, ([entry])=>{\n        const boxSize = box === 'border-box' ? entry.borderBoxSize : box === 'content-box' ? entry.contentBoxSize : entry.devicePixelContentBoxSize;\n        if (boxSize) {\n            setWidth(boxSize.reduce((acc, { inlineSize })=>acc + inlineSize, 0));\n            setHeight(boxSize.reduce((acc, { blockSize })=>acc + blockSize, 0));\n        } else {\n            // fallback\n            setWidth(entry.contentRect.width);\n            setHeight(entry.contentRect.height);\n        }\n    }, options);\n    return [\n        width,\n        height\n    ];\n};\n\nconst useIntersectionObserver = (target, callback, options = defaultOptions$1)=>{\n    const savedCallback = useLatest(callback);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n        }\n    }, []);\n    useDeepCompareEffect(()=>{\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        observerRef.current = new IntersectionObserver(savedCallback.current, options);\n        observerRef.current.observe(element);\n        return stop;\n    }, [\n        options\n    ]);\n    return stop;\n};\n\nconst useElementVisibility = (target, options = defaultOptions$1)=>{\n    const [visible, setVisible] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const callback = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((entries)=>{\n        const rect = entries[0].boundingClientRect;\n        setVisible(rect.top <= (window.innerHeight || document.documentElement.clientHeight) && rect.left <= (window.innerWidth || document.documentElement.clientWidth) && rect.bottom >= 0 && rect.right >= 0);\n    }, []);\n    const stop = useIntersectionObserver(target, callback, options);\n    return [\n        visible,\n        stop\n    ];\n};\n\nfunction useEventEmitter() {\n    const listeners = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    const _disposed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const _event = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)((listener)=>{\n        listeners.current.push(listener);\n        const disposable = {\n            dispose: ()=>{\n                if (!_disposed.current) {\n                    for(let i = 0; i < listeners.current.length; i++){\n                        if (listeners.current[i] === listener) {\n                            listeners.current.splice(i, 1);\n                            return;\n                        }\n                    }\n                }\n            }\n        };\n        return disposable;\n    });\n    const fire = (arg1, arg2)=>{\n        const queue = [];\n        for(let i = 0; i < listeners.current.length; i++){\n            queue.push(listeners.current[i]);\n        }\n        for(let i = 0; i < queue.length; i++){\n            queue[i].call(undefined, arg1, arg2);\n        }\n    };\n    const dispose = ()=>{\n        if (listeners.current.length !== 0) {\n            listeners.current.length = 0;\n        }\n        _disposed.current = true;\n    };\n    return [\n        _event.current,\n        fire,\n        dispose\n    ];\n}\n\nfunction useSupported(callback, sync = false) {\n    const [supported, setSupported] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const effect = sync ? useIsomorphicLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n    effect(()=>{\n        setSupported(Boolean(callback()));\n    }, []);\n    return supported;\n}\n\nfunction asyncGeneratorStep$6(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$6(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$6(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$6(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nconst useEyeDropper = ()=>{\n    const isSupported = useSupported(()=>typeof window !== 'undefined' && 'EyeDropper' in window, true);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ _async_to_generator$6(function*(options = {}) {\n        if (!isSupported) {\n            return {\n                sRGBHex: ''\n            };\n        }\n        const eyeDropper = new window.EyeDropper();\n        return eyeDropper.open(options);\n    }), [\n        isSupported\n    ]);\n    return [\n        isSupported,\n        open\n    ];\n};\n\nfunction useFavicon(href, baseUrl = '', rel = 'icon') {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const url = `${baseUrl}${href}`;\n        const element = document.head.querySelectorAll(`link[rel*=\"${rel}\"]`);\n        element.forEach((el)=>el.href = url);\n        if (element.length === 0) {\n            const link = document.createElement('link');\n            link.rel = rel;\n            link.href = url;\n            document.getElementsByTagName('head')[0].appendChild(link);\n        }\n    }, [\n        baseUrl,\n        href,\n        rel\n    ]);\n}\n\nfunction asyncGeneratorStep$5(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$5(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$5(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$5(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nfunction _extends$3() {\n    _extends$3 = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends$3.apply(this, arguments);\n}\nconst DEFAULT_OPTIONS = {\n    multiple: true,\n    accept: '*'\n};\nconst useFileDialog = (options = defaultOptions$1)=>{\n    const [files, setFiles] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const fileOpenPromiseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const resolveFileOpenPromiseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const initFn = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (typeof document === 'undefined') {\n            return undefined;\n        }\n        const input = document.createElement('input');\n        input.type = 'file';\n        input.onchange = (event)=>{\n            const result = event.target;\n            setFiles(result.files);\n            resolveFileOpenPromiseRef.current == null ? void 0 : resolveFileOpenPromiseRef.current.call(resolveFileOpenPromiseRef, result.files);\n        };\n        return input;\n    }, []);\n    inputRef.current = initFn();\n    const open = /*#__PURE__*/ _async_to_generator$5(function*(localOptions) {\n        if (!inputRef.current) {\n            return;\n        }\n        const _options = _extends$3({}, DEFAULT_OPTIONS, options, localOptions);\n        inputRef.current.multiple = _options.multiple;\n        inputRef.current.accept = _options.accept;\n        inputRef.current.capture = _options.capture;\n        fileOpenPromiseRef.current = new Promise((resolve)=>{\n            resolveFileOpenPromiseRef.current = resolve;\n        });\n        inputRef.current.click();\n        return fileOpenPromiseRef.current;\n    });\n    const reset = ()=>{\n        setFiles(null);\n        resolveFileOpenPromiseRef.current == null ? void 0 : resolveFileOpenPromiseRef.current.call(resolveFileOpenPromiseRef, null);\n        if (inputRef.current) {\n            inputRef.current.value = '';\n        }\n    };\n    return [\n        files,\n        open,\n        reset\n    ];\n};\n\nconst useFirstMountState = ()=>{\n    const isFirst = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(true);\n    if (isFirst.current) {\n        isFirst.current = false;\n        return true;\n    }\n    return isFirst.current;\n};\n\nconst useFocus = (target, initialValue = false)=>{\n    const [focus, innerSetFocus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    useEventListener('focus', ()=>innerSetFocus(true), target);\n    useEventListener('blur', ()=>innerSetFocus(false), target);\n    const setFocus = (value)=>{\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        if (!value) {\n            element.blur();\n        } else if (value) {\n            element.focus();\n        }\n    };\n    useMount(()=>{\n        setFocus(focus);\n    });\n    return [\n        focus,\n        setFocus\n    ];\n};\n\nconst useRafFn = (callback, initiallyActive = true)=>{\n    const raf = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const rafActivity = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const rafCallback = useLatest(callback);\n    const step = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((time)=>{\n        if (rafActivity.current) {\n            rafCallback.current(time);\n            raf.current = requestAnimationFrame(step);\n        }\n    }, [\n        rafCallback\n    ]);\n    const result = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>[\n            ()=>{\n                // stop\n                if (rafActivity.current) {\n                    rafActivity.current = false;\n                    raf.current && cancelAnimationFrame(raf.current);\n                }\n            },\n            ()=>{\n                // start\n                if (!rafActivity.current) {\n                    rafActivity.current = true;\n                    raf.current = requestAnimationFrame(step);\n                }\n            },\n            ()=>rafActivity.current\n        ], [\n        step\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (initiallyActive) {\n            result[1]();\n        }\n        return result[0];\n    }, [\n        initiallyActive,\n        result\n    ]);\n    return result;\n};\n\nfunction useFps(options = defaultOptions$1) {\n    const [fps, setFps] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    var _options_every;\n    const every = (_options_every = options.every) != null ? _options_every : 10;\n    const last = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(performance.now());\n    const ticks = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    useRafFn(()=>{\n        ticks.current += 1;\n        if (ticks.current >= every) {\n            const now = performance.now();\n            const diff = now - last.current;\n            setFps(Math.round(1000 / (diff / ticks.current)));\n            last.current = now;\n            ticks.current = 0;\n        }\n    });\n    return fps;\n}\n\nconst useFullscreen = (target, options = defaultOptions$1)=>{\n    const { onExit, onEnter } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const onChange = ()=>{\n        if (screenfull__WEBPACK_IMPORTED_MODULE_2__.isEnabled) {\n            const { isFullscreen } = screenfull__WEBPACK_IMPORTED_MODULE_2__;\n            if (isFullscreen) {\n                onEnter == null ? void 0 : onEnter();\n            } else {\n                screenfull__WEBPACK_IMPORTED_MODULE_2__.off('change', onChange);\n                onExit == null ? void 0 : onExit();\n            }\n            setState(isFullscreen);\n        }\n    };\n    const enterFullscreen = ()=>{\n        const el = getTargetElement(target);\n        if (!el) {\n            return;\n        }\n        if (screenfull__WEBPACK_IMPORTED_MODULE_2__.isEnabled) {\n            try {\n                screenfull__WEBPACK_IMPORTED_MODULE_2__.request(el);\n                screenfull__WEBPACK_IMPORTED_MODULE_2__.on('change', onChange);\n            } catch (error) {\n                console.error(error);\n            }\n        }\n    };\n    const exitFullscreen = ()=>{\n        if (screenfull__WEBPACK_IMPORTED_MODULE_2__.isEnabled) {\n            screenfull__WEBPACK_IMPORTED_MODULE_2__.exit();\n        }\n    };\n    const toggleFullscreen = ()=>{\n        if (state) {\n            exitFullscreen();\n        } else {\n            enterFullscreen();\n        }\n    };\n    useUnmount(()=>{\n        if (screenfull__WEBPACK_IMPORTED_MODULE_2__.isEnabled) {\n            screenfull__WEBPACK_IMPORTED_MODULE_2__.off('change', onChange);\n        }\n    });\n    return [\n        state,\n        {\n            enterFullscreen: useEvent(enterFullscreen),\n            exitFullscreen: useEvent(exitFullscreen),\n            toggleFullscreen: useEvent(toggleFullscreen),\n            isEnabled: screenfull__WEBPACK_IMPORTED_MODULE_2__.isEnabled\n        }\n    ];\n};\n\nconst initCoord = {\n    accuracy: 0,\n    latitude: Number.POSITIVE_INFINITY,\n    longitude: Number.POSITIVE_INFINITY,\n    altitude: null,\n    altitudeAccuracy: null,\n    heading: null,\n    speed: null\n};\nconst useGeolocation = (options = defaultOptions$1)=>{\n    const { enableHighAccuracy = true, maximumAge = 30000, timeout = 27000 } = options;\n    const isSupported = useSupported(()=>navigator && 'geolocation' in navigator);\n    const [coordinates, setCoordinates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initCoord);\n    const [locatedAt, setLocatedAt] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const updatePosition = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((position)=>{\n        setCoordinates(position.coords);\n        setLocatedAt(position.timestamp);\n        setError(null);\n    }, []);\n    const updateError = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((err)=>{\n        setCoordinates(initCoord);\n        setLocatedAt(null);\n        setError(err);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!isSupported) {\n            return;\n        }\n        navigator.geolocation.getCurrentPosition(updatePosition, updateError);\n        const watchId = navigator.geolocation.watchPosition(updatePosition, updateError, {\n            enableHighAccuracy,\n            maximumAge,\n            timeout\n        });\n        return ()=>{\n            if (watchId) {\n                navigator.geolocation.clearWatch(watchId);\n            }\n        };\n    }, [\n        enableHighAccuracy,\n        isSupported,\n        maximumAge,\n        timeout,\n        updateError,\n        updatePosition\n    ]);\n    return {\n        coordinates,\n        locatedAt,\n        error,\n        isSupported\n    };\n};\n\nconst useHover = (target)=>{\n    const [hovered, setHovered] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const onMouseEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>setHovered(true), []);\n    const onMouseLeave = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>setHovered(false), []);\n    useEventListener('mouseenter', onMouseEnter, target);\n    useEventListener('mouseleave', onMouseLeave, target);\n    return hovered;\n};\n\nconst defaultEvents$1 = [\n    'mousemove',\n    'mousedown',\n    'resize',\n    'keydown',\n    'touchstart',\n    'wheel'\n];\nconst oneMinute = 60e3;\nconst useIdle = (ms = oneMinute, initialState = false, events = defaultEvents$1)=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let mounted = true;\n        let timeout;\n        let localState = state;\n        const set = (newState)=>{\n            if (mounted) {\n                localState = newState;\n                setState(newState);\n            }\n        };\n        const onEvent = (0,lodash_es__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(()=>{\n            if (localState) {\n                set(false);\n            }\n            clearTimeout(timeout);\n            timeout = setTimeout(()=>set(true), ms);\n        }, 50);\n        const onVisibility = ()=>{\n            if (!document.hidden) {\n                onEvent();\n            }\n        };\n        for(let i = 0; i < events.length; i++){\n            on(window, events[i], onEvent);\n        }\n        on(document, 'visibilitychange', onVisibility);\n        timeout = setTimeout(()=>set(true), ms);\n        return ()=>{\n            mounted = false;\n            for(let i = 0; i < events.length; i++){\n                off(window, events[i], onEvent);\n            }\n            off(document, 'visibilitychange', onVisibility);\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        ms,\n        events\n    ]);\n    return state;\n};\n\nfunction useThrottleFn(fn, wait, options) {\n    if (isDev) {\n        if (!isFunction(fn)) {\n            console.error(`useThrottleFn expected parameter is a function, got ${typeof fn}`);\n        }\n    }\n    const fnRef = useLatest(fn);\n    const throttled = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>(0,lodash_es__WEBPACK_IMPORTED_MODULE_6__[\"default\"])((...args)=>{\n            return fnRef.current(...args);\n        }, wait, options), // eslint-disable-next-line react-hooks/exhaustive-deps\n    [\n        wait,\n        JSON.stringify(options)\n    ]);\n    useUnmount(()=>{\n        throttled.cancel();\n    });\n    return {\n        run: throttled,\n        cancel: throttled.cancel,\n        flush: throttled.flush\n    };\n}\n\n/**\n * We have to check if the scroll amount is close enough to some threshold in order to\n * more accurately calculate arrivedState. This is because scrollTop/scrollLeft are non-rounded\n * numbers, while scrollHeight/scrollWidth and clientHeight/clientWidth are rounded.\n * https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollHeight#determine_if_an_element_has_been_totally_scrolled\n */ const ARRIVED_STATE_THRESHOLD_PIXELS = 1;\nconst defaultListerOptions = {\n    capture: false,\n    passive: true\n};\nconst useScroll = (target, options = defaultOptions$1)=>{\n    const { throttle = 0, idle = 200, onStop = noop, onScroll = noop, offset = {\n        left: 0,\n        right: 0,\n        top: 0,\n        bottom: 0\n    }, eventListenerOptions = defaultListerOptions } = options;\n    const [x, setX] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [y, setY] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const [isScrolling, setIsScrolling] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [arrivedState, setArrivedState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        left: true,\n        right: false,\n        top: true,\n        bottom: false\n    });\n    const [directions, setDirections] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        left: false,\n        right: false,\n        top: false,\n        bottom: false\n    });\n    const { run: onScrollEnd } = useDebounceFn((e)=>{\n        setIsScrolling(false);\n        setDirections({\n            left: false,\n            right: false,\n            top: false,\n            bottom: false\n        });\n        onStop(e);\n    }, throttle + idle);\n    const onScrollHandler = useEvent((e)=>{\n        const eventTarget = e.target === document ? e.target.documentElement : e.target;\n        const scrollLeft = eventTarget.scrollLeft;\n        let scrollTop = eventTarget.scrollTop;\n        // patch for mobile compatible\n        if (e.target === document && !scrollTop) scrollTop = document.body.scrollTop;\n        setX(scrollLeft);\n        setY(scrollTop);\n        setDirections({\n            left: scrollLeft < x,\n            right: scrollLeft > x,\n            top: scrollTop < y,\n            bottom: scrollTop > y\n        });\n        setArrivedState({\n            left: scrollLeft <= 0 + (offset.left || 0),\n            right: scrollLeft + eventTarget.clientWidth >= eventTarget.scrollWidth - (offset.right || 0) - ARRIVED_STATE_THRESHOLD_PIXELS,\n            top: scrollTop <= 0 + (offset.top || 0),\n            bottom: scrollTop + eventTarget.clientHeight >= eventTarget.scrollHeight - (offset.bottom || 0) - ARRIVED_STATE_THRESHOLD_PIXELS\n        });\n        setIsScrolling(true);\n        onScrollEnd(e);\n        onScroll(e);\n    });\n    const { run: throttleOnScroll } = useThrottleFn(onScrollHandler, throttle);\n    useEventListener('scroll', throttle ? throttleOnScroll : onScrollHandler, target, eventListenerOptions);\n    return [\n        x,\n        y,\n        isScrolling,\n        arrivedState,\n        directions\n    ];\n};\n\nconst createUpdateEffect = (hook)=>(effect, deps)=>{\n        const isFirstMount = useFirstMountState();\n        hook(()=>{\n            if (!isFirstMount) {\n                return effect();\n            }\n        }, deps);\n    };\n\nconst useUpdateEffect = createUpdateEffect(react__WEBPACK_IMPORTED_MODULE_0__.useEffect);\n\nfunction asyncGeneratorStep$4(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$4(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$4(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$4(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nfunction _extends$2() {\n    _extends$2 = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends$2.apply(this, arguments);\n}\nconst useInfiniteScroll = (target, onLoadMore, options = defaultOptions$1)=>{\n    const savedLoadMore = useLatest(onLoadMore);\n    var _options_direction;\n    const direction = (_options_direction = options.direction) != null ? _options_direction : 'bottom';\n    var _options_distance;\n    const state = useScroll(target, _extends$2({}, options, {\n        offset: _extends$2({\n            [direction]: (_options_distance = options.distance) != null ? _options_distance : 0\n        }, options.offset)\n    }));\n    const di = state[3][direction];\n    useUpdateEffect(()=>{\n        const element = getTargetElement(target);\n        const fn = /*#__PURE__*/ _async_to_generator$4(function*() {\n            var _element_scrollHeight, _element_scrollWidth;\n            const previous = {\n                height: (_element_scrollHeight = element == null ? void 0 : element.scrollHeight) != null ? _element_scrollHeight : 0,\n                width: (_element_scrollWidth = element == null ? void 0 : element.scrollWidth) != null ? _element_scrollWidth : 0\n            };\n            yield savedLoadMore.current(state);\n            if (options.preserveScrollPosition && element) {\n                element.scrollTo({\n                    top: element.scrollHeight - previous.height,\n                    left: element.scrollWidth - previous.width\n                });\n            }\n        });\n        fn();\n    }, [\n        di,\n        options.preserveScrollPosition,\n        target\n    ]);\n};\n\nconst defaultEvents = [\n    'mousedown',\n    'mouseup',\n    'keydown',\n    'keyup'\n];\nconst useKeyModifier = (modifier, options = defaultOptions$1)=>{\n    const { events = defaultEvents, initial = false } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initial);\n    useMount(()=>{\n        events.forEach((listenEvent)=>{\n            on(document, listenEvent, (evt)=>{\n                if (typeof evt.getModifierState === 'function') {\n                    setState(evt.getModifierState(modifier));\n                }\n            });\n        });\n        return ()=>{\n            events.forEach((listenerEvent)=>{\n                off(document, listenerEvent, (evt)=>{\n                    if (typeof evt.getModifierState === 'function') {\n                        setState(evt.getModifierState(modifier));\n                    }\n                });\n            });\n        };\n    });\n    return state;\n};\n\nfunction useLocalStorage(key, defaultValue, options = defaultOptions$1) {\n    return useStorage(key, defaultValue, ()=>isBrowser ? localStorage : undefined, options);\n}\n\nfunction subscribe$1(callback) {\n    window.addEventListener('popstate', callback);\n    window.addEventListener('hashchange', callback);\n    return ()=>{\n        window.removeEventListener('popstate', callback);\n        window.removeEventListener('hashchange', callback);\n    };\n}\nconst useLocationSelector = (selector, /**\n   * @description server fallback\n   * @default undefined\n   */ fallback)=>{\n    return (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_3__.useSyncExternalStore)(subscribe$1, ()=>selector(location), ()=>fallback);\n};\n\nfunction isTouchEvent(ev) {\n    return 'touches' in ev;\n}\nfunction preventDefault$1(ev) {\n    if (!isTouchEvent(ev)) {\n        return;\n    }\n    if (ev.touches.length < 2 && ev.preventDefault) {\n        ev.preventDefault();\n    }\n}\nconst useLongPress = (callback, { isPreventDefault = true, delay = 300 } = defaultOptions$1)=>{\n    const timeout = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const target = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const start = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event)=>{\n        // prevent ghost click on mobile devices\n        if (isPreventDefault && event.target) {\n            on(event.target, 'touchend', preventDefault$1, {\n                passive: false\n            });\n            target.current = event.target;\n        }\n        timeout.current = setTimeout(()=>callback(event), delay);\n    }, [\n        callback,\n        delay,\n        isPreventDefault\n    ]);\n    const clear = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // clearTimeout and removeEventListener\n        timeout.current && clearTimeout(timeout.current);\n        if (isPreventDefault && target.current) {\n            off(target.current, 'touchend', preventDefault$1);\n        }\n    }, [\n        isPreventDefault\n    ]);\n    return {\n        onMouseDown: (e)=>start(e),\n        onTouchStart: (e)=>start(e),\n        onMouseUp: clear,\n        onMouseLeave: clear,\n        onTouchEnd: clear\n    };\n};\n\nconst defaultState$1 = {\n    x: 0,\n    y: 0,\n    width: 0,\n    height: 0,\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0\n};\nconst useMeasure = (target, options = defaultOptions$1)=>{\n    const [rect, setRect] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defaultState$1);\n    const stop = useResizeObserver(target, (entries)=>{\n        if (entries[0]) {\n            const { x, y, width, height, top, left, bottom, right } = entries[0].contentRect;\n            setRect({\n                x,\n                y,\n                width,\n                height,\n                top,\n                left,\n                bottom,\n                right\n            });\n        }\n    }, options);\n    return [\n        rect,\n        stop\n    ];\n};\n\nfunction asyncGeneratorStep$3(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$3(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$3(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$3(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nconst defaultConstints = {\n    audio: true,\n    video: true\n};\nconst useMediaDevices = (options = {})=>{\n    const { requestPermissions, constraints = defaultConstints } = options;\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        devices: []\n    });\n    const isSupported = useSupported(()=>navigator && navigator.mediaDevices && navigator.mediaDevices.enumerateDevices);\n    const permissionGranted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const stream = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const onChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        navigator.mediaDevices.enumerateDevices().then((devices)=>{\n            if (stream.current) {\n                stream.current.getTracks().forEach((t)=>t.stop());\n                stream.current = null;\n            }\n            setState({\n                devices: devices.map(({ deviceId, groupId, kind, label })=>({\n                        deviceId,\n                        groupId,\n                        kind,\n                        label\n                    }))\n            });\n        }).catch(noop);\n    }, []);\n    const ensurePermissions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ _async_to_generator$3(function*() {\n        if (!isSupported) {\n            return false;\n        }\n        if (permissionGranted.current) {\n            return true;\n        }\n        let state;\n        try {\n            state = (yield navigator.permissions.query({\n                name: 'camera'\n            })).state;\n        } catch (error) {\n            state = 'prompt';\n        }\n        if (state !== 'granted') {\n            stream.current = yield navigator.mediaDevices.getUserMedia(constraints);\n            onChange();\n            permissionGranted.current = true;\n        } else {\n            permissionGranted.current = false;\n        }\n        return permissionGranted.current;\n    }), [\n        onChange,\n        isSupported,\n        constraints\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!isSupported) {\n            return;\n        }\n        if (requestPermissions) {\n            ensurePermissions();\n        }\n        on(navigator.mediaDevices, 'devicechange', onChange);\n        onChange();\n        return ()=>{\n            off(navigator.mediaDevices, 'devicechange', onChange);\n        };\n    }, [\n        onChange,\n        isSupported,\n        requestPermissions,\n        ensurePermissions\n    ]);\n    return [\n        state,\n        ensurePermissions\n    ];\n};\n\nfunction getInitialState$1(query, defaultState) {\n    // Prevent a React hydration mismatch when a default value is provided by not defaulting to window.matchMedia(query).matches.\n    if (defaultState !== undefined) {\n        return defaultState;\n    }\n    if (isBrowser) {\n        return window.matchMedia(query).matches;\n    }\n    // A default value has not been provided, and you are rendering on the server, warn of a possible hydration mismatch when defaulting to false.\n    if (true) {\n        console.warn('`useMediaQuery` When server side rendering, defaultState should be defined to prevent a hydration mismatches.');\n    }\n    return false;\n}\nconst useMediaQuery = (query, defaultState)=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState$1(query, defaultState));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        let mounted = true;\n        const mql = window.matchMedia(query);\n        const onChange = ()=>{\n            if (!mounted) {\n                return;\n            }\n            setState(!!mql.matches);\n        };\n        if ('addEventListener' in mql) {\n            mql.addEventListener('change', onChange);\n        } else {\n            mql.addListener == null ? void 0 : mql.addListener.call(mql, onChange);\n        }\n        setState(mql.matches);\n        return ()=>{\n            mounted = false;\n            if ('removeEventListener' in mql) {\n                mql.removeEventListener('change', onChange);\n            } else {\n                mql.removeListener == null ? void 0 : mql.removeListener.call(mql, onChange);\n            }\n        };\n    }, [\n        query\n    ]);\n    return state;\n};\n\nfunction useRafState(initialState) {\n    const frame = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState);\n    const setRafState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((value)=>{\n        cancelAnimationFrame(frame.current);\n        frame.current = requestAnimationFrame(()=>{\n            setState(value);\n        });\n    }, []);\n    useUnmount(()=>{\n        cancelAnimationFrame(frame.current);\n    });\n    return [\n        state,\n        setRafState\n    ];\n}\n\nconst initState = {\n    screenX: Number.NaN,\n    screenY: Number.NaN,\n    clientX: Number.NaN,\n    clientY: Number.NaN,\n    pageX: Number.NaN,\n    pageY: Number.NaN,\n    elementX: Number.NaN,\n    elementY: Number.NaN,\n    elementH: Number.NaN,\n    elementW: Number.NaN,\n    elementPosX: Number.NaN,\n    elementPosY: Number.NaN\n};\nconst useMouse = (target)=>{\n    const [state, setState] = useRafState(initState);\n    useEventListener('mousemove', (event)=>{\n        const { screenX, screenY, clientX, clientY, pageX, pageY } = event;\n        const newState = {\n            screenX,\n            screenY,\n            clientX,\n            clientY,\n            pageX,\n            pageY,\n            elementX: Number.NaN,\n            elementY: Number.NaN,\n            elementH: Number.NaN,\n            elementW: Number.NaN,\n            elementPosX: Number.NaN,\n            elementPosY: Number.NaN\n        };\n        const targetElement = getTargetElement(target);\n        if (targetElement) {\n            const { left, top, width, height } = targetElement.getBoundingClientRect();\n            newState.elementPosX = left + window.pageXOffset;\n            newState.elementPosY = top + window.pageYOffset;\n            newState.elementX = pageX - newState.elementPosX;\n            newState.elementY = pageY - newState.elementPosY;\n            newState.elementW = width;\n            newState.elementH = height;\n        }\n        setState(newState);\n    }, ()=>document);\n    return state;\n};\n\nconst listenerOptions$2 = {\n    passive: true\n};\nconst useMousePressed = (target, options = defaultOptions$1)=>{\n    const { touch = true, drag = true, initialValue = false } = options;\n    const [pressed, setPressed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialValue);\n    const [sourceType, setSourceType] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const element = getTargetElement(target);\n    const onPressed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((srcType)=>()=>{\n            setPressed(true);\n            setSourceType(srcType);\n        }, []);\n    const onReleased = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setPressed(false);\n        setSourceType(null);\n    }, []);\n    useEventListener('mousedown', onPressed('mouse'), target, listenerOptions$2);\n    useEventListener('mouseleave', onReleased, ()=>window, listenerOptions$2);\n    useEventListener('mouseup', onReleased, ()=>window, listenerOptions$2);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (drag) {\n            element == null ? void 0 : element.addEventListener('dragstart', onPressed('mouse'), listenerOptions$2);\n            element == null ? void 0 : element.addEventListener('drop', onReleased, listenerOptions$2);\n            element == null ? void 0 : element.addEventListener('dragend', onReleased, listenerOptions$2);\n        }\n        if (touch) {\n            element == null ? void 0 : element.addEventListener('touchstart', onPressed('touch'), listenerOptions$2);\n            element == null ? void 0 : element.addEventListener('touchend', onReleased, listenerOptions$2);\n            element == null ? void 0 : element.addEventListener('touchcancel', onReleased, listenerOptions$2);\n        }\n        return ()=>{\n            if (drag) {\n                element == null ? void 0 : element.removeEventListener('dragstart', onPressed('mouse'));\n                element == null ? void 0 : element.removeEventListener('drop', onReleased);\n                element == null ? void 0 : element.removeEventListener('dragend', onReleased);\n            }\n            if (touch) {\n                element == null ? void 0 : element.removeEventListener('touchstart', onPressed('touch'));\n                element == null ? void 0 : element.removeEventListener('touchend', onReleased);\n                element == null ? void 0 : element.removeEventListener('touchcancel', onReleased);\n            }\n        };\n    }, [\n        drag,\n        onPressed,\n        onReleased,\n        touch,\n        element\n    ]);\n    return [\n        pressed,\n        sourceType\n    ];\n};\n\nconst useMutationObserver = (callback, target, options = defaultOptions$1)=>{\n    const callbackRef = useLatest(callback);\n    const observerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const stop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (observerRef.current) {\n            observerRef.current.disconnect();\n        }\n    }, []);\n    useDeepCompareEffect(()=>{\n        const element = getTargetElement(target);\n        if (!element) {\n            return;\n        }\n        observerRef.current = new MutationObserver(callbackRef.current);\n        observerRef.current.observe(element, options);\n        return stop;\n    }, [\n        options\n    ]);\n    return stop;\n};\n\nconst nav = isNavigator ? navigator : undefined;\nconst conn = nav && (nav.connection || nav.mozConnection || nav.webkitConnection);\nfunction getConnectionState(previousState) {\n    const online = nav == null ? void 0 : nav.onLine;\n    const previousOnline = previousState == null ? void 0 : previousState.online;\n    return {\n        online,\n        previous: previousOnline,\n        since: online !== previousOnline ? new Date() : previousState == null ? void 0 : previousState.since,\n        downlink: conn == null ? void 0 : conn.downlink,\n        downlinkMax: conn == null ? void 0 : conn.downlinkMax,\n        effectiveType: conn == null ? void 0 : conn.effectiveType,\n        rtt: conn == null ? void 0 : conn.rtt,\n        saveData: conn == null ? void 0 : conn.saveData,\n        type: conn == null ? void 0 : conn.type\n    };\n}\nconst useNetwork = ()=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getConnectionState);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const handleStateChange = ()=>{\n            setState(getConnectionState);\n        };\n        on(window, 'online', handleStateChange, {\n            passive: true\n        });\n        on(window, 'offline', handleStateChange, {\n            passive: true\n        });\n        if (conn) {\n            on(conn, 'change', handleStateChange, {\n                passive: true\n            });\n        }\n        return ()=>{\n            off(window, 'online', handleStateChange);\n            off(window, 'offline', handleStateChange);\n            if (conn) {\n                off(conn, 'change', handleStateChange);\n            }\n        };\n    }, []);\n    return state;\n};\n\nconst useObjectUrl = (object)=>{\n    const [url, setUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (object) {\n            setUrl(URL.createObjectURL(object));\n        }\n        return ()=>{\n            if (url) {\n                URL.revokeObjectURL(url);\n            }\n        };\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    }, [\n        object\n    ]);\n    return url;\n};\n\nconst record = new WeakSet();\nconst createOnceEffect = (hook)=>(effect, deps)=>{\n        const onceWrapper = ()=>{\n            const shouldStart = !record.has(effect);\n            if (shouldStart) {\n                record.add(effect);\n                return effect();\n            }\n        };\n        hook(()=>{\n            return onceWrapper();\n        }, deps);\n    };\n\nconst useOnceEffect = createOnceEffect(react__WEBPACK_IMPORTED_MODULE_0__.useEffect);\n\nconst useOnceLayoutEffect = createOnceEffect(react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect);\n\nconst useOnline = ()=>{\n    const { online } = useNetwork();\n    return online;\n};\n\nconst defaultState = {\n    angle: 0,\n    type: 'landscape-primary'\n};\nconst useOrientation = (initialState = defaultState)=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const screen = window.screen;\n        let mounted = true;\n        const onChange = ()=>{\n            if (mounted) {\n                const { orientation } = screen;\n                if (orientation) {\n                    const { angle, type } = orientation;\n                    setState({\n                        angle,\n                        type\n                    });\n                } else if (window.orientation !== undefined) {\n                    setState({\n                        angle: typeof window.orientation === 'number' ? window.orientation : 0,\n                        type: void 0\n                    });\n                }\n            }\n        };\n        on(window, 'orientationchange', onChange);\n        onChange();\n        return ()=>{\n            mounted = false;\n            off(window, 'orientationchange', onChange);\n        };\n    }, []);\n    const lockOrientation = (type)=>{\n        if (isBrowser) {\n            return;\n        }\n        if (!(window && 'screen' in window && 'orientation' in window.screen)) {\n            return Promise.reject(new Error('Not supported'));\n        }\n        return window.screen.orientation.lock(type);\n    };\n    const unlockOrientation = ()=>{\n        if (isBrowser) {\n            return;\n        }\n        if (!(window && 'screen' in window && 'orientation' in window.screen)) {\n            return;\n        }\n        return window.screen.orientation.unlock();\n    };\n    return [\n        state,\n        lockOrientation,\n        unlockOrientation\n    ];\n};\n\nfunction usePageLeave() {\n    const [isLeft, setIsLeft] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const handler = (event)=>{\n        if (!window) return;\n        event = event || window.event;\n        // @ts-expect-error missing types\n        const from = event.relatedTarget || event.toElement;\n        setIsLeft(!from);\n    };\n    useEventListener('mouseout', handler, ()=>window, {\n        passive: true\n    });\n    useEventListener('mouseleave', handler, ()=>document, {\n        passive: true\n    });\n    useEventListener('mouseenter', handler, ()=>document, {\n        passive: true\n    });\n    return isLeft;\n}\n\nconst usePermission = (permissionDesc)=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _navigator_permissions;\n        const desc = typeof permissionDesc === 'string' ? {\n            name: permissionDesc\n        } : permissionDesc;\n        let mounted = true;\n        let permissionStatus = null;\n        const onChange = ()=>{\n            if (!mounted) {\n                return;\n            }\n            setState(()=>{\n                var _permissionStatus_state;\n                return (_permissionStatus_state = permissionStatus == null ? void 0 : permissionStatus.state) != null ? _permissionStatus_state : '';\n            });\n        };\n        (_navigator_permissions = navigator.permissions) == null ? void 0 : _navigator_permissions.query(desc).then((status)=>{\n            permissionStatus = status;\n            on(permissionStatus, 'change', onChange);\n            onChange();\n        }).catch(noop);\n        return ()=>{\n            permissionStatus && off(permissionStatus, 'change', onChange);\n            mounted = false;\n            permissionStatus = null;\n        };\n    }, [\n        permissionDesc\n    ]);\n    return state;\n};\n\nconst usePreferredColorScheme = (defaultState = 'no-preference')=>{\n    const isLight = useMediaQuery('(prefers-color-scheme: light)', false);\n    const isDark = useMediaQuery('(prefers-color-scheme: dark)', false);\n    return isDark ? 'dark' : isLight ? 'light' : defaultState;\n};\n\nconst usePreferredContrast = (defaultState = 'no-preference')=>{\n    const isMore = useMediaQuery('(prefexrs-contrast: more)', false);\n    const isLess = useMediaQuery('(prefers-contrast: less)', false);\n    const isCustom = useMediaQuery('(prefers-contrast: custom)', false);\n    return isMore ? 'more' : isLess ? 'less' : isCustom ? 'custom' : defaultState;\n};\n\nfunction usePreferredDark(defaultState) {\n    return useMediaQuery('(prefers-color-scheme: dark)', defaultState);\n}\n\nfunction usePrevious(state) {\n    const ref = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        ref.current = state;\n    });\n    return ref.current;\n}\n\nfunction useReducedMotion(defaultState) {\n    return useMediaQuery('(prefers-reduced-motion: reduce)', defaultState);\n}\n\nconst updateReducer = (num)=>(num + 1) % 1000000;\nfunction useUpdate() {\n    const [, update] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(updateReducer, 0);\n    return update;\n}\n\nconst topVarName = '--reactuse-safe-area-top';\nconst rightVarName = '--reactuse-safe-area-right';\nconst bottomVarName = '--reactuse-safe-area-bottom';\nconst leftVarName = '--reactuse-safe-area-left';\nconst defaultElement = ()=>document.documentElement;\nfunction useScreenSafeArea() {\n    const top = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const right = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const bottom = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const left = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const forceUpdate = useUpdate();\n    useCssVar(topVarName, defaultElement, 'env(safe-area-inset-top, 0px)');\n    useCssVar(rightVarName, defaultElement, 'env(safe-area-inset-right, 0px)');\n    useCssVar(bottomVarName, defaultElement, 'env(safe-area-inset-bottom, 0px)');\n    useCssVar(leftVarName, defaultElement, 'env(safe-area-inset-left, 0px)');\n    const { run: update } = useDebounceFn(()=>{\n        top.current = getValue(topVarName);\n        right.current = getValue(rightVarName);\n        bottom.current = getValue(bottomVarName);\n        left.current = getValue(leftVarName);\n        forceUpdate();\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        update();\n    }, [\n        update\n    ]);\n    useEventListener('resize', update);\n    return [\n        top.current,\n        right.current,\n        bottom.current,\n        left.current,\n        update\n    ];\n}\nfunction getValue(position) {\n    return getComputedStyle(document.documentElement).getPropertyValue(position);\n}\n\nconst useScriptTag = (src, onLoaded = noop, options = defaultOptions$1)=>{\n    const { immediate = true, manual = false, type = 'text/javascript', async = true, crossOrigin, referrerPolicy, noModule, defer, attrs = {} } = options;\n    const scriptTag = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const _promise = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(src ? 'loading' : 'idle');\n    /**\n   * Load the script specified via `src`.\n   *\n   * @param waitForScriptLoad Whether if the Promise should resolve once the \"load\" event is emitted by the <script> attribute, or right after appending it to the DOM.\n   * @returns Promise<HTMLScriptElement>\n   */ const loadScript = (waitForScriptLoad)=>new Promise((resolve, reject)=>{\n            // Some little closure for resolving the Promise.\n            const resolveWithElement = (el)=>{\n                scriptTag.current = el;\n                resolve(el);\n                return el;\n            };\n            // Check if document actually exists, otherwise resolve the Promise (SSR Support).\n            if (!document) {\n                resolve(false);\n                return;\n            }\n            if (!src) {\n                setStatus('idle');\n                resolve(false);\n                return;\n            }\n            // Local variable defining if the <script> tag should be appended or not.\n            let shouldAppend = false;\n            let el = document.querySelector(`script[src=\"${src}\"]`);\n            // Script tag not found, preparing the element for appending\n            if (!el) {\n                el = document.createElement('script');\n                el.type = type;\n                el.async = async;\n                el.src = src;\n                // Optional attributes\n                if (defer) {\n                    el.defer = defer;\n                }\n                if (crossOrigin) {\n                    el.crossOrigin = crossOrigin;\n                }\n                if (noModule) {\n                    el.noModule = noModule;\n                }\n                if (referrerPolicy) {\n                    el.referrerPolicy = referrerPolicy;\n                }\n                Object.entries(attrs).forEach(([name, value])=>el == null ? void 0 : el.setAttribute(name, value));\n                // Enables shouldAppend\n                shouldAppend = true;\n            } else if (el.hasAttribute('data-loaded')) {\n                setStatus(el.getAttribute('data-status'));\n                resolveWithElement(el);\n            }\n            // Event listeners\n            el.addEventListener('error', (event)=>{\n                setStatus(event.type === 'load' ? 'ready' : 'error');\n                return reject(event);\n            });\n            el.addEventListener('abort', (event)=>{\n                setStatus(event.type === 'load' ? 'ready' : 'error');\n                return reject(event);\n            });\n            el.addEventListener('load', (event)=>{\n                setStatus(event.type === 'load' ? 'ready' : 'error');\n                el.setAttribute('data-loaded', 'true');\n                onLoaded(el);\n                resolveWithElement(el);\n            });\n            // Append the <script> tag to head.\n            if (shouldAppend) {\n                el = document.head.appendChild(el);\n            }\n            // If script load awaiting isn't needed, we can resolve the Promise.\n            if (!waitForScriptLoad) {\n                resolveWithElement(el);\n            }\n        });\n    /**\n   * Exposed singleton wrapper for `loadScript`, avoiding calling it twice.\n   *\n   * @param waitForScriptLoad Whether if the Promise should resolve once the \"load\" event is emitted by the <script> attribute, or right after appending it to the DOM.\n   * @returns Promise<HTMLScriptElement>\n   */ const load = (waitForScriptLoad = true)=>{\n        if (!_promise.current) {\n            _promise.current = loadScript(waitForScriptLoad);\n        }\n        return _promise.current;\n    };\n    /**\n   * Unload the script specified by `src`.\n   */ const unload = ()=>{\n        if (!document) {\n            return;\n        }\n        _promise.current = null;\n        if (scriptTag.current) {\n            scriptTag.current = null;\n        }\n        const el = document.querySelector(`script[src=\"${src}\"]`);\n        if (el) {\n            document.head.removeChild(el);\n        }\n    };\n    useMount(()=>{\n        if (immediate && !manual) {\n            load();\n        }\n    });\n    useUnmount(()=>{\n        if (!manual) {\n            unload();\n        }\n    });\n    return [\n        scriptTag.current,\n        status,\n        load,\n        unload\n    ];\n};\n\nfunction setScrollParam({ axis, parent, distance }) {\n    if (!parent && typeof document === 'undefined') {\n        return;\n    }\n    const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';\n    if (parent) {\n        parent[method] = distance;\n    } else {\n        const { body, documentElement } = document;\n        body[method] = distance;\n        documentElement[method] = distance;\n    }\n}\nfunction isScrollElement(axis, node) {\n    if (!node) {\n        return false;\n    }\n    const AXIS = axis === 'x' ? 'X' : 'Y';\n    return getComputedStyle(node)[`overflow${AXIS}`] === 'auto' || getComputedStyle(node)[`overflow${AXIS}`] === 'scroll';\n}\nconst cache = new Map();\nfunction getScrollParent(axis, node) {\n    if (!node || !node.parentElement) {\n        return null;\n    }\n    if (cache.has(node)) {\n        return cache.get(node) || null;\n    }\n    let parent = node.parentElement;\n    while(parent && !isScrollElement(axis, parent)){\n        parent = parent.parentElement;\n    }\n    if (parent) {\n        cache.set(node, parent);\n    }\n    return parent;\n}\nfunction getScrollStart({ axis, parent }) {\n    if (!parent && typeof document === 'undefined') {\n        return 0;\n    }\n    const method = axis === 'y' ? 'scrollTop' : 'scrollLeft';\n    if (parent) {\n        return parent[method];\n    }\n    const { body, documentElement } = document;\n    // while one of it has a value the second is equal 0\n    return body[method] + documentElement[method];\n}\n\nfunction easeInOutQuad(t) {\n    return t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t;\n}\n\nfunction getRelativePosition({ axis, target, parent, alignment, offset, isList }) {\n    if (!target || !parent && typeof document === 'undefined') {\n        return 0;\n    }\n    const isCustomParent = !!parent;\n    const parentElement = parent || document.body;\n    const parentPosition = parentElement.getBoundingClientRect();\n    const targetPosition = target.getBoundingClientRect();\n    const getDiff = (property)=>targetPosition[property] - parentPosition[property];\n    if (axis === 'y') {\n        const diff = getDiff('top');\n        if (diff === 0) {\n            return 0;\n        }\n        if (alignment === 'start') {\n            const distance = diff - offset;\n            const shouldScroll = distance <= targetPosition.height * (isList ? 0 : 1) || !isList;\n            return shouldScroll ? distance : 0;\n        }\n        const parentHeight = isCustomParent ? parentPosition.height : window.innerHeight;\n        if (alignment === 'end') {\n            const distance = diff + offset - parentHeight + targetPosition.height;\n            const shouldScroll = distance >= -targetPosition.height * (isList ? 0 : 1) || !isList;\n            return shouldScroll ? distance : 0;\n        }\n        if (alignment === 'center') {\n            return diff - parentHeight / 2 + targetPosition.height / 2;\n        }\n        return 0;\n    }\n    if (axis === 'x') {\n        const diff = getDiff('left');\n        if (diff === 0) {\n            return 0;\n        }\n        if (alignment === 'start') {\n            const distance = diff - offset;\n            const shouldScroll = distance <= targetPosition.width || !isList;\n            return shouldScroll ? distance : 0;\n        }\n        const parentWidth = isCustomParent ? parentPosition.width : window.innerWidth;\n        if (alignment === 'end') {\n            const distance = diff + offset - parentWidth + targetPosition.width;\n            const shouldScroll = distance >= -targetPosition.width || !isList;\n            return shouldScroll ? distance : 0;\n        }\n        if (alignment === 'center') {\n            return diff - parentWidth / 2 + targetPosition.width / 2;\n        }\n        return 0;\n    }\n    return 0;\n}\n\nconst listenerOptions$1 = {\n    passive: true\n};\nconst useScrollIntoView = (targetElement, { duration = 1250, axis = 'y', onScrollFinish, easing = easeInOutQuad, offset = 0, cancelable = true, isList = false } = defaultOptions$1, scrollElement)=>{\n    const frameID = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const startTime = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const shouldStop = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const reducedMotion = useReducedMotion(false);\n    const cancel = ()=>{\n        if (frameID.current) {\n            cancelAnimationFrame(frameID.current);\n        }\n    };\n    const element = getTargetElement(targetElement);\n    const scrollIntoView = ({ alignment = 'start' } = {})=>{\n        const parent = getTargetElement(scrollElement) || getScrollParent(axis, element);\n        shouldStop.current = false;\n        if (frameID.current) {\n            cancel();\n        }\n        var _getScrollStart;\n        const start = (_getScrollStart = getScrollStart({\n            parent,\n            axis\n        })) != null ? _getScrollStart : 0;\n        const change = getRelativePosition({\n            parent,\n            target: element,\n            axis,\n            alignment,\n            offset,\n            isList\n        }) - (parent ? 0 : start);\n        const animateScroll = ()=>{\n            if (startTime.current === 0) {\n                startTime.current = performance.now();\n            }\n            const now = performance.now();\n            const elapsed = now - startTime.current;\n            // easing timing progress\n            const t = reducedMotion || duration === 0 ? 1 : elapsed / duration;\n            const distance = start + change * easing(t);\n            setScrollParam({\n                parent,\n                axis,\n                distance\n            });\n            if (!shouldStop.current && t < 1) {\n                frameID.current = requestAnimationFrame(animateScroll);\n            } else {\n                typeof onScrollFinish === 'function' && onScrollFinish();\n                startTime.current = 0;\n                frameID.current = 0;\n                cancel();\n            }\n        };\n        animateScroll();\n    };\n    const handleStop = ()=>{\n        if (cancelable) {\n            shouldStop.current = true;\n        }\n    };\n    useEventListener('wheel', handleStop, null, listenerOptions$1);\n    useEventListener('touchmove', handleStop, null, listenerOptions$1);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>cancel, []);\n    return {\n        scrollIntoView,\n        cancel\n    };\n};\n\nfunction preventDefault(rawEvent) {\n    const e = rawEvent || window.event;\n    // Do not prevent if the event has more than one touch (usually meaning this is a multi touch gesture like pinch to zoom).\n    if (e.touches.length > 1) {\n        return true;\n    }\n    if (e.preventDefault) {\n        e.preventDefault();\n    }\n    return false;\n}\nconst useScrollLock = (target, initialState = false)=>{\n    const [locked, setLocked] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState);\n    const initialOverflowRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('scroll');\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const element = getTargetElement(target);\n        if (element) {\n            initialOverflowRef.current = element.style.overflow;\n            if (locked) {\n                element.style.overflow = 'hidden';\n            }\n        }\n    }, [\n        locked,\n        target\n    ]);\n    const lock = useEvent(()=>{\n        const element = getTargetElement(target);\n        if (!element || locked) {\n            return;\n        }\n        if (isIOS) {\n            element.addEventListener('touchmove', preventDefault, {\n                passive: false\n            });\n        }\n        setLocked(true);\n    });\n    const unlock = useEvent(()=>{\n        const element = getTargetElement(target);\n        if (!element || !locked) {\n            return;\n        }\n        if (isIOS) {\n            element.removeEventListener('touchmove', preventDefault);\n        }\n        element.style.overflow = initialOverflowRef.current;\n        setLocked(false);\n    });\n    const set = useEvent((flag)=>{\n        if (flag) {\n            lock();\n        } else {\n            unlock();\n        }\n    });\n    return [\n        locked,\n        set\n    ];\n};\n\nfunction useSessionStorage(key, defaultValue, options = defaultOptions$1) {\n    return useStorage(key, defaultValue, ()=>isBrowser ? sessionStorage : undefined, options);\n}\n\nfunction _extends$1() {\n    _extends$1 = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends$1.apply(this, arguments);\n}\nconst useSetState = (initialState)=>{\n    const [state, _setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(initialState);\n    const setState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((statePartial)=>_setState((current)=>_extends$1({}, current, typeof statePartial === 'function' ? statePartial(current) : statePartial)), []);\n    return [\n        state,\n        setState\n    ];\n};\n\nfunction useSticky(targetElement, { axis = 'y', nav = 0 }, scrollElement) {\n    const [isSticky, setSticky] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const { run: scrollHandler } = useThrottleFn(()=>{\n        const element = getTargetElement(targetElement);\n        if (!element) {\n            return;\n        }\n        const rect = element.getBoundingClientRect();\n        if (axis === 'y') {\n            setSticky((rect == null ? void 0 : rect.top) <= nav);\n        } else {\n            setSticky((rect == null ? void 0 : rect.left) <= nav);\n        }\n    }, 50);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const element = getTargetElement(targetElement);\n        const scrollParent = getTargetElement(scrollElement) || getScrollParent(axis, element);\n        if (!element || !scrollParent) {\n            return;\n        }\n        scrollParent.addEventListener('scroll', scrollHandler);\n        scrollHandler();\n        return ()=>{\n            scrollParent.removeEventListener('scroll', scrollHandler);\n        };\n    }, [\n        axis,\n        targetElement,\n        scrollElement,\n        scrollHandler\n    ]);\n    return [\n        isSticky,\n        setSticky\n    ];\n}\n\nconst useTextDirection = (options = defaultOptions$1)=>{\n    const { selector = 'html', initialValue = 'ltr' } = options;\n    const getValue = ()=>{\n        if (initialValue !== undefined) {\n            return initialValue;\n        }\n        if (isBrowser) {\n            var _document_querySelector, _document;\n            var _document_querySelector_getAttribute;\n            return (_document_querySelector_getAttribute = (_document = document) == null ? void 0 : (_document_querySelector = _document.querySelector(selector)) == null ? void 0 : _document_querySelector.getAttribute('dir')) != null ? _document_querySelector_getAttribute : initialValue;\n        }\n        // A default value has not been provided, and you are rendering on the server, warn of a possible hydration mismatch when defaulting to false.\n        if (true) {\n            console.warn('`useTextDirection` When server side rendering, defaultState should be defined to prevent a hydration mismatches.');\n        }\n        return initialValue;\n    };\n    const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getValue());\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        var _document_querySelector, _document;\n        var _document_querySelector_getAttribute;\n        setValue((_document_querySelector_getAttribute = (_document = document) == null ? void 0 : (_document_querySelector = _document.querySelector(selector)) == null ? void 0 : _document_querySelector.getAttribute('dir')) != null ? _document_querySelector_getAttribute : initialValue);\n    }, [\n        initialValue,\n        selector\n    ]);\n    const set = (value)=>{\n        if (!isBrowser) {\n            return;\n        }\n        if (value !== null) {\n            var _document_querySelector;\n            (_document_querySelector = document.querySelector(selector)) == null ? void 0 : _document_querySelector.setAttribute('dir', value);\n        } else {\n            var _document_querySelector1;\n            (_document_querySelector1 = document.querySelector(selector)) == null ? void 0 : _document_querySelector1.removeAttribute('dir');\n        }\n        setValue(value);\n    };\n    return [\n        value,\n        set\n    ];\n};\n\nconst useTextSelection = ()=>{\n    const [selection, setSelection] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const forceUpdate = useUpdate();\n    const handleSelectionChange = ()=>{\n        setSelection(document.getSelection());\n        // this is because `document.getSelection` will always return the same object\n        forceUpdate();\n    };\n    useEventListener('selectionchange', handleSelectionChange, ()=>document);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setSelection(document.getSelection());\n    }, []);\n    return selection;\n};\n\nconst useThrottle = (value, wait, options)=>{\n    const [throttled, setThrottled] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value);\n    const { run } = useThrottleFn(()=>{\n        setThrottled(value);\n    }, wait, options);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        run();\n    }, [\n        run,\n        value\n    ]);\n    return throttled;\n};\n\n/**\n * Wrapper for `setTimeout` with controls.\n *\n * @param cb\n * @param interval\n * @param options\n */ const useTimeoutFn = (cb, interval, options = defaultOptions$1)=>{\n    const { immediate = true } = options;\n    const [pending, setPending] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const savedCallback = useLatest(cb);\n    const timer = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const stop = useEvent(()=>{\n        // will still be true when component unmount\n        setPending(false);\n        if (timer.current) {\n            clearTimeout(timer.current);\n        }\n    });\n    const start = useEvent((...args)=>{\n        if (timer) {\n            clearTimeout(timer.current);\n        }\n        timer.current = setTimeout(()=>{\n            setPending(false);\n            savedCallback.current(...args);\n        }, interval);\n        setPending(true);\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (immediate) {\n            start();\n        }\n        return stop;\n    }, [\n        stop,\n        immediate,\n        interval,\n        start\n    ]);\n    return [\n        pending,\n        start,\n        stop\n    ];\n};\n\nconst useTimeout = (ms = 0, options = {})=>{\n    const update = useUpdate();\n    return useTimeoutFn(update, ms, options);\n};\n\nconst useTitle = (title)=>{\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        document.title = title;\n    }, [\n        title\n    ]);\n};\n\nfunction toggleReducer(state, nextValue) {\n    return typeof nextValue === 'boolean' ? nextValue : !state;\n}\nconst useToggle = (initialValue)=>{\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(toggleReducer, initialValue);\n};\n\nconst useUpdateLayoutEffect = createUpdateEffect(react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect);\n\nfunction asyncGeneratorStep$2(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$2(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$2(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$2(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nconst useWebNotification = (requestPermissions = false)=>{\n    const isSupported = useSupported(()=>!!window && 'Notification' in window);\n    const permissionGranted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const notificationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const show = (title, options = defaultOptions$1)=>{\n        // If either the browser does not support notifications or the user has\n        // not granted permission, do nothing:\n        if (!isSupported && !permissionGranted.current) {\n            return;\n        }\n        notificationRef.current = new Notification(title || '', options);\n        return notificationRef.current;\n    };\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (notificationRef.current) {\n            notificationRef.current.close();\n        }\n        notificationRef.current = null;\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        permissionGranted.current = isSupported && 'permission' in Notification && Notification.permission === 'granted';\n    }, [\n        isSupported\n    ]);\n    const ensurePermissions = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ _async_to_generator$2(function*() {\n        if (!isSupported) return;\n        if (!permissionGranted.current && Notification.permission !== 'denied') {\n            const result = yield Notification.requestPermission();\n            if (result === 'granted') permissionGranted.current = true;\n        }\n        return permissionGranted.current;\n    }), [\n        isSupported\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (requestPermissions) {\n            ensurePermissions();\n        }\n    }, [\n        requestPermissions,\n        ensurePermissions\n    ]);\n    useUnmount(close);\n    return {\n        isSupported,\n        show,\n        close,\n        ensurePermissions,\n        permissionGranted\n    };\n};\n\nfunction useWindowsFocus(defauleValue = false) {\n    const [focused, setFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(defauleValue);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setFocused(window.document.hasFocus());\n    }, []);\n    useEventListener('blur', ()=>{\n        setFocused(false);\n    });\n    useEventListener('focus', ()=>{\n        setFocused(true);\n    });\n    return focused;\n}\n\nconst listenerOptions = {\n    capture: false,\n    passive: true\n};\nfunction useWindowScroll() {\n    const [state, setState] = useRafState(()=>({\n            x: 0,\n            y: 0\n        }));\n    const handleScroll = ()=>{\n        setState({\n            x: window.scrollX,\n            y: window.scrollY\n        });\n    };\n    useEventListener('scroll', handleScroll, defaultWindow, listenerOptions);\n    // Set scroll at the first client-side load\n    useIsomorphicLayoutEffect(()=>{\n        handleScroll();\n    }, []);\n    return state;\n}\n\nfunction subscribe(callback) {\n    window.addEventListener('resize', callback);\n    return ()=>{\n        window.removeEventListener('resize', callback);\n    };\n}\nconst useWindowSize = ()=>{\n    const stateDependencies = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({}).current;\n    const previous = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n        width: 0,\n        height: 0\n    });\n    const isEqual = (prev, current)=>{\n        for(const _ in stateDependencies){\n            const t = _;\n            if (current[t] !== prev[t]) {\n                return false;\n            }\n        }\n        return true;\n    };\n    const cached = (0,use_sync_external_store_shim_index_js__WEBPACK_IMPORTED_MODULE_3__.useSyncExternalStore)(subscribe, ()=>{\n        const data = {\n            width: window.innerWidth,\n            height: window.innerHeight\n        };\n        if (!isEqual(previous.current, data)) {\n            previous.current = data;\n            return data;\n        }\n        return previous.current;\n    }, ()=>{\n        return previous.current;\n    });\n    return {\n        get width () {\n            stateDependencies.width = true;\n            return cached.width;\n        },\n        get height () {\n            stateDependencies.height = true;\n            return cached.height;\n        }\n    };\n};\n\nfunction asyncGeneratorStep$1(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator$1(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep$1(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep$1(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nconst useClipboard = ()=>{\n    const [text, setText] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const updateText = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        window.navigator.clipboard.readText().then((value)=>{\n            setText(value);\n        });\n    }, []);\n    useEventListener('copy', updateText);\n    useEventListener('cut', updateText);\n    const copy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(/*#__PURE__*/ _async_to_generator$1(function*(txt) {\n        setText(txt);\n        yield window.navigator.clipboard.writeText(txt);\n    }), []);\n    return [\n        text,\n        copy\n    ];\n};\n\nfunction getPlatform(userAgent) {\n    if (/iPad|iPhone|iPod|ios/i.test(userAgent)) {\n        return 'ios';\n    } else if (/android/i.test(userAgent)) {\n        return 'android';\n    } else {\n        return 'unknown';\n    }\n}\nconst usePlatform = ({ userAgent } = {\n    userAgent: ''\n})=>{\n    const [ua, setUa] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(userAgent || '');\n    const [platform, setPlatform] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(()=>{\n        if (userAgent) {\n            return getPlatform(userAgent);\n        }\n        return 'unknown';\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        setPlatform(getPlatform(navigator.userAgent));\n        setUa(navigator.userAgent);\n    }, []);\n    const isInMiniProgram = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return /miniprogram/i.test(ua);\n    }, [\n        ua\n    ]);\n    const isInWechat = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return /micromessenger/i.test(ua);\n    }, [\n        ua\n    ]);\n    const isiPhoneX = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        return /iPhoneX/i.test(ua);\n    }, [\n        ua\n    ]);\n    return {\n        platform,\n        isInMiniProgram,\n        isInWechat,\n        isiPhoneX\n    };\n};\n\nfunction useMobileLandscape() {\n    const [isMobileLandscape, setIsMobileLandscape] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [orientation] = useOrientation();\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const userAgent = window.navigator.userAgent;\n        const isMobile = /Mobi|Android｜iphone/i.test(userAgent);\n        setIsMobileLandscape(isMobile && orientation.type === 'landscape-primary');\n    }, [\n        orientation.type\n    ]);\n    return isMobileLandscape;\n}\n\nconst useControlled = (value, defaultValue, onChange)=>{\n    const [stateValue, setStateValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(value !== undefined ? value : defaultValue);\n    const isControlled = value !== undefined;\n    const onChangeRef = useLatest(onChange);\n    const setValue = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((newValue)=>{\n        if (!isControlled) {\n            setStateValue(newValue);\n        }\n        onChangeRef.current == null ? void 0 : onChangeRef.current.call(onChangeRef, newValue);\n    }, [\n        isControlled,\n        onChangeRef\n    ]);\n    return [\n        isControlled ? value : stateValue,\n        setValue\n    ];\n};\n\nfunction useDisclosure(props = {}) {\n    const { defaultOpen, isOpen: isOpenProp, onClose: onCloseProp, onOpen: onOpenProp, onChange = ()=>{} } = props;\n    const onOpenPropRef = useLatest(onOpenProp);\n    const onClosePropRef = useLatest(onCloseProp);\n    const [isOpen, setIsOpen] = useControlled(isOpenProp, defaultOpen || false, onChange);\n    const isControlled = isOpenProp !== undefined;\n    const onClose = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isControlled) {\n            setIsOpen(false);\n        }\n        onClosePropRef.current == null ? void 0 : onClosePropRef.current.call(onClosePropRef);\n    }, [\n        isControlled,\n        onClosePropRef,\n        setIsOpen\n    ]);\n    const onOpen = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!isControlled) {\n            setIsOpen(true);\n        }\n        onOpenPropRef.current == null ? void 0 : onOpenPropRef.current.call(onOpenPropRef);\n    }, [\n        isControlled,\n        onOpenPropRef,\n        setIsOpen\n    ]);\n    const onOpenChange = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const action = isOpen ? onClose : onOpen;\n        action();\n    }, [\n        isOpen,\n        onOpen,\n        onClose\n    ]);\n    return {\n        isOpen: !!isOpen,\n        onOpen,\n        onClose,\n        onOpenChange,\n        isControlled\n    };\n}\n\nconst useEventSource = (url, events = [], options = defaultOptions$1)=>{\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('DISCONNECTED');\n    const [event, setEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEventId, setLastEventId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const retries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const explicitlyClosed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const eventListenerRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    if (!eventListenerRef.current) {\n        eventListenerRef.current = new Map();\n    }\n    const clean = useEvent(()=>{\n        const listeners = eventListenerRef.current;\n        events.forEach((name)=>{\n            const handler = listeners == null ? void 0 : listeners.get(name);\n            if (handler) {\n                var _eventSourceRef_current;\n                (_eventSourceRef_current = eventSourceRef.current) == null ? void 0 : _eventSourceRef_current.removeEventListener(name, handler);\n            }\n        });\n    });\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        var _eventSourceRef_current;\n        setStatus('DISCONNECTED');\n        clean();\n        (_eventSourceRef_current = eventSourceRef.current) == null ? void 0 : _eventSourceRef_current.close();\n        eventSourceRef.current = null;\n        explicitlyClosed.current = true;\n    }, [\n        clean\n    ]);\n    const open = useEvent(()=>{\n        close();\n        explicitlyClosed.current = false;\n        retries.current = 0;\n        if (!eventSourceRef.current) {\n            eventSourceRef.current = new EventSource(url, {\n                withCredentials: options.withCredentials\n            });\n        }\n        const es = eventSourceRef.current;\n        es.onopen = ()=>{\n            setStatus('CONNECTED');\n            setError(null);\n        };\n        es.onmessage = (ev)=>{\n            setData(ev.data);\n            setLastEventId(ev.lastEventId);\n            setStatus('CONNECTED');\n        };\n        es.onerror = (err)=>{\n            setError(err);\n            setStatus('DISCONNECTED');\n            if (options.autoReconnect && !explicitlyClosed.current) {\n                const { retries: maxRetries = -1, delay = 1000, onFailed } = options.autoReconnect;\n                retries.current += 1;\n                if (typeof maxRetries === 'number' && (maxRetries < 0 || retries.current < maxRetries) || typeof maxRetries === 'function' && maxRetries()) {\n                    setTimeout(open, delay);\n                } else {\n                    onFailed == null ? void 0 : onFailed();\n                }\n            }\n        };\n        const listeners = eventListenerRef.current;\n        events.forEach((name)=>{\n            const handler = (event)=>{\n                setEvent(name);\n                var _event_data;\n                setData((_event_data = event.data) != null ? _event_data : null);\n            };\n            es.addEventListener(name, handler);\n            listeners == null ? void 0 : listeners.set(name, handler);\n        });\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (options.immediate !== false) {\n            open();\n        }\n        return close;\n    }, [\n        open,\n        close,\n        options.immediate\n    ]);\n    useUnmount(()=>{\n        close();\n    });\n    return {\n        eventSourceRef,\n        data,\n        error,\n        status,\n        lastEventId,\n        event,\n        close,\n        open\n    };\n};\n\nfunction assignRef(ref, value) {\n    if (ref == null) return;\n    if (typeof ref === 'function') {\n        ref(value);\n        return;\n    }\n    try {\n        ref.current = value;\n    } catch (error) {\n        throw new Error(`Cannot assign value '${value}' to ref '${ref}'`);\n    }\n}\nfunction mergeRefs(...refs) {\n    return (node)=>{\n        refs.forEach((ref)=>{\n            assignRef(ref, node);\n        });\n    };\n}\nfunction useMergedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(()=>mergeRefs(...refs), refs);\n}\n\n/**\n * @description copy from swr\n */ const use = react__WEBPACK_IMPORTED_MODULE_0__.use || ((thenable)=>{\n    switch(thenable.status){\n        case 'pending':\n            throw thenable;\n        case 'fulfilled':\n            return thenable.value;\n        case 'rejected':\n            throw thenable.reason;\n        default:\n            thenable.status = 'pending';\n            thenable.then((v)=>{\n                thenable.status = 'fulfilled';\n                thenable.value = v;\n            }, (e)=>{\n                thenable.status = 'rejected';\n                thenable.reason = e;\n            });\n            throw thenable;\n    }\n});\n\nfunction getInitialState(defaultState) {\n    // Prevent a React hydration mismatch when a default value is provided by not defaulting to window.matchMedia(query).matches.\n    if (defaultState !== undefined) {\n        return defaultState;\n    }\n    if (isBrowser) {\n        const navigator1 = window.navigator;\n        return navigator1.languages;\n    }\n    // A default value has not been provided, and you are rendering on the server, warn of a possible hydration mismatch when defaulting to false.\n    if (true) {\n        console.warn('`usePreferredLanguage` When server side rendering, defaultState should be defined to prevent a hydration mismatches.');\n    }\n    return [\n        'en'\n    ];\n}\nconst usePreferredLanguages = (defaultLanguages)=>{\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(getInitialState(defaultLanguages));\n    useEventListener('languagechange', ()=>{\n        setState(navigator.languages);\n    });\n    return state;\n};\n\nconst useBroadcastChannel = (options)=>{\n    const { name } = options;\n    const isSupported = useSupported(()=>window && 'BroadcastChannel' in window);\n    const [isClosed, setIsClosed] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [timeStamp, setTimeStamp] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n    const channelRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    const post = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((data)=>{\n        if (channelRef.current) {\n            channelRef.current.postMessage(data);\n        }\n    }, []);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (channelRef.current) {\n            channelRef.current.close();\n        }\n        setIsClosed(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (isSupported) {\n            channelRef.current = new BroadcastChannel(name);\n            setError(null);\n            const handleMessage = (e)=>{\n                setData(e.data);\n                // avoid data is same between two messages\n                setTimeStamp(Date.now());\n            };\n            const handleError = (e)=>{\n                setError(e);\n            };\n            const handleClose = ()=>{\n                setIsClosed(true);\n            };\n            channelRef.current.addEventListener('message', handleMessage, {\n                passive: true\n            });\n            channelRef.current.addEventListener('messageerror', handleError, {\n                passive: true\n            });\n            channelRef.current.addEventListener('close', handleClose);\n            return ()=>{\n                if (channelRef.current) {\n                    channelRef.current.removeEventListener('message', handleMessage);\n                    channelRef.current.removeEventListener('messageerror', handleError);\n                    channelRef.current.removeEventListener('close', handleClose);\n                    close();\n                }\n            };\n        }\n        return close;\n    }, [\n        isSupported,\n        name,\n        close\n    ]);\n    return {\n        isSupported,\n        channel: channelRef.current,\n        data,\n        post,\n        close,\n        error,\n        isClosed,\n        timeStamp\n    };\n};\n\nconst useDevicePixelRatio = ()=>{\n    const [pixelRatio, setPixelRatio] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(1);\n    const observe = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (!window) return;\n        setPixelRatio(window.devicePixelRatio);\n        const media = window.matchMedia(`(resolution: ${window.devicePixelRatio}dppx)`);\n        const handleChange = ()=>{\n            observe();\n        };\n        media.addEventListener('change', handleChange, {\n            once: true\n        });\n        return ()=>{\n            media.removeEventListener('change', handleChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        const cleanup = observe();\n        return cleanup;\n    }, [\n        observe\n    ]);\n    return {\n        pixelRatio\n    };\n};\n\nconst useElementByPoint = (options)=>{\n    const { x, y, document: doc = typeof document !== 'undefined' ? document : null, multiple = false, interval = 'requestAnimationFrame', immediate = true } = options;\n    const isSupported = useSupported(()=>{\n        if (multiple) return doc && 'elementsFromPoint' in doc;\n        return doc && 'elementFromPoint' in doc;\n    });\n    const [element, setElement] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(immediate);\n    const rafIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const intervalIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const getXY = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        // 需要判断 NaN\n        const currentX = typeof x === 'function' ? x() : x;\n        const currentY = typeof y === 'function' ? y() : y;\n        return {\n            x: Number.isNaN(currentX) ? 0 : currentX,\n            y: Number.isNaN(currentY) ? 0 : currentY\n        };\n    }, [\n        x,\n        y\n    ]);\n    const cb = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        const { x: currentX, y: currentY } = getXY();\n        var _doc_elementsFromPoint, _doc_elementFromPoint;\n        setElement(multiple ? (_doc_elementsFromPoint = doc == null ? void 0 : doc.elementsFromPoint(currentX, currentY)) != null ? _doc_elementsFromPoint : [] : (_doc_elementFromPoint = doc == null ? void 0 : doc.elementFromPoint(currentX, currentY)) != null ? _doc_elementFromPoint : null);\n    }, [\n        doc,\n        multiple,\n        getXY\n    ]);\n    const cleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        if (rafIdRef.current !== null) {\n            cancelAnimationFrame(rafIdRef.current);\n            rafIdRef.current = null;\n        }\n        if (intervalIdRef.current !== null) {\n            clearInterval(intervalIdRef.current);\n            intervalIdRef.current = null;\n        }\n    }, []);\n    const pause = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsActive(false);\n        cleanup();\n    }, [\n        cleanup\n    ]);\n    const resume = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(()=>{\n        setIsActive(true);\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (!isActive) {\n            return;\n        }\n        if (interval === 'requestAnimationFrame') {\n            const runRaf = ()=>{\n                cb();\n                rafIdRef.current = requestAnimationFrame(runRaf);\n            };\n            runRaf();\n        } else {\n            cb();\n            intervalIdRef.current = setInterval(cb, interval);\n        }\n        return cleanup;\n    }, [\n        isActive,\n        interval,\n        cb,\n        cleanup\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (immediate) {\n            resume();\n        }\n        return pause;\n    }, [\n        immediate,\n        resume,\n        pause\n    ]);\n    return {\n        isSupported,\n        element,\n        pause,\n        resume,\n        isActive\n    };\n};\n\nfunction asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n        var info = gen[key](arg);\n        var value = info.value;\n    } catch (error) {\n        reject(error);\n        return;\n    }\n    if (info.done) {\n        resolve(value);\n    } else {\n        Promise.resolve(value).then(_next, _throw);\n    }\n}\nfunction _async_to_generator(fn) {\n    return function() {\n        var self = this, args = arguments;\n        return new Promise(function(resolve, reject) {\n            var gen = fn.apply(self, args);\n            function _next(value) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n            }\n            function _throw(err) {\n                asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n            }\n            _next(undefined);\n        });\n    };\n}\nfunction _extends() {\n    _extends = Object.assign || function(target) {\n        for(var i = 1; i < arguments.length; i++){\n            var source = arguments[i];\n            for(var key in source){\n                if (Object.prototype.hasOwnProperty.call(source, key)) {\n                    target[key] = source[key];\n                }\n            }\n        }\n        return target;\n    };\n    return _extends.apply(this, arguments);\n}\nfunction _object_without_properties_loose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for(i = 0; i < sourceKeys.length; i++){\n        key = sourceKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        target[key] = source[key];\n    }\n    return target;\n}\nconst useFetchEventSource = (url, options = {})=>{\n    const [data, setData] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('DISCONNECTED');\n    const [event, setEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEventId, setLastEventId] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const retries = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const abortController = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const explicitlyClosed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const close = useEvent(()=>{\n        if (!explicitlyClosed.current) {\n            var _abortController_current;\n            explicitlyClosed.current = true;\n            (_abortController_current = abortController.current) == null ? void 0 : _abortController_current.abort();\n            abortController.current = null;\n            setStatus('DISCONNECTED');\n            options.onClose == null ? void 0 : options.onClose.call(options);\n        }\n    });\n    const open = useEvent(/*#__PURE__*/ _async_to_generator(function*() {\n        close();\n        setStatus('CONNECTING');\n        explicitlyClosed.current = false;\n        retries.current = 0;\n        // 创建新的 AbortController\n        abortController.current = new AbortController();\n        try {\n            // 从选项中提取 FetchEventSourceInit 相关的选项\n            const { immediate, autoReconnect, onOpen, onMessage, onError, onClose, withCredentials, body } = options, fetchOptions = _object_without_properties_loose(options, [\n                \"immediate\",\n                \"autoReconnect\",\n                \"onOpen\",\n                \"onMessage\",\n                \"onError\",\n                \"onClose\",\n                \"withCredentials\",\n                \"body\"\n            ]);\n            // 构建请求配置\n            const finalOptions = _extends({\n                method: options.method || 'GET',\n                headers: _extends({\n                    'Accept': 'text/event-stream',\n                    'Cache-Control': 'no-cache',\n                    'Connection': 'keep-alive'\n                }, options.headers),\n                signal: abortController.current.signal,\n                credentials: withCredentials ? 'include' : 'same-origin'\n            }, fetchOptions);\n            // 只在 POST 请求时添加 body\n            if (options.method === 'POST' && body) {\n                finalOptions.body = body;\n                finalOptions.headers = _extends({}, finalOptions.headers, {\n                    'Content-Type': 'application/json'\n                });\n            }\n            yield (0,_microsoft_fetch_event_source__WEBPACK_IMPORTED_MODULE_7__.fetchEventSource)(url.toString(), _extends({}, finalOptions, {\n                openWhenHidden: false,\n                onopen (response) {\n                    return _async_to_generator(function*() {\n                        if (response.ok) {\n                            setStatus('CONNECTED');\n                            setError(null);\n                            options.onOpen == null ? void 0 : options.onOpen.call(options);\n                        } else {\n                            const error = new Error(`Failed to connect: ${response.status} ${response.statusText}`);\n                            setError(error);\n                            throw error;\n                        }\n                    })();\n                },\n                onmessage (msg) {\n                    if (!explicitlyClosed.current) {\n                        setData(msg.data);\n                        var _msg_id;\n                        setLastEventId((_msg_id = msg.id) != null ? _msg_id : null);\n                        setEvent(msg.event || null);\n                        options.onMessage == null ? void 0 : options.onMessage.call(options, msg);\n                    }\n                },\n                onerror (err) {\n                    setError(err);\n                    setStatus('DISCONNECTED');\n                    const retryDelay = options.onError == null ? void 0 : options.onError.call(options, err);\n                    if (options.autoReconnect && !explicitlyClosed.current) {\n                        const { retries: maxRetries = -1, delay = 1000, onFailed } = options.autoReconnect;\n                        retries.current += 1;\n                        if (typeof maxRetries === 'number' && (maxRetries < 0 || retries.current < maxRetries) || typeof maxRetries === 'function' && maxRetries()) {\n                            return retryDelay != null ? retryDelay : delay;\n                        } else {\n                            onFailed == null ? void 0 : onFailed();\n                            throw err;\n                        }\n                    }\n                    throw err;\n                },\n                onclose () {\n                    if (!explicitlyClosed.current) {\n                        setStatus('DISCONNECTED');\n                        options.onClose == null ? void 0 : options.onClose.call(options);\n                    }\n                }\n            }));\n        } catch (err) {\n            // 只处理非主动关闭导致的错误\n            if (!explicitlyClosed.current) {\n                console.error('EventSource Error:', err);\n                setError(err);\n                setStatus('DISCONNECTED');\n            }\n        }\n    }));\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(()=>{\n        if (options.immediate !== false) {\n            open();\n        }\n        return ()=>{\n            // 组件卸载时关闭连接\n            close();\n        };\n    }, [\n        open,\n        close,\n        options.immediate\n    ]);\n    // 组件卸载时确保连接关闭\n    useUnmount(()=>{\n        close();\n    });\n    return {\n        data,\n        error,\n        status,\n        lastEventId,\n        event,\n        close,\n        open\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@reactuses+core@5.0.23_react@18.3.1/node_modules/@reactuses/core/dist/index.mjs\n");

/***/ })

};
;