"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry+equality@0.5.7";
exports.ids = ["vendor-chunks/@wry+equality@0.5.7"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   equal: () => (/* binding */ equal)\n/* harmony export */ });\nconst { toString, hasOwnProperty } = Object.prototype;\nconst fnToStr = Function.prototype.toString;\nconst previousComparisons = new Map();\n/**\n * Performs a deep equality check on two JavaScript values, tolerating cycles.\n */\nfunction equal(a, b) {\n    try {\n        return check(a, b);\n    }\n    finally {\n        previousComparisons.clear();\n    }\n}\n// Allow default imports as well.\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (equal);\nfunction check(a, b) {\n    // If the two values are strictly equal, our job is easy.\n    if (a === b) {\n        return true;\n    }\n    // Object.prototype.toString returns a representation of the runtime type of\n    // the given value that is considerably more precise than typeof.\n    const aTag = toString.call(a);\n    const bTag = toString.call(b);\n    // If the runtime types of a and b are different, they could maybe be equal\n    // under some interpretation of equality, but for simplicity and performance\n    // we just return false instead.\n    if (aTag !== bTag) {\n        return false;\n    }\n    switch (aTag) {\n        case '[object Array]':\n            // Arrays are a lot like other objects, but we can cheaply compare their\n            // lengths as a short-cut before comparing their elements.\n            if (a.length !== b.length)\n                return false;\n        // Fall through to object case...\n        case '[object Object]': {\n            if (previouslyCompared(a, b))\n                return true;\n            const aKeys = definedKeys(a);\n            const bKeys = definedKeys(b);\n            // If `a` and `b` have a different number of enumerable keys, they\n            // must be different.\n            const keyCount = aKeys.length;\n            if (keyCount !== bKeys.length)\n                return false;\n            // Now make sure they have the same keys.\n            for (let k = 0; k < keyCount; ++k) {\n                if (!hasOwnProperty.call(b, aKeys[k])) {\n                    return false;\n                }\n            }\n            // Finally, check deep equality of all child properties.\n            for (let k = 0; k < keyCount; ++k) {\n                const key = aKeys[k];\n                if (!check(a[key], b[key])) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Error]':\n            return a.name === b.name && a.message === b.message;\n        case '[object Number]':\n            // Handle NaN, which is !== itself.\n            if (a !== a)\n                return b !== b;\n        // Fall through to shared +a === +b case...\n        case '[object Boolean]':\n        case '[object Date]':\n            return +a === +b;\n        case '[object RegExp]':\n        case '[object String]':\n            return a == `${b}`;\n        case '[object Map]':\n        case '[object Set]': {\n            if (a.size !== b.size)\n                return false;\n            if (previouslyCompared(a, b))\n                return true;\n            const aIterator = a.entries();\n            const isMap = aTag === '[object Map]';\n            while (true) {\n                const info = aIterator.next();\n                if (info.done)\n                    break;\n                // If a instanceof Set, aValue === aKey.\n                const [aKey, aValue] = info.value;\n                // So this works the same way for both Set and Map.\n                if (!b.has(aKey)) {\n                    return false;\n                }\n                // However, we care about deep equality of values only when dealing\n                // with Map structures.\n                if (isMap && !check(aValue, b.get(aKey))) {\n                    return false;\n                }\n            }\n            return true;\n        }\n        case '[object Uint16Array]':\n        case '[object Uint8Array]': // Buffer, in Node.js.\n        case '[object Uint32Array]':\n        case '[object Int32Array]':\n        case '[object Int8Array]':\n        case '[object Int16Array]':\n        case '[object ArrayBuffer]':\n            // DataView doesn't need these conversions, but the equality check is\n            // otherwise the same.\n            a = new Uint8Array(a);\n            b = new Uint8Array(b);\n        // Fall through...\n        case '[object DataView]': {\n            let len = a.byteLength;\n            if (len === b.byteLength) {\n                while (len-- && a[len] === b[len]) {\n                    // Keep looping as long as the bytes are equal.\n                }\n            }\n            return len === -1;\n        }\n        case '[object AsyncFunction]':\n        case '[object GeneratorFunction]':\n        case '[object AsyncGeneratorFunction]':\n        case '[object Function]': {\n            const aCode = fnToStr.call(a);\n            if (aCode !== fnToStr.call(b)) {\n                return false;\n            }\n            // We consider non-native functions equal if they have the same code\n            // (native functions require === because their code is censored).\n            // Note that this behavior is not entirely sound, since !== function\n            // objects with the same code can behave differently depending on\n            // their closure scope. However, any function can behave differently\n            // depending on the values of its input arguments (including this)\n            // and its calling context (including its closure scope), even\n            // though the function object is === to itself; and it is entirely\n            // possible for functions that are not === to behave exactly the\n            // same under all conceivable circumstances. Because none of these\n            // factors are statically decidable in JavaScript, JS function\n            // equality is not well-defined. This ambiguity allows us to\n            // consider the best possible heuristic among various imperfect\n            // options, and equating non-native functions that have the same\n            // code has enormous practical benefits, such as when comparing\n            // functions that are repeatedly passed as fresh function\n            // expressions within objects that are otherwise deeply equal. Since\n            // any function created from the same syntactic expression (in the\n            // same code location) will always stringify to the same code\n            // according to fnToStr.call, we can reasonably expect these\n            // repeatedly passed function expressions to have the same code, and\n            // thus behave \"the same\" (with all the caveats mentioned above),\n            // even though the runtime function objects are !== to one another.\n            return !endsWith(aCode, nativeCodeSuffix);\n        }\n    }\n    // Otherwise the values are not equal.\n    return false;\n}\nfunction definedKeys(obj) {\n    // Remember that the second argument to Array.prototype.filter will be\n    // used as `this` within the callback function.\n    return Object.keys(obj).filter(isDefinedKey, obj);\n}\nfunction isDefinedKey(key) {\n    return this[key] !== void 0;\n}\nconst nativeCodeSuffix = \"{ [native code] }\";\nfunction endsWith(full, suffix) {\n    const fromIndex = full.length - suffix.length;\n    return fromIndex >= 0 &&\n        full.indexOf(suffix, fromIndex) === fromIndex;\n}\nfunction previouslyCompared(a, b) {\n    // Though cyclic references can make an object graph appear infinite from the\n    // perspective of a depth-first traversal, the graph still contains a finite\n    // number of distinct object references. We use the previousComparisons cache\n    // to avoid comparing the same pair of object references more than once, which\n    // guarantees termination (even if we end up comparing every object in one\n    // graph to every object in the other graph, which is extremely unlikely),\n    // while still allowing weird isomorphic structures (like rings with different\n    // lengths) a chance to pass the equality test.\n    let bSet = previousComparisons.get(a);\n    if (bSet) {\n        // Return true here because we can be sure false will be returned somewhere\n        // else if the objects are not equivalent.\n        if (bSet.has(b))\n            return true;\n    }\n    else {\n        previousComparisons.set(a, bSet = new Set);\n    }\n    bSet.add(b);\n    return false;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@wry+equality@0.5.7/node_modules/@wry/equality/lib/index.js\n");

/***/ })

};
;