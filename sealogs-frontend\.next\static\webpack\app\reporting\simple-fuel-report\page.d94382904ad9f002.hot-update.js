"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/simple-fuel-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/reporting/simple-fuel-report.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleFuelReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nconst tableHeadings = [\n    \"Vessel\",\n    \"Log Entry\",\n    \"Fuel Tank\",\n    \"Fuel Start\",\n    \"Fuel Added\",\n    \"Fuel End\",\n    \"Fuel Used\",\n    \"Comments\"\n];\nfunction SimpleFuelReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery)(_app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__.GET_SIMPLE_FUEL_REPORT_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            filter[\"startDate\"] = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"vehicleID\"] = {\n                in: selectedVessels.map((v)=>v.value)\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readLogBookEntries;\n        var _data_readLogBookEntries_nodes;\n        const fetchedData = (_data_readLogBookEntries_nodes = data === null || data === void 0 ? void 0 : (_data_readLogBookEntries = data.readLogBookEntries) === null || _data_readLogBookEntries === void 0 ? void 0 : _data_readLogBookEntries.nodes) !== null && _data_readLogBookEntries_nodes !== void 0 ? _data_readLogBookEntries_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const filteredItems = fetchedData.filter(function(item) {\n            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== \"0\";\n        });\n        if (filteredItems.length === 0) {\n            return [];\n        }\n        const items = [];\n        filteredItems.forEach((item)=>{\n            if (item.state !== \"Locked\") {\n                return;\n            }\n            const logBookDate = new Date(item.startDate);\n            const vessel = item.vehicle;\n            const fuelLogs = item.fuelLog.nodes.filter((item)=>item.fuelTank.id != 0);\n            const fuelTanks = fuelLogs.reduce((acc, log)=>{\n                return {\n                    ...acc,\n                    [log.fuelTank.id]: log.fuelTank.title\n                };\n            }, {});\n            const logBookEntrySections = item.logBookEntrySections.nodes;\n            const tripEvents = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.tripEvents.nodes\n                ];\n            }, []);\n            const sectionMemberComments = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.sectionMemberComments.nodes\n                ];\n            }, []).map((sectionComment)=>sectionComment === null || sectionComment === void 0 ? void 0 : sectionComment.comment).filter((value)=>value != null || value != \"\");\n            for(const key in fuelTanks){\n                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {\n                    const fuelTankName = fuelTanks[key];\n                    const fuelTankLogs = fuelLogs.filter((item)=>item.fuelTankID == key);\n                    const fuelLogStart = fuelTankLogs[0];\n                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1];\n                    var _fuelLogStart_fuelBefore;\n                    const fuelStart = (_fuelLogStart_fuelBefore = fuelLogStart === null || fuelLogStart === void 0 ? void 0 : fuelLogStart.fuelBefore) !== null && _fuelLogStart_fuelBefore !== void 0 ? _fuelLogStart_fuelBefore : 0;\n                    const fuelAdded = calculateFuelAddedFromTripEvents(tripEvents, key);\n                    var _fuelLogEnd_fuelAfter;\n                    const fuelEnd = (_fuelLogEnd_fuelAfter = fuelLogEnd === null || fuelLogEnd === void 0 ? void 0 : fuelLogEnd.fuelAfter) !== null && _fuelLogEnd_fuelAfter !== void 0 ? _fuelLogEnd_fuelAfter : 0;\n                    const fuelUsed = fuelStart + fuelAdded - fuelEnd;\n                    const reportItem = {\n                        logBookEntryID: item.id,\n                        vesselID: vessel.id,\n                        logbookDate: logBookDate,\n                        vesselName: vessel.title,\n                        fuelTankID: Number(key),\n                        fuelTankName: fuelTankName,\n                        fuelStart,\n                        fuelAdded,\n                        fuelEnd,\n                        fuelUsed,\n                        comments: sectionMemberComments.join(\", \")\n                    };\n                    items.push(reportItem);\n                }\n            }\n        });\n        return items;\n    }, [\n        data,\n        called,\n        loading\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"vessel\",\n            \"log entry\",\n            \"fuel tank\",\n            \"fuel start\",\n            \"fuel added\",\n            \"fuel end\",\n            \"fuel used\",\n            \"comments\"\n        ]);\n        reportData.forEach((item)=>{\n            var _item_comments;\n            csvEntries.push([\n                item.vesselName,\n                item.logbookDate.toISOString(),\n                item.fuelTankName,\n                item.fuelStart,\n                item.fuelAdded,\n                item.fuelEnd,\n                item.fuelUsed,\n                (_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\"\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const data = reportData.map(function(item) {\n            var _item_comments;\n            return [\n                item.vesselName + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/MM/YY\") + \"\",\n                item.fuelTankName + \"\",\n                item.fuelStart.toLocaleString(),\n                item.fuelAdded.toLocaleString(),\n                item.fuelEnd.toLocaleString(),\n                item.fuelUsed.toLocaleString(),\n                \"\".concat((_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\", \" \")\n            ];\n        });\n        const totalUsedFuel = reportData.reduce((acc, current)=>acc + current.fuelUsed, 0);\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers: [\n                [\n                    {\n                        content: \"Vessel\"\n                    },\n                    {\n                        content: \"Log Entry\"\n                    },\n                    {\n                        content: \"Fuel Tank\"\n                    },\n                    {\n                        content: \"Fuel Start\"\n                    },\n                    {\n                        content: \"Fuel Added\"\n                    },\n                    {\n                        content: \"Fuel End\"\n                    },\n                    {\n                        content: \"Fuel Used\"\n                    },\n                    {\n                        content: \"Comments\",\n                        styles: {\n                            cellWidth: 60\n                        }\n                    }\n                ]\n            ],\n            body: data,\n            footers: [\n                [\n                    {\n                        colSpan: 6,\n                        content: \"Total Fuel Used\"\n                    },\n                    {\n                        content: totalUsedFuel.toLocaleString()\n                    },\n                    {\n                        content: \"\"\n                    }\n                ]\n            ],\n            userOptions: {\n                showFoot: \"lastPage\"\n            }\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_8__.ListHeader, {\n                title: \"Simple Fuel Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_7__.Button, {\n                        variant: \"back\",\n                        onClick: ()=>router.push(\"/reporting\"),\n                        children: \"Back\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 297,\n                        columnNumber: 25\n                    }, void 0)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 296,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 293,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Card, {\n                className: \"mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardHeader, {\n                        className: \"gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Filter, {\n                                onChange: handleFilterOnChange,\n                                onClick: generateReport\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 21\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ExportButton, {\n                                onDownloadPdf: downloadPdf,\n                                onDownloadCsv: downloadCsv\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 21\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 306,\n                        columnNumber: 17\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CardContent, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Table, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                                        children: tableHeadings.map((heading)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableHead, {\n                                                children: heading\n                                            }, heading, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 37\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                        lineNumber: 319,\n                                        columnNumber: 29\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 25\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableContent, {\n                                    isLoading: called && loading,\n                                    reportData: reportData\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 25\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 317,\n                            columnNumber: 21\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 316,\n                        columnNumber: 17\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 305,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(SimpleFuelReport, \"4Sq0Rys+Pi85vYpHZIdEH/CExw8=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_9__.useLazyQuery\n    ];\n});\n_c = SimpleFuelReport;\nfunction TableContent(param) {\n    let { reportData, isLoading } = param;\n    _s1();\n    const totalFuelUsed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return reportData.reduce((acc, current)=>current.fuelUsed + acc, 0);\n    }, [\n        reportData\n    ]);\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: tableHeadings.length,\n                className: \"text-center  h-32\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 356,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n            lineNumber: 355,\n            columnNumber: 13\n        }, this);\n    }\n    if (reportData.length == 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                colSpan: tableHeadings.length,\n                className: \"text-center  h-32\",\n                children: \"No Data Available\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 368,\n                columnNumber: 17\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n            lineNumber: 367,\n            columnNumber: 13\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableBody, {\n                children: reportData.map((element, index)=>{\n                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                        className: \"group border-b  hover: \",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[15%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block ml-3\",\n                                    children: element.vesselName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 386,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(element.logbookDate).format(\"DD/M/YY\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 390,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelTankName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 398,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelStart.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelAdded.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelEnd.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 413,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 412,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[10%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.fuelUsed.toLocaleString()\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 29\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                                className: \"px-2 py-3 text-left w-[25%]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \" inline-block \",\n                                    children: element.comments\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 33\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 422,\n                                columnNumber: 29\n                            }, this)\n                        ]\n                    }, \"\".concat(element.logBookEntryID, \"-\").concat(element.fuelTankID, \"-\").concat(element.vesselID), true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                        lineNumber: 382,\n                        columnNumber: 25\n                    }, this);\n                })\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 379,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableFooter, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableRow, {\n                    className: \"group border-b\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left\",\n                            scope: \"col\",\n                            colSpan: 6,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block ml-3\",\n                                children: \"Total Fuel Used\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 437,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 433,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left w-[10%]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"inline-block \",\n                                children: totalFuelUsed.toLocaleString()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 25\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 439,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TableCell, {\n                            className: \"px-2 py-3 text-left w-[25%]\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 444,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 432,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 431,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(TableContent, \"7uTdTl0jLv1kvQbg8+bH+v904uQ=\");\n_c1 = TableContent;\nfunction getFuelAddedByFuelTankID(fuelLogs, fuelTankID) {\n    if (fuelLogs.length === 0) {\n        return 0;\n    }\n    const fuelTankLogs = fuelLogs.filter((log)=>log.fuelTankID == fuelTankID);\n    return fuelTankLogs.reduce((acc, log)=>acc + log.fuelAdded, 0);\n}\nfunction calculateFuelAddedFromTripEvents(tripEvents, fuelTankID) {\n    const fuelAddedLogs = tripEvents.map(function(tripEvent) {\n        if (tripEvent.eventCategory === \"RefuellingBunkering\") {\n            const fuelLogs = tripEvent.eventType_RefuellingBunkering.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"Tasking\") {\n            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"PassengerDropFacility\") {\n            const fuelLogs = tripEvent.eventType_PassengerDropFacility.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        return 0;\n    });\n    return fuelAddedLogs.reduce((acc, val)=>acc + val, 0);\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"SimpleFuelReport\");\n$RefreshReg$(_c1, \"TableContent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx\n"));

/***/ })

});