"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/simple-fuel-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/reporting/simple-fuel-report.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleFuelReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create columns for the fuel report table\nconst createFuelReportColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-medium\",\n                    children: item.vesselName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"logbookDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Log Entry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/M/YY\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelTankName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Tank\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelTankName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelStart\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Start\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelStart.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelAdded\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Added\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelAdded.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 108,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelEnd\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel End\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelEnd.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 120,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelUsed\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Used\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelUsed.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"comments\",\n            header: \"Comments\",\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.comments || \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 24\n                }, undefined);\n            }\n        }\n    ]);\n// Custom dropdown actions component for the fuel report\nfunction FuelReportFilterActions(param) {\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_13__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 161,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 160,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 167,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadPdf,\n                            children: \"Download PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 172,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadCsv,\n                            children: \"Download CSV\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 177,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 166,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 163,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 159,\n        columnNumber: 9\n    }, this);\n}\n_s(FuelReportFilterActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = FuelReportFilterActions;\nfunction SimpleFuelReport() {\n    _s1();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    // Create columns for the table\n    const columns = createFuelReportColumns();\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__.GET_SIMPLE_FUEL_REPORT_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            filter[\"startDate\"] = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"vehicleID\"] = {\n                in: selectedVessels.map((v)=>v.value)\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readLogBookEntries;\n        var _data_readLogBookEntries_nodes;\n        const fetchedData = (_data_readLogBookEntries_nodes = data === null || data === void 0 ? void 0 : (_data_readLogBookEntries = data.readLogBookEntries) === null || _data_readLogBookEntries === void 0 ? void 0 : _data_readLogBookEntries.nodes) !== null && _data_readLogBookEntries_nodes !== void 0 ? _data_readLogBookEntries_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const filteredItems = fetchedData.filter(function(item) {\n            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== \"0\";\n        });\n        if (filteredItems.length === 0) {\n            return [];\n        }\n        const items = [];\n        filteredItems.forEach((item)=>{\n            if (item.state !== \"Locked\") {\n                return;\n            }\n            const logBookDate = new Date(item.startDate);\n            const vessel = item.vehicle;\n            const fuelLogs = item.fuelLog.nodes.filter((item)=>item.fuelTank.id != 0);\n            const fuelTanks = fuelLogs.reduce((acc, log)=>{\n                return {\n                    ...acc,\n                    [log.fuelTank.id]: log.fuelTank.title\n                };\n            }, {});\n            const logBookEntrySections = item.logBookEntrySections.nodes;\n            const tripEvents = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.tripEvents.nodes\n                ];\n            }, []);\n            const sectionMemberComments = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.sectionMemberComments.nodes\n                ];\n            }, []).map((sectionComment)=>sectionComment === null || sectionComment === void 0 ? void 0 : sectionComment.comment).filter((value)=>value != null || value != \"\");\n            for(const key in fuelTanks){\n                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {\n                    const fuelTankName = fuelTanks[key];\n                    const fuelTankLogs = fuelLogs.filter((item)=>item.fuelTankID == key);\n                    const fuelLogStart = fuelTankLogs[0];\n                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1];\n                    var _fuelLogStart_fuelBefore;\n                    const fuelStart = (_fuelLogStart_fuelBefore = fuelLogStart === null || fuelLogStart === void 0 ? void 0 : fuelLogStart.fuelBefore) !== null && _fuelLogStart_fuelBefore !== void 0 ? _fuelLogStart_fuelBefore : 0;\n                    const fuelAdded = calculateFuelAddedFromTripEvents(tripEvents, key);\n                    var _fuelLogEnd_fuelAfter;\n                    const fuelEnd = (_fuelLogEnd_fuelAfter = fuelLogEnd === null || fuelLogEnd === void 0 ? void 0 : fuelLogEnd.fuelAfter) !== null && _fuelLogEnd_fuelAfter !== void 0 ? _fuelLogEnd_fuelAfter : 0;\n                    const fuelUsed = fuelStart + fuelAdded - fuelEnd;\n                    const reportItem = {\n                        logBookEntryID: item.id,\n                        vesselID: vessel.id,\n                        logbookDate: logBookDate,\n                        vesselName: vessel.title,\n                        fuelTankID: Number(key),\n                        fuelTankName: fuelTankName,\n                        fuelStart,\n                        fuelAdded,\n                        fuelEnd,\n                        fuelUsed,\n                        comments: sectionMemberComments.join(\", \")\n                    };\n                    items.push(reportItem);\n                }\n            }\n        });\n        return items;\n    }, [\n        data,\n        called,\n        loading\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"vessel\",\n            \"log entry\",\n            \"fuel tank\",\n            \"fuel start\",\n            \"fuel added\",\n            \"fuel end\",\n            \"fuel used\",\n            \"comments\"\n        ]);\n        reportData.forEach((item)=>{\n            var _item_comments;\n            csvEntries.push([\n                item.vesselName,\n                item.logbookDate.toISOString(),\n                item.fuelTankName,\n                item.fuelStart,\n                item.fuelAdded,\n                item.fuelEnd,\n                item.fuelUsed,\n                (_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\"\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const data = reportData.map(function(item) {\n            var _item_comments;\n            return [\n                item.vesselName + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/MM/YY\") + \"\",\n                item.fuelTankName + \"\",\n                item.fuelStart.toLocaleString(),\n                item.fuelAdded.toLocaleString(),\n                item.fuelEnd.toLocaleString(),\n                item.fuelUsed.toLocaleString(),\n                \"\".concat((_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\", \" \")\n            ];\n        });\n        const totalUsedFuel = reportData.reduce((acc, current)=>acc + current.fuelUsed, 0);\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers: [\n                [\n                    {\n                        content: \"Vessel\"\n                    },\n                    {\n                        content: \"Log Entry\"\n                    },\n                    {\n                        content: \"Fuel Tank\"\n                    },\n                    {\n                        content: \"Fuel Start\"\n                    },\n                    {\n                        content: \"Fuel Added\"\n                    },\n                    {\n                        content: \"Fuel End\"\n                    },\n                    {\n                        content: \"Fuel Used\"\n                    },\n                    {\n                        content: \"Comments\",\n                        styles: {\n                            cellWidth: 60\n                        }\n                    }\n                ]\n            ],\n            body: data,\n            footers: [\n                [\n                    {\n                        colSpan: 6,\n                        content: \"Total Fuel Used\"\n                    },\n                    {\n                        content: totalUsedFuel.toLocaleString()\n                    },\n                    {\n                        content: \"\"\n                    }\n                ]\n            ],\n            userOptions: {\n                showFoot: \"lastPage\"\n            }\n        });\n    };\n    // Calculate total fuel used for footer\n    const totalFuelUsed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return reportData.reduce((acc, current)=>current.fuelUsed + acc, 0);\n    }, [\n        reportData\n    ]);\n    // Create footer content for the table\n    const footerContent = reportData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n        className: \"group border-b\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-left font-medium\",\n                colSpan: 6,\n                children: \"Total Fuel Used\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 444,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-right font-medium\",\n                children: totalFuelUsed.toLocaleString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 449,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 452,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 443,\n        columnNumber: 13\n    }, this) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ListHeader, {\n                title: \"Simple Fuel Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FuelReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 458,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    noDataText: \"No fuel data found, try clicking generate report to view results\",\n                    showToolbar: true,\n                    footer: footerContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 468,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 467,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(SimpleFuelReport, \"Wum/bbG5w9gwx4tGcypM72vrKiw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c1 = SimpleFuelReport;\nfunction getFuelAddedByFuelTankID(fuelLogs, fuelTankID) {\n    if (fuelLogs.length === 0) {\n        return 0;\n    }\n    const fuelTankLogs = fuelLogs.filter((log)=>log.fuelTankID == fuelTankID);\n    return fuelTankLogs.reduce((acc, log)=>acc + log.fuelAdded, 0);\n}\nfunction calculateFuelAddedFromTripEvents(tripEvents, fuelTankID) {\n    const fuelAddedLogs = tripEvents.map(function(tripEvent) {\n        if (tripEvent.eventCategory === \"RefuellingBunkering\") {\n            const fuelLogs = tripEvent.eventType_RefuellingBunkering.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"Tasking\") {\n            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"PassengerDropFacility\") {\n            const fuelLogs = tripEvent.eventType_PassengerDropFacility.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        return 0;\n    });\n    return fuelAddedLogs.reduce((acc, val)=>acc + val, 0);\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"FuelReportFilterActions\");\n$RefreshReg$(_c1, \"SimpleFuelReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx\n"));

/***/ })

});