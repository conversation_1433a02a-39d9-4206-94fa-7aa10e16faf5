"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-checkbox@1._4d27bf1415e6fa99d2a5ebc4289da892";
exports.ids = ["vendor-chunks/@radix-ui+react-checkbox@1._4d27bf1415e6fa99d2a5ebc4289da892"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._4d27bf1415e6fa99d2a5ebc4289da892/node_modules/@radix-ui/react-checkbox/dist/index.mjs":
/*!**********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-checkbox@1._4d27bf1415e6fa99d2a5ebc4289da892/node_modules/@radix-ui/react-checkbox/dist/index.mjs ***!
  \**********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Checkbox: () => (/* binding */ Checkbox),\n/* harmony export */   CheckboxIndicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Indicator: () => (/* binding */ CheckboxIndicator),\n/* harmony export */   Root: () => (/* binding */ Checkbox),\n/* harmony export */   createCheckboxScope: () => (/* binding */ createCheckboxScope),\n/* harmony export */   unstable_BubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxBubbleInput: () => (/* binding */ CheckboxBubbleInput),\n/* harmony export */   unstable_CheckboxProvider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_CheckboxTrigger: () => (/* binding */ CheckboxTrigger),\n/* harmony export */   unstable_Provider: () => (/* binding */ CheckboxProvider),\n/* harmony export */   unstable_Trigger: () => (/* binding */ CheckboxTrigger)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_43bf2522b11b4054129913e6ef284501/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._6892bc03897e7520d34e6acf45100b9a/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Checkbox,CheckboxIndicator,Indicator,Root,createCheckboxScope,unstable_BubbleInput,unstable_CheckboxBubbleInput,unstable_CheckboxProvider,unstable_CheckboxTrigger,unstable_Provider,unstable_Trigger auto */ // src/checkbox.tsx\n\n\n\n\n\n\n\n\n\n\nvar CHECKBOX_NAME = \"Checkbox\";\nvar [createCheckboxContext, createCheckboxScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(CHECKBOX_NAME);\nvar [CheckboxProviderImpl, useCheckboxContext] = createCheckboxContext(CHECKBOX_NAME);\nfunction CheckboxProvider(props) {\n    const { __scopeCheckbox, checked: checkedProp, children, defaultChecked, disabled, form, name, onCheckedChange, required, value = \"on\", // @ts-expect-error\n    internal_do_not_use_render } = props;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_3__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: CHECKBOX_NAME\n    });\n    const [control, setControl] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [bubbleInput, setBubbleInput] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = control ? !!form || !!control.closest(\"form\") : // We set this to true by default so that events bubble to forms without JS (SSR)\n    true;\n    const context = {\n        checked,\n        disabled,\n        setChecked,\n        control,\n        setControl,\n        name,\n        form,\n        value,\n        hasConsumerStoppedPropagationRef,\n        required,\n        defaultChecked: isIndeterminate(defaultChecked) ? false : defaultChecked,\n        isFormControl,\n        bubbleInput,\n        setBubbleInput\n    };\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProviderImpl, {\n        scope: __scopeCheckbox,\n        ...context,\n        children: isFunction(internal_do_not_use_render) ? internal_do_not_use_render(context) : children\n    });\n}\nvar TRIGGER_NAME = \"CheckboxTrigger\";\nvar CheckboxTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, onKeyDown, onClick, ...checkboxProps }, forwardedRef)=>{\n    const { control, value, disabled, checked, required, setControl, setChecked, hasConsumerStoppedPropagationRef, isFormControl, bubbleInput } = useCheckboxContext(TRIGGER_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setControl);\n    const initialCheckedStateRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(checked);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const form = control?.form;\n        if (form) {\n            const reset = ()=>setChecked(initialCheckedStateRef.current);\n            form.addEventListener(\"reset\", reset);\n            return ()=>form.removeEventListener(\"reset\", reset);\n        }\n    }, [\n        control,\n        setChecked\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n        type: \"button\",\n        role: \"checkbox\",\n        \"aria-checked\": isIndeterminate(checked) ? \"mixed\" : checked,\n        \"aria-required\": required,\n        \"data-state\": getState(checked),\n        \"data-disabled\": disabled ? \"\" : void 0,\n        disabled,\n        value,\n        ...checkboxProps,\n        ref: composedRefs,\n        onKeyDown: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onKeyDown, (event)=>{\n            if (event.key === \"Enter\") event.preventDefault();\n        }),\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(onClick, (event)=>{\n            setChecked((prevChecked)=>isIndeterminate(prevChecked) ? true : !prevChecked);\n            if (bubbleInput && isFormControl) {\n                hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n            }\n        })\n    });\n});\nCheckboxTrigger.displayName = TRIGGER_NAME;\nvar Checkbox = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, name, checked, defaultChecked, required, disabled, value, onCheckedChange, form, ...checkboxProps } = props;\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxProvider, {\n        __scopeCheckbox,\n        checked,\n        defaultChecked,\n        disabled,\n        required,\n        onCheckedChange,\n        name,\n        form,\n        value,\n        internal_do_not_use_render: ({ isFormControl })=>/* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                children: [\n                    /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxTrigger, {\n                        ...checkboxProps,\n                        ref: forwardedRef,\n                        __scopeCheckbox\n                    }),\n                    isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(CheckboxBubbleInput, {\n                        __scopeCheckbox\n                    })\n                ]\n            })\n    });\n});\nCheckbox.displayName = CHECKBOX_NAME;\nvar INDICATOR_NAME = \"CheckboxIndicator\";\nvar CheckboxIndicator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeCheckbox, forceMount, ...indicatorProps } = props;\n    const context = useCheckboxContext(INDICATOR_NAME, __scopeCheckbox);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_7__.Presence, {\n        present: forceMount || isIndeterminate(context.checked) || context.checked === true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n            \"data-state\": getState(context.checked),\n            \"data-disabled\": context.disabled ? \"\" : void 0,\n            ...indicatorProps,\n            ref: forwardedRef,\n            style: {\n                pointerEvents: \"none\",\n                ...props.style\n            }\n        })\n    });\n});\nCheckboxIndicator.displayName = INDICATOR_NAME;\nvar BUBBLE_INPUT_NAME = \"CheckboxBubbleInput\";\nvar CheckboxBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeCheckbox, ...props }, forwardedRef)=>{\n    const { control, hasConsumerStoppedPropagationRef, checked, defaultChecked, required, disabled, name, value, form, bubbleInput, setBubbleInput } = useCheckboxContext(BUBBLE_INPUT_NAME, __scopeCheckbox);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_4__.useComposedRefs)(forwardedRef, setBubbleInput);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_8__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_9__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = bubbleInput;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        const bubbles = !hasConsumerStoppedPropagationRef.current;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            input.indeterminate = isIndeterminate(checked);\n            setChecked.call(input, isIndeterminate(checked) ? false : checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        bubbleInput,\n        prevChecked,\n        checked,\n        hasConsumerStoppedPropagationRef\n    ]);\n    const defaultCheckedRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(isIndeterminate(checked) ? false : checked);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.input, {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: defaultChecked ?? defaultCheckedRef.current,\n        required,\n        disabled,\n        name,\n        value,\n        form,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0,\n            // We transform because the input is absolutely positioned but we have\n            // rendered it **after** the button. This pulls it back to sit on top\n            // of the button.\n            transform: \"translateX(-100%)\"\n        }\n    });\n});\nCheckboxBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction isFunction(value) {\n    return typeof value === \"function\";\n}\nfunction isIndeterminate(checked) {\n    return checked === \"indeterminate\";\n}\nfunction getState(checked) {\n    return isIndeterminate(checked) ? \"indeterminate\" : checked ? \"checked\" : \"unchecked\";\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-checkbox@1._4d27bf1415e6fa99d2a5ebc4289da892/node_modules/@radix-ui/react-checkbox/dist/index.mjs\n");

/***/ })

};
;