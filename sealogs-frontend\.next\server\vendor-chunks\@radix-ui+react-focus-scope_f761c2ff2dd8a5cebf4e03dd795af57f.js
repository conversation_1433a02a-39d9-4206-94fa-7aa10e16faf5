"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f";
exports.ids = ["vendor-chunks/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs":
/*!*************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   FocusScope: () => (/* binding */ FocusScope),\n/* harmony export */   Root: () => (/* binding */ Root)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-use-callback-ref */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-callbac_d352c0cd6a6afa5cbe132ca4d71633df/node_modules/@radix-ui/react-use-callback-ref/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ FocusScope,Root auto */ // src/focus-scope.tsx\n\n\n\n\n\nvar AUTOFOCUS_ON_MOUNT = \"focusScope.autoFocusOnMount\";\nvar AUTOFOCUS_ON_UNMOUNT = \"focusScope.autoFocusOnUnmount\";\nvar EVENT_OPTIONS = {\n    bubbles: false,\n    cancelable: true\n};\nvar FOCUS_SCOPE_NAME = \"FocusScope\";\nvar FocusScope = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { loop = false, trapped = false, onMountAutoFocus: onMountAutoFocusProp, onUnmountAutoFocus: onUnmountAutoFocusProp, ...scopeProps } = props;\n    const [container, setContainer] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const onMountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onMountAutoFocusProp);\n    const onUnmountAutoFocus = (0,_radix_ui_react_use_callback_ref__WEBPACK_IMPORTED_MODULE_2__.useCallbackRef)(onUnmountAutoFocusProp);\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setContainer(node));\n    const focusScope = react__WEBPACK_IMPORTED_MODULE_0__.useRef({\n        paused: false,\n        pause () {\n            this.paused = true;\n        },\n        resume () {\n            this.paused = false;\n        }\n    }).current;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (trapped) {\n            let handleFocusIn2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const target = event.target;\n                if (container.contains(target)) {\n                    lastFocusedElementRef.current = target;\n                } else {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleFocusOut2 = function(event) {\n                if (focusScope.paused || !container) return;\n                const relatedTarget = event.relatedTarget;\n                if (relatedTarget === null) return;\n                if (!container.contains(relatedTarget)) {\n                    focus(lastFocusedElementRef.current, {\n                        select: true\n                    });\n                }\n            }, handleMutations2 = function(mutations) {\n                const focusedElement = document.activeElement;\n                if (focusedElement !== document.body) return;\n                for (const mutation of mutations){\n                    if (mutation.removedNodes.length > 0) focus(container);\n                }\n            };\n            var handleFocusIn = handleFocusIn2, handleFocusOut = handleFocusOut2, handleMutations = handleMutations2;\n            document.addEventListener(\"focusin\", handleFocusIn2);\n            document.addEventListener(\"focusout\", handleFocusOut2);\n            const mutationObserver = new MutationObserver(handleMutations2);\n            if (container) mutationObserver.observe(container, {\n                childList: true,\n                subtree: true\n            });\n            return ()=>{\n                document.removeEventListener(\"focusin\", handleFocusIn2);\n                document.removeEventListener(\"focusout\", handleFocusOut2);\n                mutationObserver.disconnect();\n            };\n        }\n    }, [\n        trapped,\n        container,\n        focusScope.paused\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        if (container) {\n            focusScopesStack.add(focusScope);\n            const previouslyFocusedElement = document.activeElement;\n            const hasFocusedCandidate = container.contains(previouslyFocusedElement);\n            if (!hasFocusedCandidate) {\n                const mountEvent = new CustomEvent(AUTOFOCUS_ON_MOUNT, EVENT_OPTIONS);\n                container.addEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                container.dispatchEvent(mountEvent);\n                if (!mountEvent.defaultPrevented) {\n                    focusFirst(removeLinks(getTabbableCandidates(container)), {\n                        select: true\n                    });\n                    if (document.activeElement === previouslyFocusedElement) {\n                        focus(container);\n                    }\n                }\n            }\n            return ()=>{\n                container.removeEventListener(AUTOFOCUS_ON_MOUNT, onMountAutoFocus);\n                setTimeout(()=>{\n                    const unmountEvent = new CustomEvent(AUTOFOCUS_ON_UNMOUNT, EVENT_OPTIONS);\n                    container.addEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    container.dispatchEvent(unmountEvent);\n                    if (!unmountEvent.defaultPrevented) {\n                        focus(previouslyFocusedElement ?? document.body, {\n                            select: true\n                        });\n                    }\n                    container.removeEventListener(AUTOFOCUS_ON_UNMOUNT, onUnmountAutoFocus);\n                    focusScopesStack.remove(focusScope);\n                }, 0);\n            };\n        }\n    }, [\n        container,\n        onMountAutoFocus,\n        onUnmountAutoFocus,\n        focusScope\n    ]);\n    const handleKeyDown = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((event)=>{\n        if (!loop && !trapped) return;\n        if (focusScope.paused) return;\n        const isTabKey = event.key === \"Tab\" && !event.altKey && !event.ctrlKey && !event.metaKey;\n        const focusedElement = document.activeElement;\n        if (isTabKey && focusedElement) {\n            const container2 = event.currentTarget;\n            const [first, last] = getTabbableEdges(container2);\n            const hasTabbableElementsInside = first && last;\n            if (!hasTabbableElementsInside) {\n                if (focusedElement === container2) event.preventDefault();\n            } else {\n                if (!event.shiftKey && focusedElement === last) {\n                    event.preventDefault();\n                    if (loop) focus(first, {\n                        select: true\n                    });\n                } else if (event.shiftKey && focusedElement === first) {\n                    event.preventDefault();\n                    if (loop) focus(last, {\n                        select: true\n                    });\n                }\n            }\n        }\n    }, [\n        loop,\n        trapped,\n        focusScope.paused\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_4__.Primitive.div, {\n        tabIndex: -1,\n        ...scopeProps,\n        ref: composedRefs,\n        onKeyDown: handleKeyDown\n    });\n});\nFocusScope.displayName = FOCUS_SCOPE_NAME;\nfunction focusFirst(candidates, { select = false } = {}) {\n    const previouslyFocusedElement = document.activeElement;\n    for (const candidate of candidates){\n        focus(candidate, {\n            select\n        });\n        if (document.activeElement !== previouslyFocusedElement) return;\n    }\n}\nfunction getTabbableEdges(container) {\n    const candidates = getTabbableCandidates(container);\n    const first = findVisible(candidates, container);\n    const last = findVisible(candidates.reverse(), container);\n    return [\n        first,\n        last\n    ];\n}\nfunction getTabbableCandidates(container) {\n    const nodes = [];\n    const walker = document.createTreeWalker(container, NodeFilter.SHOW_ELEMENT, {\n        acceptNode: (node)=>{\n            const isHiddenInput = node.tagName === \"INPUT\" && node.type === \"hidden\";\n            if (node.disabled || node.hidden || isHiddenInput) return NodeFilter.FILTER_SKIP;\n            return node.tabIndex >= 0 ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_SKIP;\n        }\n    });\n    while(walker.nextNode())nodes.push(walker.currentNode);\n    return nodes;\n}\nfunction findVisible(elements, container) {\n    for (const element of elements){\n        if (!isHidden(element, {\n            upTo: container\n        })) return element;\n    }\n}\nfunction isHidden(node, { upTo }) {\n    if (getComputedStyle(node).visibility === \"hidden\") return true;\n    while(node){\n        if (upTo !== void 0 && node === upTo) return false;\n        if (getComputedStyle(node).display === \"none\") return true;\n        node = node.parentElement;\n    }\n    return false;\n}\nfunction isSelectableInput(element) {\n    return element instanceof HTMLInputElement && \"select\" in element;\n}\nfunction focus(element, { select = false } = {}) {\n    if (element && element.focus) {\n        const previouslyFocusedElement = document.activeElement;\n        element.focus({\n            preventScroll: true\n        });\n        if (element !== previouslyFocusedElement && isSelectableInput(element) && select) element.select();\n    }\n}\nvar focusScopesStack = createFocusScopesStack();\nfunction createFocusScopesStack() {\n    let stack = [];\n    return {\n        add (focusScope) {\n            const activeFocusScope = stack[0];\n            if (focusScope !== activeFocusScope) {\n                activeFocusScope?.pause();\n            }\n            stack = arrayRemove(stack, focusScope);\n            stack.unshift(focusScope);\n        },\n        remove (focusScope) {\n            stack = arrayRemove(stack, focusScope);\n            stack[0]?.resume();\n        }\n    };\n}\nfunction arrayRemove(array, item) {\n    const updatedArray = [\n        ...array\n    ];\n    const index = updatedArray.indexOf(item);\n    if (index !== -1) {\n        updatedArray.splice(index, 1);\n    }\n    return updatedArray;\n}\nfunction removeLinks(items) {\n    return items.filter((item)=>item.tagName !== \"A\");\n}\nvar Root = FocusScope;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\n");

/***/ })

};
;