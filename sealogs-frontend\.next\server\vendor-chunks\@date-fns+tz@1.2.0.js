"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@date-fns+tz@1.2.0";
exports.ids = ["vendor-chunks/@date-fns+tz@1.2.0"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/constants/index.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/constants/index.js ***!
  \********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constructFromSymbol: () => (/* binding */ constructFromSymbol)\n/* harmony export */ });\n/**\n * The symbol to access the `TZDate`'s function to construct a new instance from\n * the provided value. It helps date-fns to inherit the time zone.\n */\nconst constructFromSymbol = Symbol.for(\"constructDateFrom\");//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGRhdGUtZm5zK3R6QDEuMi4wL25vZGVfbW9kdWxlcy9AZGF0ZS1mbnMvdHovY29uc3RhbnRzL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNPIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9AZGF0ZS1mbnMrdHpAMS4yLjAvbm9kZV9tb2R1bGVzL0BkYXRlLWZucy90ei9jb25zdGFudHMvaW5kZXguanM/ZWM5OCJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIFRoZSBzeW1ib2wgdG8gYWNjZXNzIHRoZSBgVFpEYXRlYCdzIGZ1bmN0aW9uIHRvIGNvbnN0cnVjdCBhIG5ldyBpbnN0YW5jZSBmcm9tXG4gKiB0aGUgcHJvdmlkZWQgdmFsdWUuIEl0IGhlbHBzIGRhdGUtZm5zIHRvIGluaGVyaXQgdGhlIHRpbWUgem9uZS5cbiAqL1xuZXhwb3J0IGNvbnN0IGNvbnN0cnVjdEZyb21TeW1ib2wgPSBTeW1ib2wuZm9yKFwiY29uc3RydWN0RGF0ZUZyb21cIik7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/constants/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/index.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/index.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* binding */ TZDate)\n/* harmony export */ });\n/* harmony import */ var _mini_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./mini.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js\");\n\n\n/**\n * UTC date class. It maps getters and setters to corresponding UTC methods,\n * forcing all calculations in the UTC time zone.\n *\n * Combined with date-fns, it allows using the class the same way as\n * the original date class.\n *\n * This complete version provides not only getters, setters,\n * and `getTimezoneOffset`, but also the formatter functions, mirroring\n * all original `Date` functionality. Use this version when you need to format\n * a string or in an environment you don't fully control (a library).\n * For a minimal version, see `UTCDateMini`.\n */\nclass TZDate extends _mini_js__WEBPACK_IMPORTED_MODULE_0__.TZDateMini {\n  //#region static\n\n  static tz(tz, ...args) {\n    return args.length ? new TZDate(...args, tz) : new TZDate(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region representation\n\n  toISOString() {\n    const [sign, hours, minutes] = this.tzComponents();\n    const tz = `${sign}${hours}:${minutes}`;\n    return this.internal.toISOString().slice(0, -1) + tz;\n  }\n  toString() {\n    // \"Tue Aug 13 2024 07:50:19 GMT+0800 (Singapore Standard Time)\";\n    return `${this.toDateString()} ${this.toTimeString()}`;\n  }\n  toDateString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const [day, date, month, year] = this.internal.toUTCString().split(\" \");\n    // \"Tue Aug 13 2024\"\n    return `${day?.slice(0, -1) /* Remove \",\" */} ${month} ${date} ${year}`;\n  }\n  toTimeString() {\n    // toUTCString returns RFC 7231 (\"Mon, 12 Aug 2024 23:36:08 GMT\")\n    const time = this.internal.toUTCString().split(\" \")[4];\n    const [sign, hours, minutes] = this.tzComponents();\n    // \"07:42:23 GMT+0800 (Singapore Standard Time)\"\n    return `${time} GMT${sign}${hours}${minutes} (${tzName(this.timeZone, this)})`;\n  }\n  toLocaleString(locales, options) {\n    return Date.prototype.toLocaleString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleDateString(locales, options) {\n    return Date.prototype.toLocaleDateString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n  toLocaleTimeString(locales, options) {\n    return Date.prototype.toLocaleTimeString.call(this, locales, {\n      ...options,\n      timeZone: options?.timeZone || this.timeZone\n    });\n  }\n\n  //#endregion\n\n  //#region private\n\n  tzComponents() {\n    const offset = this.getTimezoneOffset();\n    const sign = offset > 0 ? \"-\" : \"+\";\n    const hours = String(Math.floor(Math.abs(offset) / 60)).padStart(2, \"0\");\n    const minutes = String(Math.abs(offset) % 60).padStart(2, \"0\");\n    return [sign, hours, minutes];\n  }\n\n  //#endregion\n\n  withTimeZone(timeZone) {\n    return new TZDate(+this, timeZone);\n  }\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDate(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\nfunction tzName(tz, date) {\n  return new Intl.DateTimeFormat(\"en-GB\", {\n    timeZone: tz,\n    timeZoneName: \"long\"\n  }).format(date).slice(12);\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDateMini: () => (/* binding */ TZDateMini)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\");\n\nclass TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN((0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    return -(0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCMinutes(date.internal.getUTCMinutes() - date.getTimezoneOffset());\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDate: () => (/* reexport safe */ _date_index_js__WEBPACK_IMPORTED_MODULE_1__.TZDate),\n/* harmony export */   TZDateMini: () => (/* reexport safe */ _date_mini_js__WEBPACK_IMPORTED_MODULE_2__.TZDateMini),\n/* harmony export */   constructFromSymbol: () => (/* reexport safe */ _constants_index_js__WEBPACK_IMPORTED_MODULE_0__.constructFromSymbol),\n/* harmony export */   tz: () => (/* reexport safe */ _tz_index_js__WEBPACK_IMPORTED_MODULE_3__.tz),\n/* harmony export */   tzOffset: () => (/* reexport safe */ _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__.tzOffset),\n/* harmony export */   tzScan: () => (/* reexport safe */ _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__.tzScan)\n/* harmony export */ });\n/* harmony import */ var _constants_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/constants/index.js\");\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./date/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/index.js\");\n/* harmony import */ var _date_mini_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./date/mini.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js\");\n/* harmony import */ var _tz_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./tz/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tz/index.js\");\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./tzOffset/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\");\n/* harmony import */ var _tzScan_index_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./tzScan/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzScan/index.js\");\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGRhdGUtZm5zK3R6QDEuMi4wL25vZGVfbW9kdWxlcy9AZGF0ZS1mbnMvdHovaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQXFDO0FBQ0w7QUFDRDtBQUNEO0FBQ00iLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL0BkYXRlLWZucyt0ekAxLjIuMC9ub2RlX21vZHVsZXMvQGRhdGUtZm5zL3R6L2luZGV4LmpzPzE0OWMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSBcIi4vY29uc3RhbnRzL2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kYXRlL2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi9kYXRlL21pbmkuanNcIjtcbmV4cG9ydCAqIGZyb20gXCIuL3R6L2luZGV4LmpzXCI7XG5leHBvcnQgKiBmcm9tIFwiLi90ek9mZnNldC9pbmRleC5qc1wiO1xuZXhwb3J0ICogZnJvbSBcIi4vdHpTY2FuL2luZGV4LmpzXCI7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js":
/*!*******************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js ***!
  \*******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzOffset: () => (/* binding */ tzOffset)\n/* harmony export */ });\nconst offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nfunction tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-GB\", {\n      timeZone,\n      hour: \"numeric\",\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split('GMT')[1] || '';\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +values[0];\n  const minutes = +(values[1] || 0);\n  return offsetCache[cacheStr] = hours > 0 ? hours * 60 + minutes : hours * 60 - minutes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzScan/index.js":
/*!*****************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzScan/index.js ***!
  \*****************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzScan: () => (/* binding */ tzScan)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\");\n\n\n/**\n * Time interval.\n */\n\n/**\n * Time zone change record.\n */\n\n/**\n * The function scans the time zone for changes in the given interval.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param interval - Time interval to scan for changes\n *\n * @returns Array of time zone changes\n */\nfunction tzScan(timeZone, interval) {\n  const changes = [];\n  const monthDate = new Date(interval.start);\n  monthDate.setUTCSeconds(0, 0);\n  const endDate = new Date(interval.end);\n  endDate.setUTCSeconds(0, 0);\n  const endMonthTime = +endDate;\n  let lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n  while (+monthDate < endMonthTime) {\n    // Month forward\n    monthDate.setUTCMonth(monthDate.getUTCMonth() + 1);\n\n    // Find the month where the offset changes\n    const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, monthDate);\n    if (offset != lastOffset) {\n      // Rewind a month back to find the day where the offset changes\n      const dayDate = new Date(monthDate);\n      dayDate.setUTCMonth(dayDate.getUTCMonth() - 1);\n      const endDayTime = +monthDate;\n      lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n      while (+dayDate < endDayTime) {\n        // Day forward\n        dayDate.setUTCDate(dayDate.getUTCDate() + 1);\n\n        // Find the day where the offset changes\n        const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, dayDate);\n        if (offset != lastOffset) {\n          // Rewind a day back to find the time where the offset changes\n          const hourDate = new Date(dayDate);\n          hourDate.setUTCDate(hourDate.getUTCDate() - 1);\n          const endHourTime = +dayDate;\n          lastOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n          while (+hourDate < endHourTime) {\n            // Hour forward\n            hourDate.setUTCHours(hourDate.getUTCHours() + 1);\n\n            // Find the hour where the offset changes\n            const hourOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(timeZone, hourDate);\n            if (hourOffset !== lastOffset) {\n              changes.push({\n                date: new Date(hourDate),\n                change: hourOffset - lastOffset,\n                offset: hourOffset\n              });\n            }\n            lastOffset = hourOffset;\n          }\n        }\n        lastOffset = offset;\n      }\n    }\n    lastOffset = offset;\n  }\n  return changes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzScan/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tz/index.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tz/index.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tz: () => (/* binding */ tz)\n/* harmony export */ });\n/* harmony import */ var _date_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../date/index.js */ \"(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/index.js\");\n\n\n/**\n * The function creates accepts a time zone and returns a function that creates\n * a new `TZDate` instance in the time zone from the provided value. Use it to\n * provide the context for the date-fns functions, via the `in` option.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n *\n * @returns Function that creates a new `TZDate` instance in the time zone\n */\nconst tz = timeZone => value => _date_index_js__WEBPACK_IMPORTED_MODULE_0__.TZDate.tz(timeZone, +new Date(value));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQGRhdGUtZm5zK3R6QDEuMi4wL25vZGVfbW9kdWxlcy9AZGF0ZS1mbnMvdHovdHovaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBMEM7O0FBRTFDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNPLGdDQUFnQyxrREFBTSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vQGRhdGUtZm5zK3R6QDEuMi4wL25vZGVfbW9kdWxlcy9AZGF0ZS1mbnMvdHovdHovaW5kZXguanM/NDIwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBUWkRhdGUgfSBmcm9tIFwiLi4vZGF0ZS9pbmRleC5qc1wiO1xuXG4vKipcbiAqIFRoZSBmdW5jdGlvbiBjcmVhdGVzIGFjY2VwdHMgYSB0aW1lIHpvbmUgYW5kIHJldHVybnMgYSBmdW5jdGlvbiB0aGF0IGNyZWF0ZXNcbiAqIGEgbmV3IGBUWkRhdGVgIGluc3RhbmNlIGluIHRoZSB0aW1lIHpvbmUgZnJvbSB0aGUgcHJvdmlkZWQgdmFsdWUuIFVzZSBpdCB0b1xuICogcHJvdmlkZSB0aGUgY29udGV4dCBmb3IgdGhlIGRhdGUtZm5zIGZ1bmN0aW9ucywgdmlhIHRoZSBgaW5gIG9wdGlvbi5cbiAqXG4gKiBAcGFyYW0gdGltZVpvbmUgLSBUaW1lIHpvbmUgbmFtZSAoSUFOQSBvciBVVEMgb2Zmc2V0KVxuICpcbiAqIEByZXR1cm5zIEZ1bmN0aW9uIHRoYXQgY3JlYXRlcyBhIG5ldyBgVFpEYXRlYCBpbnN0YW5jZSBpbiB0aGUgdGltZSB6b25lXG4gKi9cbmV4cG9ydCBjb25zdCB0eiA9IHRpbWVab25lID0+IHZhbHVlID0+IFRaRGF0ZS50eih0aW1lWm9uZSwgK25ldyBEYXRlKHZhbHVlKSk7Il0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tz/index.js\n");

/***/ })

};
;