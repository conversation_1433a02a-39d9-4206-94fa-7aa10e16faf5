"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/optimism@0.18.1";
exports.ids = ["vendor-chunks/optimism@0.18.1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.bind),\n/* harmony export */   noContext: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.noContext),\n/* harmony export */   nonReactive: () => (/* binding */ nonReactive),\n/* harmony export */   parentEntrySlot: () => (/* binding */ parentEntrySlot),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _wry_context__WEBPACK_IMPORTED_MODULE_0__.setTimeout)\n/* harmony export */ });\n/* harmony import */ var _wry_context__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/context */ \"(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js\");\n\nconst parentEntrySlot = new _wry_context__WEBPACK_IMPORTED_MODULE_0__.Slot();\nfunction nonReactive(fn) {\n    return parentEntrySlot.withValue(void 0, fn);\n}\n\n\n//# sourceMappingURL=context.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb3B0aW1pc21AMC4xOC4xL25vZGVfbW9kdWxlcy9vcHRpbWlzbS9saWIvY29udGV4dC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFvQztBQUM3Qiw0QkFBNEIsOENBQUk7QUFDaEM7QUFDUDtBQUNBO0FBQ2dCO0FBQ3lFO0FBQ3pGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VhbG9ncy8uL25vZGVfbW9kdWxlcy8ucG5wbS9vcHRpbWlzbUAwLjE4LjEvbm9kZV9tb2R1bGVzL29wdGltaXNtL2xpYi9jb250ZXh0LmpzPzAzYzgiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2xvdCB9IGZyb20gXCJAd3J5L2NvbnRleHRcIjtcbmV4cG9ydCBjb25zdCBwYXJlbnRFbnRyeVNsb3QgPSBuZXcgU2xvdCgpO1xuZXhwb3J0IGZ1bmN0aW9uIG5vblJlYWN0aXZlKGZuKSB7XG4gICAgcmV0dXJuIHBhcmVudEVudHJ5U2xvdC53aXRoVmFsdWUodm9pZCAwLCBmbik7XG59XG5leHBvcnQgeyBTbG90IH07XG5leHBvcnQgeyBiaW5kIGFzIGJpbmRDb250ZXh0LCBub0NvbnRleHQsIHNldFRpbWVvdXQsIGFzeW5jRnJvbUdlbiwgfSBmcm9tIFwiQHdyeS9jb250ZXh0XCI7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1jb250ZXh0LmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js":
/*!*****************************************************************************!*\
  !*** ./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js ***!
  \*****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dep: () => (/* binding */ dep)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst EntryMethods = {\n    setDirty: true,\n    dispose: true,\n    forget: true, // Fully remove parent Entry from LRU cache and computation graph\n};\nfunction dep(options) {\n    const depsByKey = new Map();\n    const subscribe = options && options.subscribe;\n    function depend(key) {\n        const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n        if (parent) {\n            let dep = depsByKey.get(key);\n            if (!dep) {\n                depsByKey.set(key, dep = new Set);\n            }\n            parent.dependOn(dep);\n            if (typeof subscribe === \"function\") {\n                (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n                dep.unsubscribe = subscribe(key);\n            }\n        }\n    }\n    depend.dirty = function dirty(key, entryMethodName) {\n        const dep = depsByKey.get(key);\n        if (dep) {\n            const m = (entryMethodName &&\n                _helpers_js__WEBPACK_IMPORTED_MODULE_1__.hasOwnProperty.call(EntryMethods, entryMethodName)) ? entryMethodName : \"setDirty\";\n            // We have to use arrayFromSet(dep).forEach instead of dep.forEach,\n            // because modifying a Set while iterating over it can cause elements in\n            // the Set to be removed from the Set before they've been iterated over.\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(dep).forEach(entry => entry[m]());\n            depsByKey.delete(key);\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(dep);\n        }\n    };\n    return depend;\n}\n//# sourceMappingURL=dep.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb3B0aW1pc21AMC4xOC4xL25vZGVfbW9kdWxlcy9vcHRpbWlzbS9saWIvZGVwLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUErQztBQUNnQztBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQSx1QkFBdUIsd0RBQWU7QUFDdEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsNkRBQWdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxnQkFBZ0IsdURBQWM7QUFDOUI7QUFDQTtBQUNBO0FBQ0EsWUFBWSx5REFBWTtBQUN4QjtBQUNBLFlBQVksNkRBQWdCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2RlcC5qcz8wYzFlIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IHBhcmVudEVudHJ5U2xvdCB9IGZyb20gXCIuL2NvbnRleHQuanNcIjtcbmltcG9ydCB7IGhhc093blByb3BlcnR5LCBtYXliZVVuc3Vic2NyaWJlLCBhcnJheUZyb21TZXQsIH0gZnJvbSBcIi4vaGVscGVycy5qc1wiO1xuY29uc3QgRW50cnlNZXRob2RzID0ge1xuICAgIHNldERpcnR5OiB0cnVlLFxuICAgIGRpc3Bvc2U6IHRydWUsXG4gICAgZm9yZ2V0OiB0cnVlLCAvLyBGdWxseSByZW1vdmUgcGFyZW50IEVudHJ5IGZyb20gTFJVIGNhY2hlIGFuZCBjb21wdXRhdGlvbiBncmFwaFxufTtcbmV4cG9ydCBmdW5jdGlvbiBkZXAob3B0aW9ucykge1xuICAgIGNvbnN0IGRlcHNCeUtleSA9IG5ldyBNYXAoKTtcbiAgICBjb25zdCBzdWJzY3JpYmUgPSBvcHRpb25zICYmIG9wdGlvbnMuc3Vic2NyaWJlO1xuICAgIGZ1bmN0aW9uIGRlcGVuZChrZXkpIHtcbiAgICAgICAgY29uc3QgcGFyZW50ID0gcGFyZW50RW50cnlTbG90LmdldFZhbHVlKCk7XG4gICAgICAgIGlmIChwYXJlbnQpIHtcbiAgICAgICAgICAgIGxldCBkZXAgPSBkZXBzQnlLZXkuZ2V0KGtleSk7XG4gICAgICAgICAgICBpZiAoIWRlcCkge1xuICAgICAgICAgICAgICAgIGRlcHNCeUtleS5zZXQoa2V5LCBkZXAgPSBuZXcgU2V0KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICAgIHBhcmVudC5kZXBlbmRPbihkZXApO1xuICAgICAgICAgICAgaWYgKHR5cGVvZiBzdWJzY3JpYmUgPT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgIG1heWJlVW5zdWJzY3JpYmUoZGVwKTtcbiAgICAgICAgICAgICAgICBkZXAudW5zdWJzY3JpYmUgPSBzdWJzY3JpYmUoa2V5KTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH1cbiAgICBkZXBlbmQuZGlydHkgPSBmdW5jdGlvbiBkaXJ0eShrZXksIGVudHJ5TWV0aG9kTmFtZSkge1xuICAgICAgICBjb25zdCBkZXAgPSBkZXBzQnlLZXkuZ2V0KGtleSk7XG4gICAgICAgIGlmIChkZXApIHtcbiAgICAgICAgICAgIGNvbnN0IG0gPSAoZW50cnlNZXRob2ROYW1lICYmXG4gICAgICAgICAgICAgICAgaGFzT3duUHJvcGVydHkuY2FsbChFbnRyeU1ldGhvZHMsIGVudHJ5TWV0aG9kTmFtZSkpID8gZW50cnlNZXRob2ROYW1lIDogXCJzZXREaXJ0eVwiO1xuICAgICAgICAgICAgLy8gV2UgaGF2ZSB0byB1c2UgYXJyYXlGcm9tU2V0KGRlcCkuZm9yRWFjaCBpbnN0ZWFkIG9mIGRlcC5mb3JFYWNoLFxuICAgICAgICAgICAgLy8gYmVjYXVzZSBtb2RpZnlpbmcgYSBTZXQgd2hpbGUgaXRlcmF0aW5nIG92ZXIgaXQgY2FuIGNhdXNlIGVsZW1lbnRzIGluXG4gICAgICAgICAgICAvLyB0aGUgU2V0IHRvIGJlIHJlbW92ZWQgZnJvbSB0aGUgU2V0IGJlZm9yZSB0aGV5J3ZlIGJlZW4gaXRlcmF0ZWQgb3Zlci5cbiAgICAgICAgICAgIGFycmF5RnJvbVNldChkZXApLmZvckVhY2goZW50cnkgPT4gZW50cnlbbV0oKSk7XG4gICAgICAgICAgICBkZXBzQnlLZXkuZGVsZXRlKGtleSk7XG4gICAgICAgICAgICBtYXliZVVuc3Vic2NyaWJlKGRlcCk7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIHJldHVybiBkZXBlbmQ7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1kZXAuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Entry: () => (/* binding */ Entry)\n/* harmony export */ });\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _helpers_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./helpers.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\");\n\n\nconst emptySetPool = [];\nconst POOL_TARGET_SIZE = 100;\n// Since this package might be used browsers, we should avoid using the\n// Node built-in assert module.\nfunction assert(condition, optionalMessage) {\n    if (!condition) {\n        throw new Error(optionalMessage || \"assertion failure\");\n    }\n}\nfunction valueIs(a, b) {\n    const len = a.length;\n    return (\n    // Unknown values are not equal to each other.\n    len > 0 &&\n        // Both values must be ordinary (or both exceptional) to be equal.\n        len === b.length &&\n        // The underlying value or exception must be the same.\n        a[len - 1] === b[len - 1]);\n}\nfunction valueGet(value) {\n    switch (value.length) {\n        case 0: throw new Error(\"unknown value\");\n        case 1: return value[0];\n        case 2: throw value[1];\n    }\n}\nfunction valueCopy(value) {\n    return value.slice(0);\n}\nclass Entry {\n    constructor(fn) {\n        this.fn = fn;\n        this.parents = new Set();\n        this.childValues = new Map();\n        // When this Entry has children that are dirty, this property becomes\n        // a Set containing other Entry objects, borrowed from emptySetPool.\n        // When the set becomes empty, it gets recycled back to emptySetPool.\n        this.dirtyChildren = null;\n        this.dirty = true;\n        this.recomputing = false;\n        this.value = [];\n        this.deps = null;\n        ++Entry.count;\n    }\n    peek() {\n        if (this.value.length === 1 && !mightBeDirty(this)) {\n            rememberParent(this);\n            return this.value[0];\n        }\n    }\n    // This is the most important method of the Entry API, because it\n    // determines whether the cached this.value can be returned immediately,\n    // or must be recomputed. The overall performance of the caching system\n    // depends on the truth of the following observations: (1) this.dirty is\n    // usually false, (2) this.dirtyChildren is usually null/empty, and thus\n    // (3) valueGet(this.value) is usually returned without recomputation.\n    recompute(args) {\n        assert(!this.recomputing, \"already recomputing\");\n        rememberParent(this);\n        return mightBeDirty(this)\n            ? reallyRecompute(this, args)\n            : valueGet(this.value);\n    }\n    setDirty() {\n        if (this.dirty)\n            return;\n        this.dirty = true;\n        reportDirty(this);\n        // We can go ahead and unsubscribe here, since any further dirty\n        // notifications we receive will be redundant, and unsubscribing may\n        // free up some resources, e.g. file watchers.\n        (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(this);\n    }\n    dispose() {\n        this.setDirty();\n        // Sever any dependency relationships with our own children, so those\n        // children don't retain this parent Entry in their child.parents sets,\n        // thereby preventing it from being fully garbage collected.\n        forgetChildren(this);\n        // Because this entry has been kicked out of the cache (in index.js),\n        // we've lost the ability to find out if/when this entry becomes dirty,\n        // whether that happens through a subscription, because of a direct call\n        // to entry.setDirty(), or because one of its children becomes dirty.\n        // Because of this loss of future information, we have to assume the\n        // worst (that this entry might have become dirty very soon), so we must\n        // immediately mark this entry's parents as dirty. Normally we could\n        // just call entry.setDirty() rather than calling parent.setDirty() for\n        // each parent, but that would leave this entry in parent.childValues\n        // and parent.dirtyChildren, which would prevent the child from being\n        // truly forgotten.\n        eachParent(this, (parent, child) => {\n            parent.setDirty();\n            forgetChild(parent, this);\n        });\n    }\n    forget() {\n        // The code that creates Entry objects in index.ts will replace this method\n        // with one that actually removes the Entry from the cache, which will also\n        // trigger the entry.dispose method.\n        this.dispose();\n    }\n    dependOn(dep) {\n        dep.add(this);\n        if (!this.deps) {\n            this.deps = emptySetPool.pop() || new Set();\n        }\n        this.deps.add(dep);\n    }\n    forgetDeps() {\n        if (this.deps) {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(this.deps).forEach(dep => dep.delete(this));\n            this.deps.clear();\n            emptySetPool.push(this.deps);\n            this.deps = null;\n        }\n    }\n}\nEntry.count = 0;\nfunction rememberParent(child) {\n    const parent = _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.getValue();\n    if (parent) {\n        child.parents.add(parent);\n        if (!parent.childValues.has(child)) {\n            parent.childValues.set(child, []);\n        }\n        if (mightBeDirty(child)) {\n            reportDirtyChild(parent, child);\n        }\n        else {\n            reportCleanChild(parent, child);\n        }\n        return parent;\n    }\n}\nfunction reallyRecompute(entry, args) {\n    forgetChildren(entry);\n    // Set entry as the parent entry while calling recomputeNewValue(entry).\n    _context_js__WEBPACK_IMPORTED_MODULE_0__.parentEntrySlot.withValue(entry, recomputeNewValue, [entry, args]);\n    if (maybeSubscribe(entry, args)) {\n        // If we successfully recomputed entry.value and did not fail to\n        // (re)subscribe, then this Entry is no longer explicitly dirty.\n        setClean(entry);\n    }\n    return valueGet(entry.value);\n}\nfunction recomputeNewValue(entry, args) {\n    entry.recomputing = true;\n    const { normalizeResult } = entry;\n    let oldValueCopy;\n    if (normalizeResult && entry.value.length === 1) {\n        oldValueCopy = valueCopy(entry.value);\n    }\n    // Make entry.value an empty array, representing an unknown value.\n    entry.value.length = 0;\n    try {\n        // If entry.fn succeeds, entry.value will become a normal Value.\n        entry.value[0] = entry.fn.apply(null, args);\n        // If we have a viable oldValueCopy to compare with the (successfully\n        // recomputed) new entry.value, and they are not already === identical, give\n        // normalizeResult a chance to pick/choose/reuse parts of oldValueCopy[0]\n        // and/or entry.value[0] to determine the final cached entry.value.\n        if (normalizeResult && oldValueCopy && !valueIs(oldValueCopy, entry.value)) {\n            try {\n                entry.value[0] = normalizeResult(entry.value[0], oldValueCopy[0]);\n            }\n            catch (_a) {\n                // If normalizeResult throws, just use the newer value, rather than\n                // saving the exception as entry.value[1].\n            }\n        }\n    }\n    catch (e) {\n        // If entry.fn throws, entry.value will hold that exception.\n        entry.value[1] = e;\n    }\n    // Either way, this line is always reached.\n    entry.recomputing = false;\n}\nfunction mightBeDirty(entry) {\n    return entry.dirty || !!(entry.dirtyChildren && entry.dirtyChildren.size);\n}\nfunction setClean(entry) {\n    entry.dirty = false;\n    if (mightBeDirty(entry)) {\n        // This Entry may still have dirty children, in which case we can't\n        // let our parents know we're clean just yet.\n        return;\n    }\n    reportClean(entry);\n}\nfunction reportDirty(child) {\n    eachParent(child, reportDirtyChild);\n}\nfunction reportClean(child) {\n    eachParent(child, reportCleanChild);\n}\nfunction eachParent(child, callback) {\n    const parentCount = child.parents.size;\n    if (parentCount) {\n        const parents = (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.arrayFromSet)(child.parents);\n        for (let i = 0; i < parentCount; ++i) {\n            callback(parents[i], child);\n        }\n    }\n}\n// Let a parent Entry know that one of its children may be dirty.\nfunction reportDirtyChild(parent, child) {\n    // Must have called rememberParent(child) before calling\n    // reportDirtyChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(mightBeDirty(child));\n    const parentWasClean = !mightBeDirty(parent);\n    if (!parent.dirtyChildren) {\n        parent.dirtyChildren = emptySetPool.pop() || new Set;\n    }\n    else if (parent.dirtyChildren.has(child)) {\n        // If we already know this child is dirty, then we must have already\n        // informed our own parents that we are dirty, so we can terminate\n        // the recursion early.\n        return;\n    }\n    parent.dirtyChildren.add(child);\n    // If parent was clean before, it just became (possibly) dirty (according to\n    // mightBeDirty), since we just added child to parent.dirtyChildren.\n    if (parentWasClean) {\n        reportDirty(parent);\n    }\n}\n// Let a parent Entry know that one of its children is no longer dirty.\nfunction reportCleanChild(parent, child) {\n    // Must have called rememberChild(child) before calling\n    // reportCleanChild(parent, child).\n    assert(parent.childValues.has(child));\n    assert(!mightBeDirty(child));\n    const childValue = parent.childValues.get(child);\n    if (childValue.length === 0) {\n        parent.childValues.set(child, valueCopy(child.value));\n    }\n    else if (!valueIs(childValue, child.value)) {\n        parent.setDirty();\n    }\n    removeDirtyChild(parent, child);\n    if (mightBeDirty(parent)) {\n        return;\n    }\n    reportClean(parent);\n}\nfunction removeDirtyChild(parent, child) {\n    const dc = parent.dirtyChildren;\n    if (dc) {\n        dc.delete(child);\n        if (dc.size === 0) {\n            if (emptySetPool.length < POOL_TARGET_SIZE) {\n                emptySetPool.push(dc);\n            }\n            parent.dirtyChildren = null;\n        }\n    }\n}\n// Removes all children from this entry and returns an array of the\n// removed children.\nfunction forgetChildren(parent) {\n    if (parent.childValues.size > 0) {\n        parent.childValues.forEach((_value, child) => {\n            forgetChild(parent, child);\n        });\n    }\n    // Remove this parent Entry from any sets to which it was added by the\n    // addToSet method.\n    parent.forgetDeps();\n    // After we forget all our children, this.dirtyChildren must be empty\n    // and therefore must have been reset to null.\n    assert(parent.dirtyChildren === null);\n}\nfunction forgetChild(parent, child) {\n    child.parents.delete(parent);\n    parent.childValues.delete(child);\n    removeDirtyChild(parent, child);\n}\nfunction maybeSubscribe(entry, args) {\n    if (typeof entry.subscribe === \"function\") {\n        try {\n            (0,_helpers_js__WEBPACK_IMPORTED_MODULE_1__.maybeUnsubscribe)(entry); // Prevent double subscriptions.\n            entry.unsubscribe = entry.subscribe.apply(null, args);\n        }\n        catch (e) {\n            // If this Entry has a subscribe function and it threw an exception\n            // (or an unsubscribe function it previously returned now throws),\n            // return false to indicate that we were not able to subscribe (or\n            // unsubscribe), and this Entry should remain dirty.\n            entry.setDirty();\n            return false;\n        }\n    }\n    // Returning true indicates either that there was no entry.subscribe\n    // function or that it succeeded.\n    return true;\n}\n//# sourceMappingURL=entry.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js":
/*!*********************************************************************************!*\
  !*** ./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js ***!
  \*********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   arrayFromSet: () => (/* binding */ arrayFromSet),\n/* harmony export */   hasOwnProperty: () => (/* binding */ hasOwnProperty),\n/* harmony export */   maybeUnsubscribe: () => (/* binding */ maybeUnsubscribe)\n/* harmony export */ });\nconst { hasOwnProperty, } = Object.prototype;\nconst arrayFromSet = Array.from ||\n    function (set) {\n        const array = [];\n        set.forEach(item => array.push(item));\n        return array;\n    };\nfunction maybeUnsubscribe(entryOrDep) {\n    const { unsubscribe } = entryOrDep;\n    if (typeof unsubscribe === \"function\") {\n        entryOrDep.unsubscribe = void 0;\n        unsubscribe();\n    }\n}\n//# sourceMappingURL=helpers.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vb3B0aW1pc21AMC4xOC4xL25vZGVfbW9kdWxlcy9vcHRpbWlzbS9saWIvaGVscGVycy5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBTyxRQUFRLGtCQUFrQjtBQUMxQjtBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLFlBQVksY0FBYztBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZWFsb2dzLy4vbm9kZV9tb2R1bGVzLy5wbnBtL29wdGltaXNtQDAuMTguMS9ub2RlX21vZHVsZXMvb3B0aW1pc20vbGliL2hlbHBlcnMuanM/NmFlZSJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgY29uc3QgeyBoYXNPd25Qcm9wZXJ0eSwgfSA9IE9iamVjdC5wcm90b3R5cGU7XG5leHBvcnQgY29uc3QgYXJyYXlGcm9tU2V0ID0gQXJyYXkuZnJvbSB8fFxuICAgIGZ1bmN0aW9uIChzZXQpIHtcbiAgICAgICAgY29uc3QgYXJyYXkgPSBbXTtcbiAgICAgICAgc2V0LmZvckVhY2goaXRlbSA9PiBhcnJheS5wdXNoKGl0ZW0pKTtcbiAgICAgICAgcmV0dXJuIGFycmF5O1xuICAgIH07XG5leHBvcnQgZnVuY3Rpb24gbWF5YmVVbnN1YnNjcmliZShlbnRyeU9yRGVwKSB7XG4gICAgY29uc3QgeyB1bnN1YnNjcmliZSB9ID0gZW50cnlPckRlcDtcbiAgICBpZiAodHlwZW9mIHVuc3Vic2NyaWJlID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICAgICAgZW50cnlPckRlcC51bnN1YnNjcmliZSA9IHZvaWQgMDtcbiAgICAgICAgdW5zdWJzY3JpYmUoKTtcbiAgICB9XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1oZWxwZXJzLmpzLm1hcCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/helpers.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   KeyTrie: () => (/* reexport safe */ _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie),\n/* harmony export */   Slot: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.Slot),\n/* harmony export */   asyncFromGen: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.asyncFromGen),\n/* harmony export */   bindContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.bindContext),\n/* harmony export */   defaultMakeCacheKey: () => (/* binding */ defaultMakeCacheKey),\n/* harmony export */   dep: () => (/* reexport safe */ _dep_js__WEBPACK_IMPORTED_MODULE_3__.dep),\n/* harmony export */   noContext: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.noContext),\n/* harmony export */   nonReactive: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.nonReactive),\n/* harmony export */   setTimeout: () => (/* reexport safe */ _context_js__WEBPACK_IMPORTED_MODULE_2__.setTimeout),\n/* harmony export */   wrap: () => (/* binding */ wrap)\n/* harmony export */ });\n/* harmony import */ var _wry_trie__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wry/trie */ \"(ssr)/./node_modules/.pnpm/@wry+trie@0.5.0/node_modules/@wry/trie/lib/index.js\");\n/* harmony import */ var _wry_caches__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @wry/caches */ \"(ssr)/./node_modules/.pnpm/@wry+caches@1.0.1/node_modules/@wry/caches/lib/strong.js\");\n/* harmony import */ var _entry_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./entry.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/entry.js\");\n/* harmony import */ var _context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/context.js\");\n/* harmony import */ var _dep_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./dep.js */ \"(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/dep.js\");\n\n\n\n\n// These helper functions are important for making optimism work with\n// asynchronous code. In order to register parent-child dependencies,\n// optimism needs to know about any currently active parent computations.\n// In ordinary synchronous code, the parent context is implicit in the\n// execution stack, but asynchronous code requires some extra guidance in\n// order to propagate context from one async task segment to the next.\n\n// A lighter-weight dependency, similar to OptimisticWrapperFunction, except\n// with only one argument, no makeCacheKey, no wrapped function to recompute,\n// and no result value. Useful for representing dependency leaves in the graph\n// of computation. Subscriptions are supported.\n\n// The defaultMakeCacheKey function is remarkably powerful, because it gives\n// a unique object for any shallow-identical list of arguments. If you need\n// to implement a custom makeCacheKey function, you may find it helpful to\n// delegate the final work to defaultMakeCacheKey, which is why we export it\n// here. However, you may want to avoid defaultMakeCacheKey if your runtime\n// does not support WeakMap, or you have the ability to return a string key.\n// In those cases, just write your own custom makeCacheKey functions.\nlet defaultKeyTrie;\nfunction defaultMakeCacheKey(...args) {\n    const trie = defaultKeyTrie || (defaultKeyTrie = new _wry_trie__WEBPACK_IMPORTED_MODULE_0__.Trie(typeof WeakMap === \"function\"));\n    return trie.lookupArray(args);\n}\n// If you're paranoid about memory leaks, or you want to avoid using WeakMap\n// under the hood, but you still need the behavior of defaultMakeCacheKey,\n// import this constructor to create your own tries.\n\n;\nconst caches = new Set();\nfunction wrap(originalFunction, { max = Math.pow(2, 16), keyArgs, makeCacheKey = defaultMakeCacheKey, normalizeResult, subscribe, cache: cacheOption = _wry_caches__WEBPACK_IMPORTED_MODULE_4__.StrongCache, } = Object.create(null)) {\n    const cache = typeof cacheOption === \"function\"\n        ? new cacheOption(max, entry => entry.dispose())\n        : cacheOption;\n    const optimistic = function () {\n        const key = makeCacheKey.apply(null, keyArgs ? keyArgs.apply(null, arguments) : arguments);\n        if (key === void 0) {\n            return originalFunction.apply(null, arguments);\n        }\n        let entry = cache.get(key);\n        if (!entry) {\n            cache.set(key, entry = new _entry_js__WEBPACK_IMPORTED_MODULE_1__.Entry(originalFunction));\n            entry.normalizeResult = normalizeResult;\n            entry.subscribe = subscribe;\n            // Give the Entry the ability to trigger cache.delete(key), even though\n            // the Entry itself does not know about key or cache.\n            entry.forget = () => cache.delete(key);\n        }\n        const value = entry.recompute(Array.prototype.slice.call(arguments));\n        // Move this entry to the front of the least-recently used queue,\n        // since we just finished computing its value.\n        cache.set(key, entry);\n        caches.add(cache);\n        // Clean up any excess entries in the cache, but only if there is no\n        // active parent entry, meaning we're not in the middle of a larger\n        // computation that might be flummoxed by the cleaning.\n        if (!_context_js__WEBPACK_IMPORTED_MODULE_2__.parentEntrySlot.hasValue()) {\n            caches.forEach(cache => cache.clean());\n            caches.clear();\n        }\n        return value;\n    };\n    Object.defineProperty(optimistic, \"size\", {\n        get: () => cache.size,\n        configurable: false,\n        enumerable: false,\n    });\n    Object.freeze(optimistic.options = {\n        max,\n        keyArgs,\n        makeCacheKey,\n        normalizeResult,\n        subscribe,\n        cache,\n    });\n    function dirtyKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            entry.setDirty();\n        }\n    }\n    optimistic.dirtyKey = dirtyKey;\n    optimistic.dirty = function dirty() {\n        dirtyKey(makeCacheKey.apply(null, arguments));\n    };\n    function peekKey(key) {\n        const entry = key && cache.get(key);\n        if (entry) {\n            return entry.peek();\n        }\n    }\n    optimistic.peekKey = peekKey;\n    optimistic.peek = function peek() {\n        return peekKey(makeCacheKey.apply(null, arguments));\n    };\n    function forgetKey(key) {\n        return key ? cache.delete(key) : false;\n    }\n    optimistic.forgetKey = forgetKey;\n    optimistic.forget = function forget() {\n        return forgetKey(makeCacheKey.apply(null, arguments));\n    };\n    optimistic.makeCacheKey = makeCacheKey;\n    optimistic.getKey = keyArgs ? function getKey() {\n        return makeCacheKey.apply(null, keyArgs.apply(null, arguments));\n    } : makeCacheKey;\n    return Object.freeze(optimistic);\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/optimism@0.18.1/node_modules/optimism/lib/index.js\n");

/***/ })

};
;