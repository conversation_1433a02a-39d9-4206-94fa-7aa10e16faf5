"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@userback+react@0.3.9_react_52a31c29d49fa6c88d0dd64f42a5c9d3";
exports.ids = ["vendor-chunks/@userback+react@0.3.9_react_52a31c29d49fa6c88d0dd64f42a5c9d3"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@userback+react@0.3.9_react_52a31c29d49fa6c88d0dd64f42a5c9d3/node_modules/@userback/react/dist/react.mjs":
/*!*************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@userback+react@0.3.9_react_52a31c29d49fa6c88d0dd64f42a5c9d3/node_modules/@userback/react/dist/react.mjs ***!
  \*************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserbackProvider: () => (/* binding */ UserbackProvider),\n/* harmony export */   useUserback: () => (/* binding */ useUserback),\n/* harmony export */   useUserbackContext: () => (/* binding */ useUserbackContext),\n/* harmony export */   withUserback: () => (/* binding */ withUserback)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _userback_widget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @userback/widget */ \"(ssr)/./node_modules/.pnpm/@userback+widget@0.3.11/node_modules/@userback/widget/dist/widget.mjs\");\n\n\n\n/******************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n\r\nfunction __awaiter(thisArg, _arguments, P, generator) {\r\n    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\r\n    return new (P || (P = Promise))(function (resolve, reject) {\r\n        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\r\n        function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\r\n        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\r\n        step((generator = generator.apply(thisArg, _arguments || [])).next());\r\n    });\r\n}\r\n\r\ntypeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\r\n    var e = new Error(message);\r\n    return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\r\n};\n\nconst UserbackContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(undefined);\n/**\n * UserbackProider\n *\n * @example `<UserbackProvider token={UB_TOKEN} ><MyRouter /></UserbackProvider>`\n * @returns React.Component\n */\nconst UserbackProvider = ({ token, options = {}, widgetSettings: widget_settings, delayInit = false, children, }) => {\n    const ubLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const [Userback, setUserback] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)();\n    const init = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((_token = token, _options = Object.assign({ widget_settings }, options)) => __awaiter(void 0, void 0, void 0, function* () {\n        if (Userback)\n            return Userback;\n        ubLoaded.current = true;\n        const ub = yield (0,_userback_widget__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(_token, _options);\n        setUserback(ub);\n        return ub;\n    }), [Userback, token, widget_settings, options]);\n    // onMount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n        if (!ubLoaded.current && !delayInit) {\n            init(token, Object.assign({ widget_settings }, options));\n        }\n    }, [delayInit]); // eslint-disable-line react-hooks/exhaustive-deps\n    // Api hooks\n    const hide = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.hide(); }, [Userback]);\n    const show = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.show(); }, [Userback]);\n    const close = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.close(); }, [Userback]);\n    const openPortal = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => { Userback === null || Userback === void 0 ? void 0 : Userback.openPortal(); }, [Userback]);\n    const isLoaded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => (Userback === null || Userback === void 0 ? void 0 : Userback.isLoaded()) || false, [Userback]);\n    const setName = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((name) => { Userback === null || Userback === void 0 ? void 0 : Userback.setName(name); }, [Userback]);\n    const setData = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((data) => { Userback === null || Userback === void 0 ? void 0 : Userback.setData(data); }, [Userback]);\n    const setEmail = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((email) => { Userback === null || Userback === void 0 ? void 0 : Userback.setEmail(email); }, [Userback]);\n    const setCategories = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((categories) => { Userback === null || Userback === void 0 ? void 0 : Userback.setCategories(categories); }, [Userback]);\n    const setPriority = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((priority) => { Userback === null || Userback === void 0 ? void 0 : Userback.setPriority(priority); }, [Userback]);\n    const addHeader = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key, value) => { Userback === null || Userback === void 0 ? void 0 : Userback.addHeader(key, value); }, [Userback]);\n    const open = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((feedback, destination) => {\n        Userback === null || Userback === void 0 ? void 0 : Userback.open(feedback, destination);\n    }, [Userback]);\n    const destroy = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n        Userback === null || Userback === void 0 ? void 0 : Userback.destroy();\n        setUserback(undefined);\n        ubLoaded.current = false;\n    }, [Userback]);\n    const identify = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((user_id, user_info) => Userback === null || Userback === void 0 ? void 0 : Userback.identify(user_id, user_info), [Userback]);\n    const openSurvey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((key) => Userback === null || Userback === void 0 ? void 0 : Userback.openSurvey(key), [Userback]);\n    const closeSurvey = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.closeSurvey(), [Userback]);\n    const refresh = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.refresh(), [Userback]);\n    const showLauncher = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.showLauncher(), [Userback]);\n    const hideLauncher = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.hideLauncher(), [Userback]);\n    const startSessionReplay = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((session_replay_option) => {\n        Userback === null || Userback === void 0 ? void 0 : Userback.startSessionReplay(session_replay_option);\n    }, [Userback]);\n    const stopSessionReplay = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => Userback === null || Userback === void 0 ? void 0 : Userback.stopSessionReplay(), [Userback]);\n    const addCustomEvent = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((event, data) => Userback === null || Userback === void 0 ? void 0 : Userback.addCustomEvent(event, data), [Userback]);\n    // Create the provider values, usable upstream by users\n    const providerValue = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(() => ({\n        init,\n        show,\n        hide,\n        open,\n        close,\n        destroy,\n        setData,\n        setEmail,\n        setCategories,\n        setPriority,\n        addHeader,\n        identify,\n        openPortal,\n        isLoaded,\n        setName,\n        openSurvey,\n        closeSurvey,\n        refresh,\n        showLauncher,\n        hideLauncher,\n        startSessionReplay,\n        stopSessionReplay,\n        addCustomEvent,\n    }), [\n        init,\n        show,\n        hide,\n        open,\n        close,\n        destroy,\n        setData,\n        setEmail,\n        setCategories,\n        setPriority,\n        addHeader,\n        identify,\n        openPortal,\n        isLoaded,\n        setName,\n        openSurvey,\n        closeSurvey,\n        refresh,\n        showLauncher,\n        hideLauncher,\n        startSessionReplay,\n        stopSessionReplay,\n        addCustomEvent,\n    ]);\n    return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(UserbackContext.Provider, { value: providerValue }, children));\n};\nconst useUserbackContext = () => {\n    const ctx = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(UserbackContext);\n    if (ctx === undefined) {\n        throw new Error('`useUserback` must be used within `UserbackProvider`.');\n    }\n    return ctx;\n};\n/* Provides the Userback api as React hooks */\nconst useUserback = () => useUserbackContext();\n/**\n * A higher Ordered Component for using hooks within a class based component\n * */\nfunction withUserback(Component) {\n    return function UserbackWrapper(props) {\n        const userback = useUserback();\n        // eslint-disable-next-line react/jsx-props-no-spreading\n        return (react__WEBPACK_IMPORTED_MODULE_0__.createElement(Component, Object.assign({}, props, { userback: userback })));\n    };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@userback+react@0.3.9_react_52a31c29d49fa6c88d0dd64f42a5c9d3/node_modules/@userback/react/dist/react.mjs\n");

/***/ })

};
;