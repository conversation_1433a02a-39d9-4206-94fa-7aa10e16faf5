"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-switch@1.2._2af24369488266db867d3fd6507506e1";
exports.ids = ["vendor-chunks/@radix-ui+react-switch@1.2._2af24369488266db867d3fd6507506e1"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.2._2af24369488266db867d3fd6507506e1/node_modules/@radix-ui/react-switch/dist/index.mjs":
/*!********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-switch@1.2._2af24369488266db867d3fd6507506e1/node_modules/@radix-ui/react-switch/dist/index.mjs ***!
  \********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Root: () => (/* binding */ Root),\n/* harmony export */   Switch: () => (/* binding */ Switch),\n/* harmony export */   SwitchThumb: () => (/* binding */ SwitchThumb),\n/* harmony export */   Thumb: () => (/* binding */ Thumb),\n/* harmony export */   createSwitchScope: () => (/* binding */ createSwitchScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-use-previous */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-previou_43bf2522b11b4054129913e6ef284501/node_modules/@radix-ui/react-use-previous/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/react-use-size */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-size@1._6892bc03897e7520d34e6acf45100b9a/node_modules/@radix-ui/react-use-size/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Root,Switch,SwitchThumb,Thumb,createSwitchScope auto */ // src/switch.tsx\n\n\n\n\n\n\n\n\n\nvar SWITCH_NAME = \"Switch\";\nvar [createSwitchContext, createSwitchScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(SWITCH_NAME);\nvar [SwitchProvider, useSwitchContext] = createSwitchContext(SWITCH_NAME);\nvar Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, name, checked: checkedProp, defaultChecked, required, disabled, value = \"on\", onCheckedChange, form, ...switchProps } = props;\n    const [button, setButton] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(forwardedRef, (node)=>setButton(node));\n    const hasConsumerStoppedPropagationRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const isFormControl = button ? form || !!button.closest(\"form\") : true;\n    const [checked, setChecked] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: checkedProp,\n        defaultProp: defaultChecked ?? false,\n        onChange: onCheckedChange,\n        caller: SWITCH_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(SwitchProvider, {\n        scope: __scopeSwitch,\n        checked,\n        disabled,\n        children: [\n            /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.button, {\n                type: \"button\",\n                role: \"switch\",\n                \"aria-checked\": checked,\n                \"aria-required\": required,\n                \"data-state\": getState(checked),\n                \"data-disabled\": disabled ? \"\" : void 0,\n                disabled,\n                value,\n                ...switchProps,\n                ref: composedRefs,\n                onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_6__.composeEventHandlers)(props.onClick, (event)=>{\n                    setChecked((prevChecked)=>!prevChecked);\n                    if (isFormControl) {\n                        hasConsumerStoppedPropagationRef.current = event.isPropagationStopped();\n                        if (!hasConsumerStoppedPropagationRef.current) event.stopPropagation();\n                    }\n                })\n            }),\n            isFormControl && /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(SwitchBubbleInput, {\n                control: button,\n                bubbles: !hasConsumerStoppedPropagationRef.current,\n                name,\n                value,\n                checked,\n                required,\n                disabled,\n                form,\n                style: {\n                    transform: \"translateX(-100%)\"\n                }\n            })\n        ]\n    });\n});\nSwitch.displayName = SWITCH_NAME;\nvar THUMB_NAME = \"SwitchThumb\";\nvar SwitchThumb = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopeSwitch, ...thumbProps } = props;\n    const context = useSwitchContext(THUMB_NAME, __scopeSwitch);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_5__.Primitive.span, {\n        \"data-state\": getState(context.checked),\n        \"data-disabled\": context.disabled ? \"\" : void 0,\n        ...thumbProps,\n        ref: forwardedRef\n    });\n});\nSwitchThumb.displayName = THUMB_NAME;\nvar BUBBLE_INPUT_NAME = \"SwitchBubbleInput\";\nvar SwitchBubbleInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(({ __scopeSwitch, control, checked, bubbles = true, ...props }, forwardedRef)=>{\n    const ref = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_3__.useComposedRefs)(ref, forwardedRef);\n    const prevChecked = (0,_radix_ui_react_use_previous__WEBPACK_IMPORTED_MODULE_7__.usePrevious)(checked);\n    const controlSize = (0,_radix_ui_react_use_size__WEBPACK_IMPORTED_MODULE_8__.useSize)(control);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const input = ref.current;\n        if (!input) return;\n        const inputProto = window.HTMLInputElement.prototype;\n        const descriptor = Object.getOwnPropertyDescriptor(inputProto, \"checked\");\n        const setChecked = descriptor.set;\n        if (prevChecked !== checked && setChecked) {\n            const event = new Event(\"click\", {\n                bubbles\n            });\n            setChecked.call(input, checked);\n            input.dispatchEvent(event);\n        }\n    }, [\n        prevChecked,\n        checked,\n        bubbles\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        type: \"checkbox\",\n        \"aria-hidden\": true,\n        defaultChecked: checked,\n        ...props,\n        tabIndex: -1,\n        ref: composedRefs,\n        style: {\n            ...props.style,\n            ...controlSize,\n            position: \"absolute\",\n            pointerEvents: \"none\",\n            opacity: 0,\n            margin: 0\n        }\n    });\n});\nSwitchBubbleInput.displayName = BUBBLE_INPUT_NAME;\nfunction getState(checked) {\n    return checked ? \"checked\" : \"unchecked\";\n}\nvar Root = Switch;\nvar Thumb = SwitchThumb;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-switch@1.2._2af24369488266db867d3fd6507506e1/node_modules/@radix-ui/react-switch/dist/index.mjs\n");

/***/ })

};
;