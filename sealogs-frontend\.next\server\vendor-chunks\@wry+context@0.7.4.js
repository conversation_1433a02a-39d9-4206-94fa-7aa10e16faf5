"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@wry+context@0.7.4";
exports.ids = ["vendor-chunks/@wry+context@0.7.4"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js ***!
  \**************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* reexport safe */ _slot_js__WEBPACK_IMPORTED_MODULE_0__.Slot),\n/* harmony export */   asyncFromGen: () => (/* binding */ asyncFromGen),\n/* harmony export */   bind: () => (/* binding */ bind),\n/* harmony export */   noContext: () => (/* binding */ noContext),\n/* harmony export */   setTimeout: () => (/* binding */ setTimeoutWithContext),\n/* harmony export */   wrapYieldingFiberMethods: () => (/* binding */ wrapYieldingFiberMethods)\n/* harmony export */ });\n/* harmony import */ var _slot_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./slot.js */ \"(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/slot.js\");\n\n\nconst { bind, noContext } = _slot_js__WEBPACK_IMPORTED_MODULE_0__.Slot;\n// Like global.setTimeout, except the callback runs with captured context.\n\nfunction setTimeoutWithContext(callback, delay) {\n    return setTimeout(bind(callback), delay);\n}\n// Turn any generator function into an async function (using yield instead\n// of await), with context automatically preserved across yields.\nfunction asyncFromGen(genFn) {\n    return function () {\n        const gen = genFn.apply(this, arguments);\n        const boundNext = bind(gen.next);\n        const boundThrow = bind(gen.throw);\n        return new Promise((resolve, reject) => {\n            function invoke(method, argument) {\n                try {\n                    var result = method.call(gen, argument);\n                }\n                catch (error) {\n                    return reject(error);\n                }\n                const next = result.done ? resolve : invokeNext;\n                if (isPromiseLike(result.value)) {\n                    result.value.then(next, result.done ? reject : invokeThrow);\n                }\n                else {\n                    next(result.value);\n                }\n            }\n            const invokeNext = (value) => invoke(boundNext, value);\n            const invokeThrow = (error) => invoke(boundThrow, error);\n            invokeNext();\n        });\n    };\n}\nfunction isPromiseLike(value) {\n    return value && typeof value.then === \"function\";\n}\n// If you use the fibers npm package to implement coroutines in Node.js,\n// you should call this function at least once to ensure context management\n// remains coherent across any yields.\nconst wrappedFibers = [];\nfunction wrapYieldingFiberMethods(Fiber) {\n    // There can be only one implementation of Fiber per process, so this array\n    // should never grow longer than one element.\n    if (wrappedFibers.indexOf(Fiber) < 0) {\n        const wrap = (obj, method) => {\n            const fn = obj[method];\n            obj[method] = function () {\n                return noContext(fn, arguments, this);\n            };\n        };\n        // These methods can yield, according to\n        // https://github.com/laverdet/node-fibers/blob/ddebed9b8ae3883e57f822e2108e6943e5c8d2a8/fibers.js#L97-L100\n        wrap(Fiber, \"yield\");\n        wrap(Fiber.prototype, \"run\");\n        wrap(Fiber.prototype, \"throwInto\");\n        wrappedFibers.push(Fiber);\n    }\n    return Fiber;\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdyeStjb250ZXh0QDAuNy40L25vZGVfbW9kdWxlcy9Ad3J5L2NvbnRleHQvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7QUFBaUM7QUFDakI7QUFDVCxRQUFRLGtCQUFrQixFQUFFLDBDQUFJO0FBQ3ZDO0FBQytDO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTO0FBQ1Q7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL3NlYWxvZ3MvLi9ub2RlX21vZHVsZXMvLnBucG0vQHdyeStjb250ZXh0QDAuNy40L25vZGVfbW9kdWxlcy9Ad3J5L2NvbnRleHQvbGliL2luZGV4LmpzPzA2ZGYiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgU2xvdCB9IGZyb20gXCIuL3Nsb3QuanNcIjtcbmV4cG9ydCB7IFNsb3QgfTtcbmV4cG9ydCBjb25zdCB7IGJpbmQsIG5vQ29udGV4dCB9ID0gU2xvdDtcbi8vIExpa2UgZ2xvYmFsLnNldFRpbWVvdXQsIGV4Y2VwdCB0aGUgY2FsbGJhY2sgcnVucyB3aXRoIGNhcHR1cmVkIGNvbnRleHQuXG5leHBvcnQgeyBzZXRUaW1lb3V0V2l0aENvbnRleHQgYXMgc2V0VGltZW91dCB9O1xuZnVuY3Rpb24gc2V0VGltZW91dFdpdGhDb250ZXh0KGNhbGxiYWNrLCBkZWxheSkge1xuICAgIHJldHVybiBzZXRUaW1lb3V0KGJpbmQoY2FsbGJhY2spLCBkZWxheSk7XG59XG4vLyBUdXJuIGFueSBnZW5lcmF0b3IgZnVuY3Rpb24gaW50byBhbiBhc3luYyBmdW5jdGlvbiAodXNpbmcgeWllbGQgaW5zdGVhZFxuLy8gb2YgYXdhaXQpLCB3aXRoIGNvbnRleHQgYXV0b21hdGljYWxseSBwcmVzZXJ2ZWQgYWNyb3NzIHlpZWxkcy5cbmV4cG9ydCBmdW5jdGlvbiBhc3luY0Zyb21HZW4oZ2VuRm4pIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKCkge1xuICAgICAgICBjb25zdCBnZW4gPSBnZW5Gbi5hcHBseSh0aGlzLCBhcmd1bWVudHMpO1xuICAgICAgICBjb25zdCBib3VuZE5leHQgPSBiaW5kKGdlbi5uZXh0KTtcbiAgICAgICAgY29uc3QgYm91bmRUaHJvdyA9IGJpbmQoZ2VuLnRocm93KTtcbiAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHtcbiAgICAgICAgICAgIGZ1bmN0aW9uIGludm9rZShtZXRob2QsIGFyZ3VtZW50KSB7XG4gICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgdmFyIHJlc3VsdCA9IG1ldGhvZC5jYWxsKGdlbiwgYXJndW1lbnQpO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBjYXRjaCAoZXJyb3IpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIHJlamVjdChlcnJvcik7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGNvbnN0IG5leHQgPSByZXN1bHQuZG9uZSA/IHJlc29sdmUgOiBpbnZva2VOZXh0O1xuICAgICAgICAgICAgICAgIGlmIChpc1Byb21pc2VMaWtlKHJlc3VsdC52YWx1ZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgcmVzdWx0LnZhbHVlLnRoZW4obmV4dCwgcmVzdWx0LmRvbmUgPyByZWplY3QgOiBpbnZva2VUaHJvdyk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIGVsc2Uge1xuICAgICAgICAgICAgICAgICAgICBuZXh0KHJlc3VsdC52YWx1ZSk7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgICAgY29uc3QgaW52b2tlTmV4dCA9ICh2YWx1ZSkgPT4gaW52b2tlKGJvdW5kTmV4dCwgdmFsdWUpO1xuICAgICAgICAgICAgY29uc3QgaW52b2tlVGhyb3cgPSAoZXJyb3IpID0+IGludm9rZShib3VuZFRocm93LCBlcnJvcik7XG4gICAgICAgICAgICBpbnZva2VOZXh0KCk7XG4gICAgICAgIH0pO1xuICAgIH07XG59XG5mdW5jdGlvbiBpc1Byb21pc2VMaWtlKHZhbHVlKSB7XG4gICAgcmV0dXJuIHZhbHVlICYmIHR5cGVvZiB2YWx1ZS50aGVuID09PSBcImZ1bmN0aW9uXCI7XG59XG4vLyBJZiB5b3UgdXNlIHRoZSBmaWJlcnMgbnBtIHBhY2thZ2UgdG8gaW1wbGVtZW50IGNvcm91dGluZXMgaW4gTm9kZS5qcyxcbi8vIHlvdSBzaG91bGQgY2FsbCB0aGlzIGZ1bmN0aW9uIGF0IGxlYXN0IG9uY2UgdG8gZW5zdXJlIGNvbnRleHQgbWFuYWdlbWVudFxuLy8gcmVtYWlucyBjb2hlcmVudCBhY3Jvc3MgYW55IHlpZWxkcy5cbmNvbnN0IHdyYXBwZWRGaWJlcnMgPSBbXTtcbmV4cG9ydCBmdW5jdGlvbiB3cmFwWWllbGRpbmdGaWJlck1ldGhvZHMoRmliZXIpIHtcbiAgICAvLyBUaGVyZSBjYW4gYmUgb25seSBvbmUgaW1wbGVtZW50YXRpb24gb2YgRmliZXIgcGVyIHByb2Nlc3MsIHNvIHRoaXMgYXJyYXlcbiAgICAvLyBzaG91bGQgbmV2ZXIgZ3JvdyBsb25nZXIgdGhhbiBvbmUgZWxlbWVudC5cbiAgICBpZiAod3JhcHBlZEZpYmVycy5pbmRleE9mKEZpYmVyKSA8IDApIHtcbiAgICAgICAgY29uc3Qgd3JhcCA9IChvYmosIG1ldGhvZCkgPT4ge1xuICAgICAgICAgICAgY29uc3QgZm4gPSBvYmpbbWV0aG9kXTtcbiAgICAgICAgICAgIG9ialttZXRob2RdID0gZnVuY3Rpb24gKCkge1xuICAgICAgICAgICAgICAgIHJldHVybiBub0NvbnRleHQoZm4sIGFyZ3VtZW50cywgdGhpcyk7XG4gICAgICAgICAgICB9O1xuICAgICAgICB9O1xuICAgICAgICAvLyBUaGVzZSBtZXRob2RzIGNhbiB5aWVsZCwgYWNjb3JkaW5nIHRvXG4gICAgICAgIC8vIGh0dHBzOi8vZ2l0aHViLmNvbS9sYXZlcmRldC9ub2RlLWZpYmVycy9ibG9iL2RkZWJlZDliOGFlMzg4M2U1N2Y4MjJlMjEwOGU2OTQzZTVjOGQyYTgvZmliZXJzLmpzI0w5Ny1MMTAwXG4gICAgICAgIHdyYXAoRmliZXIsIFwieWllbGRcIik7XG4gICAgICAgIHdyYXAoRmliZXIucHJvdG90eXBlLCBcInJ1blwiKTtcbiAgICAgICAgd3JhcChGaWJlci5wcm90b3R5cGUsIFwidGhyb3dJbnRvXCIpO1xuICAgICAgICB3cmFwcGVkRmliZXJzLnB1c2goRmliZXIpO1xuICAgIH1cbiAgICByZXR1cm4gRmliZXI7XG59XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/slot.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/slot.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slot: () => (/* binding */ Slot)\n/* harmony export */ });\n// This currentContext variable will only be used if the makeSlotClass\n// function is called, which happens only if this is the first copy of the\n// @wry/context package to be imported.\nlet currentContext = null;\n// This unique internal object is used to denote the absence of a value\n// for a given Slot, and is never exposed to outside code.\nconst MISSING_VALUE = {};\nlet idCounter = 1;\n// Although we can't do anything about the cost of duplicated code from\n// accidentally bundling multiple copies of the @wry/context package, we can\n// avoid creating the Slot class more than once using makeSlotClass.\nconst makeSlotClass = () => class Slot {\n    constructor() {\n        // If you have a Slot object, you can find out its slot.id, but you cannot\n        // guess the slot.id of a Slot you don't have access to, thanks to the\n        // randomized suffix.\n        this.id = [\n            \"slot\",\n            idCounter++,\n            Date.now(),\n            Math.random().toString(36).slice(2),\n        ].join(\":\");\n    }\n    hasValue() {\n        for (let context = currentContext; context; context = context.parent) {\n            // We use the Slot object iself as a key to its value, which means the\n            // value cannot be obtained without a reference to the Slot object.\n            if (this.id in context.slots) {\n                const value = context.slots[this.id];\n                if (value === MISSING_VALUE)\n                    break;\n                if (context !== currentContext) {\n                    // Cache the value in currentContext.slots so the next lookup will\n                    // be faster. This caching is safe because the tree of contexts and\n                    // the values of the slots are logically immutable.\n                    currentContext.slots[this.id] = value;\n                }\n                return true;\n            }\n        }\n        if (currentContext) {\n            // If a value was not found for this Slot, it's never going to be found\n            // no matter how many times we look it up, so we might as well cache\n            // the absence of the value, too.\n            currentContext.slots[this.id] = MISSING_VALUE;\n        }\n        return false;\n    }\n    getValue() {\n        if (this.hasValue()) {\n            return currentContext.slots[this.id];\n        }\n    }\n    withValue(value, callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        const slots = {\n            __proto__: null,\n            [this.id]: value,\n        };\n        const parent = currentContext;\n        currentContext = { parent, slots };\n        try {\n            // Function.prototype.apply allows the arguments array argument to be\n            // omitted or undefined, so args! is fine here.\n            return callback.apply(thisArg, args);\n        }\n        finally {\n            currentContext = parent;\n        }\n    }\n    // Capture the current context and wrap a callback function so that it\n    // reestablishes the captured context when called.\n    static bind(callback) {\n        const context = currentContext;\n        return function () {\n            const saved = currentContext;\n            try {\n                currentContext = context;\n                return callback.apply(this, arguments);\n            }\n            finally {\n                currentContext = saved;\n            }\n        };\n    }\n    // Immediately run a callback function without any captured context.\n    static noContext(callback, \n    // Given the prevalence of arrow functions, specifying arguments is likely\n    // to be much more common than specifying `this`, hence this ordering:\n    args, thisArg) {\n        if (currentContext) {\n            const saved = currentContext;\n            try {\n                currentContext = null;\n                // Function.prototype.apply allows the arguments array argument to be\n                // omitted or undefined, so args! is fine here.\n                return callback.apply(thisArg, args);\n            }\n            finally {\n                currentContext = saved;\n            }\n        }\n        else {\n            return callback.apply(thisArg, args);\n        }\n    }\n};\nfunction maybe(fn) {\n    try {\n        return fn();\n    }\n    catch (ignored) { }\n}\n// We store a single global implementation of the Slot class as a permanent\n// non-enumerable property of the globalThis object. This obfuscation does\n// nothing to prevent access to the Slot class, but at least it ensures the\n// implementation (i.e. currentContext) cannot be tampered with, and all copies\n// of the @wry/context package (hopefully just one) will share the same Slot\n// implementation. Since the first copy of the @wry/context package to be\n// imported wins, this technique imposes a steep cost for any future breaking\n// changes to the Slot class.\nconst globalKey = \"@wry/context:Slot\";\nconst host = \n// Prefer globalThis when available.\n// https://github.com/benjamn/wryware/issues/347\nmaybe(() => globalThis) ||\n    // Fall back to global, which works in Node.js and may be converted by some\n    // bundlers to the appropriate identifier (window, self, ...) depending on the\n    // bundling target. https://github.com/endojs/endo/issues/576#issuecomment-1178515224\n    maybe(() => global) ||\n    // Otherwise, use a dummy host that's local to this module. We used to fall\n    // back to using the Array constructor as a namespace, but that was flagged in\n    // https://github.com/benjamn/wryware/issues/347, and can be avoided.\n    Object.create(null);\n// Whichever globalHost we're using, make TypeScript happy about the additional\n// globalKey property.\nconst globalHost = host;\nconst Slot = globalHost[globalKey] ||\n    // Earlier versions of this package stored the globalKey property on the Array\n    // constructor, so we check there as well, to prevent Slot class duplication.\n    Array[globalKey] ||\n    (function (Slot) {\n        try {\n            Object.defineProperty(globalHost, globalKey, {\n                value: Slot,\n                enumerable: false,\n                writable: false,\n                // When it was possible for globalHost to be the Array constructor (a\n                // legacy Slot dedup strategy), it was important for the property to be\n                // configurable:true so it could be deleted. That does not seem to be as\n                // important when globalHost is the global object, but I don't want to\n                // cause similar problems again, and configurable:true seems safest.\n                // https://github.com/endojs/endo/issues/576#issuecomment-1178274008\n                configurable: true\n            });\n        }\n        finally {\n            return Slot;\n        }\n    })(makeSlotClass());\n//# sourceMappingURL=slot.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@wry+context@0.7.4/node_modules/@wry/context/lib/slot.js\n");

/***/ })

};
;