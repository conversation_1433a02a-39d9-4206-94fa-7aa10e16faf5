"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@radix-ui+react-popover@1.1_58d720829f82e8d062ec070fb7840cdf";
exports.ids = ["vendor-chunks/@radix-ui+react-popover@1.1_58d720829f82e8d062ec070fb7840cdf"];
exports.modules = {

/***/ "(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1_58d720829f82e8d062ec070fb7840cdf/node_modules/@radix-ui/react-popover/dist/index.mjs":
/*!*********************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/@radix-ui+react-popover@1.1_58d720829f82e8d062ec070fb7840cdf/node_modules/@radix-ui/react-popover/dist/index.mjs ***!
  \*********************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Anchor: () => (/* binding */ Anchor2),\n/* harmony export */   Arrow: () => (/* binding */ Arrow2),\n/* harmony export */   Close: () => (/* binding */ Close),\n/* harmony export */   Content: () => (/* binding */ Content2),\n/* harmony export */   Popover: () => (/* binding */ Popover),\n/* harmony export */   PopoverAnchor: () => (/* binding */ PopoverAnchor),\n/* harmony export */   PopoverArrow: () => (/* binding */ PopoverArrow),\n/* harmony export */   PopoverClose: () => (/* binding */ PopoverClose),\n/* harmony export */   PopoverContent: () => (/* binding */ PopoverContent),\n/* harmony export */   PopoverPortal: () => (/* binding */ PopoverPortal),\n/* harmony export */   PopoverTrigger: () => (/* binding */ PopoverTrigger),\n/* harmony export */   Portal: () => (/* binding */ Portal),\n/* harmony export */   Root: () => (/* binding */ Root2),\n/* harmony export */   Trigger: () => (/* binding */ Trigger),\n/* harmony export */   createPopoverScope: () => (/* binding */ createPopoverScope)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @radix-ui/primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+primitive@1.1.2/node_modules/@radix-ui/primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @radix-ui/react-compose-refs */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-compose-ref_a0ec2738abda304f97df2634b41c8bcd/node_modules/@radix-ui/react-compose-refs/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @radix-ui/react-context */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-context@1.1_76d388bc9b59462d990d0dffb9f0fdd3/node_modules/@radix-ui/react-context/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @radix-ui/react-dismissable-layer */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-dismissable_dbf8386523191e50867cd199de52aa0e/node_modules/@radix-ui/react-dismissable-layer/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @radix-ui/react-focus-guards */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-guard_172eed378379c5fa3b568ee810e2dcd9/node_modules/@radix-ui/react-focus-guards/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @radix-ui/react-focus-scope */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-focus-scope_f761c2ff2dd8a5cebf4e03dd795af57f/node_modules/@radix-ui/react-focus-scope/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @radix-ui/react-id */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-id@1.1.1_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-id/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-popper */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-popper@1.2._ffa2341e59ce9c78f0d0d849ccd75e57/node_modules/@radix-ui/react-popper/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @radix-ui/react-portal */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-portal@1.1._6c1cd0a6f7cc4779efee75f9fbbe7053/node_modules/@radix-ui/react-portal/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @radix-ui/react-presence */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-presence@1._587c7e8c3eecba09139e2afe2a783727/node_modules/@radix-ui/react-presence/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @radix-ui/react-primitive */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-primitive@2_db0ee435667e42f4b05fd5a9bb21abc3/node_modules/@radix-ui/react-primitive/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-slot@1.2.3_@types+react@18.3.23_react@18.3.1/node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var _radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-use-controllable-state */ \"(ssr)/./node_modules/.pnpm/@radix-ui+react-use-control_52ef77ca249c22a1fbb1133c7238e331/node_modules/@radix-ui/react-use-controllable-state/dist/index.mjs\");\n/* harmony import */ var aria_hidden__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! aria-hidden */ \"(ssr)/./node_modules/.pnpm/aria-hidden@1.2.6/node_modules/aria-hidden/dist/es2015/index.js\");\n/* harmony import */ var react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! react-remove-scroll */ \"(ssr)/./node_modules/.pnpm/react-remove-scroll@2.7.1_@types+react@18.3.23_react@18.3.1/node_modules/react-remove-scroll/dist/es2015/Combination.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* __next_internal_client_entry_do_not_use__ Anchor,Arrow,Close,Content,Popover,PopoverAnchor,PopoverArrow,PopoverClose,PopoverContent,PopoverPortal,PopoverTrigger,Portal,Root,Trigger,createPopoverScope auto */ // src/popover.tsx\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar POPOVER_NAME = \"Popover\";\nvar [createPopoverContext, createPopoverScope] = (0,_radix_ui_react_context__WEBPACK_IMPORTED_MODULE_2__.createContextScope)(POPOVER_NAME, [\n    _radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope\n]);\nvar usePopperScope = (0,_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.createPopperScope)();\nvar [PopoverProvider, usePopoverContext] = createPopoverContext(POPOVER_NAME);\nvar Popover = (props)=>{\n    const { __scopePopover, children, open: openProp, defaultOpen, onOpenChange, modal = false } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    const triggerRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [hasCustomAnchor, setHasCustomAnchor] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [open, setOpen] = (0,_radix_ui_react_use_controllable_state__WEBPACK_IMPORTED_MODULE_4__.useControllableState)({\n        prop: openProp,\n        defaultProp: defaultOpen ?? false,\n        onChange: onOpenChange,\n        caller: POPOVER_NAME\n    });\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ...popperScope,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverProvider, {\n            scope: __scopePopover,\n            contentId: (0,_radix_ui_react_id__WEBPACK_IMPORTED_MODULE_5__.useId)(),\n            triggerRef,\n            open,\n            onOpenChange: setOpen,\n            onOpenToggle: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setOpen((prevOpen)=>!prevOpen), [\n                setOpen\n            ]),\n            hasCustomAnchor,\n            onCustomAnchorAdd: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(true), []),\n            onCustomAnchorRemove: react__WEBPACK_IMPORTED_MODULE_0__.useCallback(()=>setHasCustomAnchor(false), []),\n            modal,\n            children\n        })\n    });\n};\nPopover.displayName = POPOVER_NAME;\nvar ANCHOR_NAME = \"PopoverAnchor\";\nvar PopoverAnchor = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...anchorProps } = props;\n    const context = usePopoverContext(ANCHOR_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const { onCustomAnchorAdd, onCustomAnchorRemove } = context;\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        onCustomAnchorAdd();\n        return ()=>onCustomAnchorRemove();\n    }, [\n        onCustomAnchorAdd,\n        onCustomAnchorRemove\n    ]);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        ...popperScope,\n        ...anchorProps,\n        ref: forwardedRef\n    });\n});\nPopoverAnchor.displayName = ANCHOR_NAME;\nvar TRIGGER_NAME = \"PopoverTrigger\";\nvar PopoverTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...triggerProps } = props;\n    const context = usePopoverContext(TRIGGER_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    const composedTriggerRef = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, context.triggerRef);\n    const trigger = /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        \"aria-haspopup\": \"dialog\",\n        \"aria-expanded\": context.open,\n        \"aria-controls\": context.contentId,\n        \"data-state\": getState(context.open),\n        ...triggerProps,\n        ref: composedTriggerRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, context.onOpenToggle)\n    });\n    return context.hasCustomAnchor ? trigger : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Anchor, {\n        asChild: true,\n        ...popperScope,\n        children: trigger\n    });\n});\nPopoverTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"PopoverPortal\";\nvar [PortalProvider, usePortalContext] = createPopoverContext(PORTAL_NAME, {\n    forceMount: void 0\n});\nvar PopoverPortal = (props)=>{\n    const { __scopePopover, forceMount, children, container } = props;\n    const context = usePopoverContext(PORTAL_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PortalProvider, {\n        scope: __scopePopover,\n        forceMount,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n            present: forceMount || context.open,\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_portal__WEBPACK_IMPORTED_MODULE_10__.Portal, {\n                asChild: true,\n                container,\n                children\n            })\n        })\n    });\n};\nPopoverPortal.displayName = PORTAL_NAME;\nvar CONTENT_NAME = \"PopoverContent\";\nvar PopoverContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const portalContext = usePortalContext(CONTENT_NAME, props.__scopePopover);\n    const { forceMount = portalContext.forceMount, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_presence__WEBPACK_IMPORTED_MODULE_9__.Presence, {\n        present: forceMount || context.open,\n        children: context.modal ? /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentModal, {\n            ...contentProps,\n            ref: forwardedRef\n        }) : /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentNonModal, {\n            ...contentProps,\n            ref: forwardedRef\n        })\n    });\n});\nPopoverContent.displayName = CONTENT_NAME;\nvar Slot = (0,_radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_11__.createSlot)(\"PopoverContent.RemoveScroll\");\nvar PopoverContentModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const contentRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const composedRefs = (0,_radix_ui_react_compose_refs__WEBPACK_IMPORTED_MODULE_6__.useComposedRefs)(forwardedRef, contentRef);\n    const isRightClickOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        const content = contentRef.current;\n        if (content) return (0,aria_hidden__WEBPACK_IMPORTED_MODULE_12__.hideOthers)(content);\n    }, []);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(react_remove_scroll__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n        as: Slot,\n        allowPinchZoom: true,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n            ...props,\n            ref: composedRefs,\n            trapFocus: context.open,\n            disableOutsidePointerEvents: true,\n            onCloseAutoFocus: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onCloseAutoFocus, (event)=>{\n                event.preventDefault();\n                if (!isRightClickOutsideRef.current) context.triggerRef.current?.focus();\n            }),\n            onPointerDownOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onPointerDownOutside, (event)=>{\n                const originalEvent = event.detail.originalEvent;\n                const ctrlLeftClick = originalEvent.button === 0 && originalEvent.ctrlKey === true;\n                const isRightClick = originalEvent.button === 2 || ctrlLeftClick;\n                isRightClickOutsideRef.current = isRightClick;\n            }, {\n                checkForDefaultPrevented: false\n            }),\n            onFocusOutside: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onFocusOutside, (event)=>event.preventDefault(), {\n                checkForDefaultPrevented: false\n            })\n        })\n    });\n});\nvar PopoverContentNonModal = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const context = usePopoverContext(CONTENT_NAME, props.__scopePopover);\n    const hasInteractedOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const hasPointerDownOutsideRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(PopoverContentImpl, {\n        ...props,\n        ref: forwardedRef,\n        trapFocus: false,\n        disableOutsidePointerEvents: false,\n        onCloseAutoFocus: (event)=>{\n            props.onCloseAutoFocus?.(event);\n            if (!event.defaultPrevented) {\n                if (!hasInteractedOutsideRef.current) context.triggerRef.current?.focus();\n                event.preventDefault();\n            }\n            hasInteractedOutsideRef.current = false;\n            hasPointerDownOutsideRef.current = false;\n        },\n        onInteractOutside: (event)=>{\n            props.onInteractOutside?.(event);\n            if (!event.defaultPrevented) {\n                hasInteractedOutsideRef.current = true;\n                if (event.detail.originalEvent.type === \"pointerdown\") {\n                    hasPointerDownOutsideRef.current = true;\n                }\n            }\n            const target = event.target;\n            const targetIsTrigger = context.triggerRef.current?.contains(target);\n            if (targetIsTrigger) event.preventDefault();\n            if (event.detail.originalEvent.type === \"focusin\" && hasPointerDownOutsideRef.current) {\n                event.preventDefault();\n            }\n        }\n    });\n});\nvar PopoverContentImpl = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, trapFocus, onOpenAutoFocus, onCloseAutoFocus, disableOutsidePointerEvents, onEscapeKeyDown, onPointerDownOutside, onFocusOutside, onInteractOutside, ...contentProps } = props;\n    const context = usePopoverContext(CONTENT_NAME, __scopePopover);\n    const popperScope = usePopperScope(__scopePopover);\n    (0,_radix_ui_react_focus_guards__WEBPACK_IMPORTED_MODULE_14__.useFocusGuards)();\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_focus_scope__WEBPACK_IMPORTED_MODULE_15__.FocusScope, {\n        asChild: true,\n        loop: true,\n        trapped: trapFocus,\n        onMountAutoFocus: onOpenAutoFocus,\n        onUnmountAutoFocus: onCloseAutoFocus,\n        children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_dismissable_layer__WEBPACK_IMPORTED_MODULE_16__.DismissableLayer, {\n            asChild: true,\n            disableOutsidePointerEvents,\n            onInteractOutside,\n            onEscapeKeyDown,\n            onPointerDownOutside,\n            onFocusOutside,\n            onDismiss: ()=>context.onOpenChange(false),\n            children: /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Content, {\n                \"data-state\": getState(context.open),\n                role: \"dialog\",\n                id: context.contentId,\n                ...popperScope,\n                ...contentProps,\n                ref: forwardedRef,\n                style: {\n                    ...contentProps.style,\n                    // re-namespace exposed content custom properties\n                    ...{\n                        \"--radix-popover-content-transform-origin\": \"var(--radix-popper-transform-origin)\",\n                        \"--radix-popover-content-available-width\": \"var(--radix-popper-available-width)\",\n                        \"--radix-popover-content-available-height\": \"var(--radix-popper-available-height)\",\n                        \"--radix-popover-trigger-width\": \"var(--radix-popper-anchor-width)\",\n                        \"--radix-popover-trigger-height\": \"var(--radix-popper-anchor-height)\"\n                    }\n                }\n            })\n        })\n    });\n});\nvar CLOSE_NAME = \"PopoverClose\";\nvar PopoverClose = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...closeProps } = props;\n    const context = usePopoverContext(CLOSE_NAME, __scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_primitive__WEBPACK_IMPORTED_MODULE_7__.Primitive.button, {\n        type: \"button\",\n        ...closeProps,\n        ref: forwardedRef,\n        onClick: (0,_radix_ui_primitive__WEBPACK_IMPORTED_MODULE_8__.composeEventHandlers)(props.onClick, ()=>context.onOpenChange(false))\n    });\n});\nPopoverClose.displayName = CLOSE_NAME;\nvar ARROW_NAME = \"PopoverArrow\";\nvar PopoverArrow = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef((props, forwardedRef)=>{\n    const { __scopePopover, ...arrowProps } = props;\n    const popperScope = usePopperScope(__scopePopover);\n    return /* @__PURE__ */ (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_radix_ui_react_popper__WEBPACK_IMPORTED_MODULE_3__.Arrow, {\n        ...popperScope,\n        ...arrowProps,\n        ref: forwardedRef\n    });\n});\nPopoverArrow.displayName = ARROW_NAME;\nfunction getState(open) {\n    return open ? \"open\" : \"closed\";\n}\nvar Root2 = Popover;\nvar Anchor2 = PopoverAnchor;\nvar Trigger = PopoverTrigger;\nvar Portal = PopoverPortal;\nvar Content2 = PopoverContent;\nvar Close = PopoverClose;\nvar Arrow2 = PopoverArrow;\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/@radix-ui+react-popover@1.1_58d720829f82e8d062ec070fb7840cdf/node_modules/@radix-ui/react-popover/dist/index.mjs\n");

/***/ })

};
;