"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/simple-fuel-report/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx":
/*!*****************************************************!*\
  !*** ./src/app/ui/reporting/simple-fuel-report.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ SimpleFuelReport; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/index.ts\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/sidebar */ \"(app-pages-browser)/./src/components/ui/sidebar.tsx\");\n/* harmony import */ var _app_lib_icons__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/app/lib/icons */ \"(app-pages-browser)/./src/app/lib/icons/index.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Create columns for the fuel report table\nconst createFuelReportColumns = ()=>(0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.createColumns)([\n        {\n            accessorKey: \"vesselName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Vessel\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"font-medium\",\n                    children: item.vesselName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"logbookDate\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Log Entry\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/M/YY\")\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 71,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelTankName\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Tank\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelTankName\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelStart\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Start\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelStart.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelAdded\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Added\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelAdded.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 107,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelEnd\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel End\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelEnd.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"fuelUsed\",\n            header: (param)=>{\n                let { column } = param;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_9__.DataTableSortHeader, {\n                    column: column,\n                    title: \"Fuel Used\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 125,\n                    columnNumber: 17\n                }, undefined);\n            },\n            cellAlignment: \"right\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.fuelUsed.toLocaleString()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 24\n                }, undefined);\n            }\n        },\n        {\n            accessorKey: \"comments\",\n            header: \"Comments\",\n            cellAlignment: \"left\",\n            cellClassName: \"px-2.5\",\n            cell: (param)=>{\n                let { row } = param;\n                const item = row.original;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    children: item.comments || \"-\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 141,\n                    columnNumber: 24\n                }, undefined);\n            }\n        }\n    ]);\n// Custom dropdown actions component for the fuel report\nfunction FuelReportFilterActions(param) {\n    let { onDownloadCsv, onDownloadPdf } = param;\n    _s();\n    const { isMobile } = (0,_components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                asChild: true,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_lib_icons__WEBPACK_IMPORTED_MODULE_13__.SealogsCogIcon, {\n                    size: 36\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 160,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 159,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                side: isMobile ? \"bottom\" : \"right\",\n                align: isMobile ? \"end\" : \"start\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-input flex flex-col items-center justify-center py-[9px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            variant: \"backButton\",\n                            onClick: ()=>router.push(\"/reporting\"),\n                            children: \"Back\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadPdf,\n                            children: \"Download PDF\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 171,\n                            columnNumber: 21\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                            className: \"px-[26px]\",\n                            onClick: onDownloadCsv,\n                            children: \"Download CSV\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                            lineNumber: 176,\n                            columnNumber: 21\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 165,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 162,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 158,\n        columnNumber: 9\n    }, this);\n}\n_s(FuelReportFilterActions, \"2jIoXD9G8OZZK/Hd0W/SlED0TzQ=\", false, function() {\n    return [\n        _components_ui_sidebar__WEBPACK_IMPORTED_MODULE_12__.useSidebar,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter\n    ];\n});\n_c = FuelReportFilterActions;\nfunction SimpleFuelReport() {\n    _s1();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    // Create columns for the table\n    const columns = createFuelReportColumns();\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"dateRange\":\n                setDateRange(data);\n                break;\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query_reporting__WEBPACK_IMPORTED_MODULE_2__.GET_SIMPLE_FUEL_REPORT_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"queryLogBookEntrySections error\", error);\n        }\n    });\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange.startDate !== null && dateRange.endDate !== null) {\n            filter[\"startDate\"] = {\n                gte: dateRange.startDate,\n                lte: dateRange.endDate\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"vehicleID\"] = {\n                in: selectedVessels.map((v)=>v.value)\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readLogBookEntries;\n        var _data_readLogBookEntries_nodes;\n        const fetchedData = (_data_readLogBookEntries_nodes = data === null || data === void 0 ? void 0 : (_data_readLogBookEntries = data.readLogBookEntries) === null || _data_readLogBookEntries === void 0 ? void 0 : _data_readLogBookEntries.nodes) !== null && _data_readLogBookEntries_nodes !== void 0 ? _data_readLogBookEntries_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const filteredItems = fetchedData.filter(function(item) {\n            return item.fuelLog.nodes.length > 0 && item.vehicle.id !== \"0\";\n        });\n        if (filteredItems.length === 0) {\n            return [];\n        }\n        const items = [];\n        filteredItems.forEach((item)=>{\n            if (item.state !== \"Locked\") {\n                return;\n            }\n            const logBookDate = new Date(item.startDate);\n            const vessel = item.vehicle;\n            const fuelLogs = item.fuelLog.nodes.filter((item)=>item.fuelTank.id != 0);\n            const fuelTanks = fuelLogs.reduce((acc, log)=>{\n                return {\n                    ...acc,\n                    [log.fuelTank.id]: log.fuelTank.title\n                };\n            }, {});\n            const logBookEntrySections = item.logBookEntrySections.nodes;\n            const tripEvents = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.tripEvents.nodes\n                ];\n            }, []);\n            const sectionMemberComments = logBookEntrySections.reduce((acc, section)=>{\n                return [\n                    ...acc,\n                    ...section.sectionMemberComments.nodes\n                ];\n            }, []).map((sectionComment)=>sectionComment === null || sectionComment === void 0 ? void 0 : sectionComment.comment).filter((value)=>value != null || value != \"\");\n            for(const key in fuelTanks){\n                if (Object.prototype.hasOwnProperty.call(fuelTanks, key)) {\n                    const fuelTankName = fuelTanks[key];\n                    const fuelTankLogs = fuelLogs.filter((item)=>item.fuelTankID == key);\n                    const fuelLogStart = fuelTankLogs[0];\n                    const fuelLogEnd = fuelTankLogs[fuelTankLogs.length - 1];\n                    var _fuelLogStart_fuelBefore;\n                    const fuelStart = (_fuelLogStart_fuelBefore = fuelLogStart === null || fuelLogStart === void 0 ? void 0 : fuelLogStart.fuelBefore) !== null && _fuelLogStart_fuelBefore !== void 0 ? _fuelLogStart_fuelBefore : 0;\n                    const fuelAdded = calculateFuelAddedFromTripEvents(tripEvents, key);\n                    var _fuelLogEnd_fuelAfter;\n                    const fuelEnd = (_fuelLogEnd_fuelAfter = fuelLogEnd === null || fuelLogEnd === void 0 ? void 0 : fuelLogEnd.fuelAfter) !== null && _fuelLogEnd_fuelAfter !== void 0 ? _fuelLogEnd_fuelAfter : 0;\n                    const fuelUsed = fuelStart + fuelAdded - fuelEnd;\n                    const reportItem = {\n                        logBookEntryID: item.id,\n                        vesselID: vessel.id,\n                        logbookDate: logBookDate,\n                        vesselName: vessel.title,\n                        fuelTankID: Number(key),\n                        fuelTankName: fuelTankName,\n                        fuelStart,\n                        fuelAdded,\n                        fuelEnd,\n                        fuelUsed,\n                        comments: sectionMemberComments.join(\", \")\n                    };\n                    items.push(reportItem);\n                }\n            }\n        });\n        return items;\n    }, [\n        data,\n        called,\n        loading\n    ]);\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [];\n        csvEntries.push([\n            \"vessel\",\n            \"log entry\",\n            \"fuel tank\",\n            \"fuel start\",\n            \"fuel added\",\n            \"fuel end\",\n            \"fuel used\",\n            \"comments\"\n        ]);\n        reportData.forEach((item)=>{\n            var _item_comments;\n            csvEntries.push([\n                item.vesselName,\n                item.logbookDate.toISOString(),\n                item.fuelTankName,\n                item.fuelStart,\n                item.fuelAdded,\n                item.fuelEnd,\n                item.fuelUsed,\n                (_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\"\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_4__.exportCsv)(csvEntries);\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const data = reportData.map(function(item) {\n            var _item_comments;\n            return [\n                item.vesselName + \"\",\n                dayjs__WEBPACK_IMPORTED_MODULE_3___default()(item.logbookDate).format(\"DD/MM/YY\") + \"\",\n                item.fuelTankName + \"\",\n                item.fuelStart.toLocaleString(),\n                item.fuelAdded.toLocaleString(),\n                item.fuelEnd.toLocaleString(),\n                item.fuelUsed.toLocaleString(),\n                \"\".concat((_item_comments = item.comments) !== null && _item_comments !== void 0 ? _item_comments : \"\", \" \")\n            ];\n        });\n        const totalUsedFuel = reportData.reduce((acc, current)=>acc + current.fuelUsed, 0);\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_5__.exportPdfTable)({\n            headers: [\n                [\n                    {\n                        content: \"Vessel\"\n                    },\n                    {\n                        content: \"Log Entry\"\n                    },\n                    {\n                        content: \"Fuel Tank\"\n                    },\n                    {\n                        content: \"Fuel Start\"\n                    },\n                    {\n                        content: \"Fuel Added\"\n                    },\n                    {\n                        content: \"Fuel End\"\n                    },\n                    {\n                        content: \"Fuel Used\"\n                    },\n                    {\n                        content: \"Comments\",\n                        styles: {\n                            cellWidth: 60\n                        }\n                    }\n                ]\n            ],\n            body: data,\n            footers: [\n                [\n                    {\n                        colSpan: 6,\n                        content: \"Total Fuel Used\"\n                    },\n                    {\n                        content: totalUsedFuel.toLocaleString()\n                    },\n                    {\n                        content: \"\"\n                    }\n                ]\n            ],\n            userOptions: {\n                showFoot: \"lastPage\"\n            }\n        });\n    };\n    // Calculate total fuel used for footer\n    const totalFuelUsed = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return reportData.reduce((acc, current)=>current.fuelUsed + acc, 0);\n    }, [\n        reportData\n    ]);\n    // Create footer content for the table\n    const footerContent = reportData.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableRow, {\n        className: \"group border-b\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-left font-medium\",\n                colSpan: 6,\n                children: \"Total Fuel Used\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 442,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3 text-right font-medium\",\n                children: totalFuelUsed.toLocaleString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 447,\n                columnNumber: 17\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_10__.TableCell, {\n                className: \"px-2.5 py-3\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 450,\n                columnNumber: 17\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n        lineNumber: 441,\n        columnNumber: 13\n    }, this) : null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_7__.ListHeader, {\n                title: \"Simple Fuel Report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(FuelReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 459,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 456,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-16\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_8__.DataTable, {\n                    columns: columns,\n                    data: reportData,\n                    isLoading: called && loading,\n                    onChange: handleFilterOnChange,\n                    onFilterClick: generateReport,\n                    noDataText: \"No fuel data found, try clicking generate report to view results\",\n                    showToolbar: true,\n                    footer: footerContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                    lineNumber: 466,\n                    columnNumber: 17\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\simple-fuel-report.tsx\",\n                lineNumber: 465,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s1(SimpleFuelReport, \"FjUG+MWP+kYXAKFh5HOCMHzr5ag=\", false, function() {\n    return [\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c1 = SimpleFuelReport;\nfunction getFuelAddedByFuelTankID(fuelLogs, fuelTankID) {\n    if (fuelLogs.length === 0) {\n        return 0;\n    }\n    const fuelTankLogs = fuelLogs.filter((log)=>log.fuelTankID == fuelTankID);\n    return fuelTankLogs.reduce((acc, log)=>acc + log.fuelAdded, 0);\n}\nfunction calculateFuelAddedFromTripEvents(tripEvents, fuelTankID) {\n    const fuelAddedLogs = tripEvents.map(function(tripEvent) {\n        if (tripEvent.eventCategory === \"RefuellingBunkering\") {\n            const fuelLogs = tripEvent.eventType_RefuellingBunkering.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"Tasking\") {\n            const fuelLogs = tripEvent.eventType_Tasking.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        if (tripEvent.eventCategory === \"PassengerDropFacility\") {\n            const fuelLogs = tripEvent.eventType_PassengerDropFacility.fuelLog.nodes;\n            return getFuelAddedByFuelTankID(fuelLogs, fuelTankID);\n        }\n        return 0;\n    });\n    return fuelAddedLogs.reduce((acc, val)=>acc + val, 0);\n}\nvar _c, _c1;\n$RefreshReg$(_c, \"FuelReportFilterActions\");\n$RefreshReg$(_c1, \"SimpleFuelReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/simple-fuel-report.tsx\n"));

/***/ })

});